name: ye<PERSON><PERSON><PERSON><PERSON>da
description: <PERSON><PERSON><PERSON>ımda Uygulaması
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 2.11.0+17

environment:
  sdk: '>=3.0.5 <4.0.0'
dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.5
  vector_math: ^2.1.4
  intl_phone_number_input: ^0.7.3+1
  pinput: ^5.0.0
  cupertino_icons: ^1.0.2
  stylish_bottom_bar: ^1.0.3
  carousel_slider: ^4.2.1
  dropdown_button2:
  image_picker: ^1.0.0
  flutter_rating_bar: ^4.0.1
  flutter_switch: ^0.3.2
  url_launcher: ^6.1.11
  timeline_list: ^0.0.6
  shared_preferences: ^2.2.1
  popover: ^0.2.8+2
  fl_country_code_picker: ^0.1.5
  intl: any
  map_launcher: ^3.1.0
  cupertino_will_pop_scope: ^1.2.1
  flutter_localization: ^0.2.0
  flutter_localizations:
    sdk: flutter
  firebase_core: ^3.8.1
  firebase_auth: ^5.3.4
  google_sign_in: ^6.2.1
  cloud_firestore: ^5.5.1
  firebase_storage: ^12.3.7
  cached_network_image: ^3.4.1
  geolocator: ^13.0.2
  permission_handler: ^11.3.1
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  in_app_update: ^4.2.2
  logging: ^1.2.0
  firebase_messaging: ^15.1.6
  timelines_plus: ^1.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
flutter:
  uses-material-design: true
  assets:
   - assets/images/
  fonts:
    - family: Urbanist_Regular
      fonts:
        - asset: assets/fonts/Urbanist-Regular.ttf
    - family: Urbanist_Medium
      fonts:
        - asset: assets/fonts/Urbanist-Medium.ttf
    - family: Urbanist_Bold
      fonts:
        - asset: assets/fonts/Urbanist-Bold.ttf
    - family: Urbanist_SemiBold
      fonts:
        - asset: assets/fonts/Urbanist-SemiBold.ttf
    - family: Urbanist_Light
      fonts:
        - asset: assets/fonts/Urbanist-Light.ttf
    - family: Urbanist_ExtraBold
      fonts:
        - asset: assets/fonts/Urbanist-ExtraBold.ttf