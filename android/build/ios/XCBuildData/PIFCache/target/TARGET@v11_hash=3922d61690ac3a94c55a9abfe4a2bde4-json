{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886f790e34feb4dd4d60ead8e537927f9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad0ca5a7287849df7cfda8056731a852", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad0ca5a7287849df7cfda8056731a852", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c560c2ee22cf10ece345fe8a6b5aae31", "guid": "bfdfe7dc352907fc980b868725387e982ea0ca19c0771fd6d266f2b66446b37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f706b6dd54e8a07e30a2756489009ff7", "guid": "bfdfe7dc352907fc980b868725387e98f1887403300e04b839877af836998497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0266f8f0f2842e67b74255bf3fc063e", "guid": "bfdfe7dc352907fc980b868725387e9899c4b45e473fef94c750c44c19300e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8194ff26e554ee6a90f8de11ed2b151", "guid": "bfdfe7dc352907fc980b868725387e9801e957e9f2cdc171382139f7af1a5681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802bb44b1077828b6d179fa01ed6ee248", "guid": "bfdfe7dc352907fc980b868725387e98f9dcd7bfc2b70fc0334d5a2b029d2be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c152113bc38625fc4e17715878efae1", "guid": "bfdfe7dc352907fc980b868725387e983c0142618f0c80e86891f682ede039d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2af166d149f00ebc5c16adbfbbf7d5", "guid": "bfdfe7dc352907fc980b868725387e98a01fc3746b75fa29083dde20e4e057e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf915e08fe61199c35f72706cb40b9e9", "guid": "bfdfe7dc352907fc980b868725387e98cd73b22058278a9b4f651422cde8e6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833373674e3c4e480d1da03fc5ac2c871", "guid": "bfdfe7dc352907fc980b868725387e9858da164c6f05b97dc4f70eeaf0ab7f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986f8536b301f6227728eaea1eaa1088", "guid": "bfdfe7dc352907fc980b868725387e9810ee0ac07ad1614d2a0f4f4692012057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801d2814b776eef8910be031725d2c61d", "guid": "bfdfe7dc352907fc980b868725387e9898f6f1d0225d5da11e073619b459ecbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9e2d3a45ca1753fd491c8f931e9a317", "guid": "bfdfe7dc352907fc980b868725387e982567c545f94782ef792faee70a9f28e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956da6d21929a15cd67aad500a62e655", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e09eeb2dd24c32e4daf3473c8acca2e", "guid": "bfdfe7dc352907fc980b868725387e98616c37b3aed90db7f9fdd15a498d9b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd4e84c93c4d74866e6197994ce71e6", "guid": "bfdfe7dc352907fc980b868725387e989ec464f79f509c315509332bb727e800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7431688eaa590326a1d6359c47a665f", "guid": "bfdfe7dc352907fc980b868725387e983249d575db8b5cbd53c0564d7472fd4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d69f3b5f980058455bd517ee4c510c8", "guid": "bfdfe7dc352907fc980b868725387e980d76b669df9576a70807ec449fb952b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982433a3e8146ab4c7e558327d4ceb3171", "guid": "bfdfe7dc352907fc980b868725387e98bc7c56555dd219b78ffdd88c372b232b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0c1d2bd3168ac5a1251b7bab47930e", "guid": "bfdfe7dc352907fc980b868725387e988e7018dc0c9e1f0c9f93b5994bcbc130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849cd014b55f55186c9a596f207506b7", "guid": "bfdfe7dc352907fc980b868725387e98dcef7cb18e4d73ea8eef002919db5632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a9acbcd8e717c5f995749ceb76c7b4", "guid": "bfdfe7dc352907fc980b868725387e98955da4966904666f6adbe480ce93a36f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c23c5e63eab0fdbc14d53d93f6dd122", "guid": "bfdfe7dc352907fc980b868725387e98232d0a9b2be5b70279bedd97f191826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3be7799fef38568b4df1c824f911e1", "guid": "bfdfe7dc352907fc980b868725387e987211db59fabb7296cf1568fb71a68255", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdd5d1d816f9175cf15b449cc37cccb6", "guid": "bfdfe7dc352907fc980b868725387e98c8af4857ac32d849c3b447bcff03aa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e627b6cf606900616c949f64423aa390", "guid": "bfdfe7dc352907fc980b868725387e98dc950c62ee46ede361cd98ae90dd27a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c7b90a8c7d67a1c5dc2db73048b765", "guid": "bfdfe7dc352907fc980b868725387e983f0af89b0b8bda8241febbe4723f8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32ef416b45b836c67815077681d9572", "guid": "bfdfe7dc352907fc980b868725387e981a333127dad81374abf6fe8c83600eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c336e6e4e866cc5847272b04ee0bd45a", "guid": "bfdfe7dc352907fc980b868725387e988f8e2ac76c48795c8610f17a754a59c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c52e720c751a7af0d1f9a755cd3f04f", "guid": "bfdfe7dc352907fc980b868725387e984b1e4946f1823465931d4e8492eb19a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c3fcc2dcf1fae2b90b56287b887f16f", "guid": "bfdfe7dc352907fc980b868725387e98486c17b42526ba81b0e47a838401edd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ba688a8df5206ec086788f396f137e", "guid": "bfdfe7dc352907fc980b868725387e98e24ce56a008cd8fb5a8cdae1e9811e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc24e423de9520588fbfe567fb7afdb9", "guid": "bfdfe7dc352907fc980b868725387e98243ca78b2974c64cd01083596d8fe9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411304ea75475e8fbcc29c059f1d8cd4", "guid": "bfdfe7dc352907fc980b868725387e98c41a340685df4d1ce3783021dcf54fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d55ad10d5d926f186dc762302f1fce", "guid": "bfdfe7dc352907fc980b868725387e98506a4bb5c9b087dedda179e379e974b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbdaba8e9877394696ae84a974f26c79", "guid": "bfdfe7dc352907fc980b868725387e984463890dd80178e8bcdc0b520010feee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbe209fe164c553f5b2159d0e350094", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d470ef6c8853a2c4701ea3cfffe3e89", "guid": "bfdfe7dc352907fc980b868725387e9859e3b579695a572ae5f0cad67ec2876d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984246aa361f473750b4f1af0ca8d3f1b6", "guid": "bfdfe7dc352907fc980b868725387e9885aba67d2d135f9806c9a05487a1a98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894489d1c984b34fd82aaf231114b5556", "guid": "bfdfe7dc352907fc980b868725387e985805bc10d6057738af5938ead2b69fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc390e25e67e0b49c0a4dfc4bbeaa042", "guid": "bfdfe7dc352907fc980b868725387e985658d819a1ceda9c7580d1657159b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132dae290c49a29d3a21a0db00a4917f", "guid": "bfdfe7dc352907fc980b868725387e98c8c90bc417199af9668b9601526e858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32abd0422c3509027b0717ee37ebba8", "guid": "bfdfe7dc352907fc980b868725387e98a79aa5b656ffdfb7930448cc9a744fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143eae0729db144d97526790298d2f57", "guid": "bfdfe7dc352907fc980b868725387e9822c0d109ebdbc53ca874ef63b18251dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879689a63b25e1e289f5bb7d6370867e9", "guid": "bfdfe7dc352907fc980b868725387e983c3ab34f30d3bcea13309294f772a70c"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}