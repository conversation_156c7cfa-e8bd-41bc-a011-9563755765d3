{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988444f128c5bab0d618bc90cdeccdb80d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9828e8b3c0d6355fafbeb43de699a000e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f92fa18ba42ce5b74d07e9813071e460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981eaeead00157d20c2229623adb73f165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f92fa18ba42ce5b74d07e9813071e460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834508855da45e720983a9fa3da8713bd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f34b6618935f4d3679581a1020f2f3eb", "guid": "bfdfe7dc352907fc980b868725387e9862c79bb8212fc8fdfbf8b6b4ecad67c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887ca890055474fab8e63af9c6e070320", "guid": "bfdfe7dc352907fc980b868725387e98ffcf894f9027f710580b9a8051376412", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f388df54a393b062872a7ea7da6a9ecd", "guid": "bfdfe7dc352907fc980b868725387e9803417265c8714de6dcf9c5cc8b206d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983df2551abd2ba982df347018184de92f", "guid": "bfdfe7dc352907fc980b868725387e98e33597366390215c1dd1a398243e533a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814010218de7211fe829cfb8467db7612", "guid": "bfdfe7dc352907fc980b868725387e98a74bb22e563801d31b937a39f4af5c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f0aa6a7e250467a631fac8b3c35277", "guid": "bfdfe7dc352907fc980b868725387e989cbee1cced221a467fa2c07ea5d2fe76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d808ab492b4b04ad36a6f1c5d3b471", "guid": "bfdfe7dc352907fc980b868725387e9831e320a1d214947691a874bf0f53b8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083d773102865761109a55f6eda0265d", "guid": "bfdfe7dc352907fc980b868725387e98919540b64fee38fb35aeed7bea6e0bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988908232c59fb3a81def1e7b8bde62eb0", "guid": "bfdfe7dc352907fc980b868725387e98469abcbe04cfa30fb23c7f3e55a131ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892dedcf6ce8db20d58a5c478fa95a105", "guid": "bfdfe7dc352907fc980b868725387e98bcec6705cc1921d479cbdbcc16e50c03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491ada5df49ce7b0805e55665a87e513", "guid": "bfdfe7dc352907fc980b868725387e983fc8380b89d9cc563a4189048dac9ebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6fdf3fbb8ae45fede5255cd580872a", "guid": "bfdfe7dc352907fc980b868725387e9801744dcb7bf8fd1b90842d11ffa50cca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888d9117e1e0cc7538ac05e4e33c3f5d", "guid": "bfdfe7dc352907fc980b868725387e98889f9359b4f85a2e24f394aa2d1b9040", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02768acceb06047da6a3bf760190995", "guid": "bfdfe7dc352907fc980b868725387e987823c40c2267888393a9c449da0f64d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c13e51722889a1ea6583bf4a383b9e84", "guid": "bfdfe7dc352907fc980b868725387e988b13a5d31948e066ee82cbba34e8b8ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c724a3b9c44c45c35264fc0a7742779f", "guid": "bfdfe7dc352907fc980b868725387e98eb9c5da58648675cdc21f1e949622b00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5ac3ebbbf4eaed644447fbfb58371f7", "guid": "bfdfe7dc352907fc980b868725387e98e87b38ba4818d4b1abd3b3fc4e182d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4c4aa986e49b58a82ed1471d6d03775", "guid": "bfdfe7dc352907fc980b868725387e982228507561b114327e10eab41624a0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98659196accad56c5f87fc21b64a7826d5", "guid": "bfdfe7dc352907fc980b868725387e989da802bb4b0055458f6c107ddb637d2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f69574bba22ac6ccda9f30635301c72", "guid": "bfdfe7dc352907fc980b868725387e98964367ad0b083dffac29c703e1f04de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29d0c76c3495fddbd9944b25e064875", "guid": "bfdfe7dc352907fc980b868725387e9870189b2ac4fd30e2890a4772d39100b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981812431206b3ccb2f841f56c9ee70e01", "guid": "bfdfe7dc352907fc980b868725387e98b388c29edc035b9822d48f2e3d0efb9d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e9586fc2d661e57ec077bfbe29693469", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f3316e5c8b1eace481b9430b57497f0", "guid": "bfdfe7dc352907fc980b868725387e988854d3e3b018752693e7065103957101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aef93c32d4edc9f02a44b56eab7da35", "guid": "bfdfe7dc352907fc980b868725387e98edbcf8023c9b9744700074ba01160ba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880564e8baf75cac41c494c3fd83d3865", "guid": "bfdfe7dc352907fc980b868725387e98867f4916f0202bdcd5601486df64333b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29c2021b866529679b019f5f41d93fe", "guid": "bfdfe7dc352907fc980b868725387e988726b5bc38e541497e018a05b175d796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1b298748f4594c70930fe40be7941d", "guid": "bfdfe7dc352907fc980b868725387e9886ca206a9ad67c417092ee249e312fa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf153ea6441d124b7ea98d836a5e662", "guid": "bfdfe7dc352907fc980b868725387e98896e0d3ac2e7863a768ef41d70764677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6bc2a0f988c5db4e5df1e376b75e5a8", "guid": "bfdfe7dc352907fc980b868725387e98214709a080aa07404894901f360962ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98292111c62084d7570f4779454d579aa3", "guid": "bfdfe7dc352907fc980b868725387e98caeac25d69de0c4ab51e4aef5bf6f0bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98481c404b2455bd6f1f87d6d62a87732f", "guid": "bfdfe7dc352907fc980b868725387e989251ccc6e0cd072376bb20a2ab9380a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986054a5c645bfe66447e79fad602ec383", "guid": "bfdfe7dc352907fc980b868725387e9844442f5bbfb2df18ecb78cc17b258195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978eca582b9ffc0d3401b8b29c367980", "guid": "bfdfe7dc352907fc980b868725387e98dee6f8a407d62077df47992d7fea7600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df18236221e993dfa788e07e4438894", "guid": "bfdfe7dc352907fc980b868725387e98d0e8e71342195d6df63c48194d17cdca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab62ad3e5ab26abbbcb731d0cfd91212", "guid": "bfdfe7dc352907fc980b868725387e9857b5153e6ddf8cd3e3025acb4aaba0e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c19a0fa1c5c6d7016c2625fabccf830c", "guid": "bfdfe7dc352907fc980b868725387e988ee6c9313786893e38ee32ca89070e82"}], "guid": "bfdfe7dc352907fc980b868725387e980882962d238cea8387c3bcd8b727b0f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98cbce12d64d9b8c0b058de063f1885dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e98d3a4530f07cc158df0016651997758b2"}], "guid": "bfdfe7dc352907fc980b868725387e98433d484a14b62e2467659732482b64f9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985cfda15cef848f1b6a0e4fad0c56da58", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e980500ebf109c84d1bf5075d16d6e21cf6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}