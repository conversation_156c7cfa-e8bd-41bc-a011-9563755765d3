{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98870973c453d2092c715119f00b7f981b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980eff50199a98160f985e0d26f20258fa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b569e6270624280cf4e18b2a801ff816", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e58c71bb506013320d818add88ea70c7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b569e6270624280cf4e18b2a801ff816", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98170604362f5702fffe2233898542f263", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98830aca19f08d8ff9ac5a43e23110263e", "guid": "bfdfe7dc352907fc980b868725387e9821e2a575354d3fcc1917cb682272c83f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2b6455beaacb4ff901abdbb60cee06", "guid": "bfdfe7dc352907fc980b868725387e98bf47b13a4bdb5dabcd098dc0c44a75a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd3d38a2159c64d06847c116f8d09cfd", "guid": "bfdfe7dc352907fc980b868725387e98c625718f46374cb6a458bf01090f9252"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eba874a171f434515c48624cce459bb", "guid": "bfdfe7dc352907fc980b868725387e9844f18e0db06909d142851808f1599930"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800225c9c589c119af7597e1b37552c9f", "guid": "bfdfe7dc352907fc980b868725387e9844ebfb7f8963e1112a6dfa14b07b6db8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdbf67188310599191d7ab5f792d9ac", "guid": "bfdfe7dc352907fc980b868725387e9832201f56f8f40d635184871b4ba17f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2109ac51326fcc88efdd587122acad", "guid": "bfdfe7dc352907fc980b868725387e98d38bb5f77faee5ce6606ee7ccac0b5d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f628a09b8a49739cb7fdafe7919eee", "guid": "bfdfe7dc352907fc980b868725387e980277b5c0cffe6d5f2878dd672ee8a539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba813d2110b38ee1a9daf343d3e278f3", "guid": "bfdfe7dc352907fc980b868725387e98d010ba04a820f66fe2c3da9695b01732"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f2cdfd998c6fb733251a6d57ca43e2", "guid": "bfdfe7dc352907fc980b868725387e9887f135414a4a4cb3ca6873a5ce7d124b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e1a904a3a377ff331147709681ab73", "guid": "bfdfe7dc352907fc980b868725387e98d9ba5c2fc74622bca8547942d943c762", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de84bd808c7cd0a1fea19ad238f9a59", "guid": "bfdfe7dc352907fc980b868725387e98715d4ac2330b51ab4ad6180987d41b2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca4265f5bfa99571a874b0abb8df200", "guid": "bfdfe7dc352907fc980b868725387e988870348a6fe9af5b0f507c81a7293e95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988376c58993434d8d7139f688428045aa", "guid": "bfdfe7dc352907fc980b868725387e982ee50897d686bd3871085dc64fb423ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a0b72d4d6cb901c31795167550d4a2", "guid": "bfdfe7dc352907fc980b868725387e98813fa64e868a1da056a26e1d781faef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1c03cde7c43be1f0b4cc8a62711a67", "guid": "bfdfe7dc352907fc980b868725387e98ac216c3bdf80381bec523273515a79d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c803ec43af839ec4611bc36e3a917502", "guid": "bfdfe7dc352907fc980b868725387e9865e5ba8d3034911c3430c7d55c0ca23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b2703ab0903fa30aac23bfdeeb01ed", "guid": "bfdfe7dc352907fc980b868725387e989a25421d123d8ea119b1e9542bf06178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3406402e3681a003b987428629ab8e3", "guid": "bfdfe7dc352907fc980b868725387e983d98ad163a4853d4b2d46a9bd531d3a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50f813a9a3ccd6ee338939b44f82219", "guid": "bfdfe7dc352907fc980b868725387e9862fdbdfe10c52cd7cb491c3ad5dcc7bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bc89cdceaa8c9617d45600e8d54d61", "guid": "bfdfe7dc352907fc980b868725387e989b0d3893357b3b55f98897c07368ca36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe0badefd813df1d43db8ecf9f48143", "guid": "bfdfe7dc352907fc980b868725387e980842f6fbf6aa095f322802cc64cf3d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986634d3b7bf0bd56e1185bc139399b444", "guid": "bfdfe7dc352907fc980b868725387e982b46cb74b8a71236b90947252ccdc3b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec7692d4470ea2f89b0c77d42056612", "guid": "bfdfe7dc352907fc980b868725387e9871a6080cd9de839a34ef54c523779fef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b87f3fb40a2c775f89c2cb4233b79f", "guid": "bfdfe7dc352907fc980b868725387e98d185d2e8862a40c7551ea4497572fa3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8d6b3ae61a736f0bb3cae880545cf76", "guid": "bfdfe7dc352907fc980b868725387e9845b342f0bdd1263f8a115dcbe558675b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a77dab3ae85ff234b911cc034cc0c7", "guid": "bfdfe7dc352907fc980b868725387e9806906dcf191644db25d324f599d61164"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4e4e2f10573a0358047f09b8185b79", "guid": "bfdfe7dc352907fc980b868725387e98feb621e82fd2a05c7045e5a8828b1167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830582034aed2eb684854aed28b813d57", "guid": "bfdfe7dc352907fc980b868725387e98047240f607cfc37672019b8f6350a6e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a3f0badd2deb3aabf163cc11e20c5a", "guid": "bfdfe7dc352907fc980b868725387e982dba907e84b96659312767a891240f96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e25598acb4377cc90533b9d7d2a87c97", "guid": "bfdfe7dc352907fc980b868725387e9820a1950df2ae62ef2a8816c52688b3f3"}], "guid": "bfdfe7dc352907fc980b868725387e98753edfb2cdbcc0c680bf614ab3542feb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee0fa730b6530993a726a60380e2cc09", "guid": "bfdfe7dc352907fc980b868725387e98bbb87debee5c9a6efe1006aa2c24269c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ea0c9a7ed813746253ed85a45882de", "guid": "bfdfe7dc352907fc980b868725387e98546313852b9b2cbe48482c473c209abd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808ff0175278beb350af0bb2f9ab047a5", "guid": "bfdfe7dc352907fc980b868725387e988a3c605fcb9f903008bb31ecc7fe27de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881574430493a247e9c23582808bb4fb3", "guid": "bfdfe7dc352907fc980b868725387e988359da438312b2294948d31bbdefab03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987449767c7b8aa2188fe39949b933c63e", "guid": "bfdfe7dc352907fc980b868725387e98ad555bf7f24942cf02012562a1947eef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cdb5eff3c40f1e986bcd551c10edf22", "guid": "bfdfe7dc352907fc980b868725387e98147500426a5a7ca908aa5ce3ee28b684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689597427ef5f90865300217fea0509e", "guid": "bfdfe7dc352907fc980b868725387e985f9c9c0751a53ffd4f956ec24dde949d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebe01360173b85164434fa11d79d64d", "guid": "bfdfe7dc352907fc980b868725387e98c7344a61a3c4957c81fdb43eb898e03d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f126020b8a232ea6c6ea470f9bd00df0", "guid": "bfdfe7dc352907fc980b868725387e9865d7249bb81bebbb9a1f225f7fb74c49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8a12b79d08c2b7178dae7c76ffea28", "guid": "bfdfe7dc352907fc980b868725387e98d53ac44f8784f607f984b7368a8ff1d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c504cd582afa7f7268d245a7150957", "guid": "bfdfe7dc352907fc980b868725387e980cbd9c740b05afcc16699fcdcabb1fde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f7d9fb5a6da4ff225b88027f9a2245", "guid": "bfdfe7dc352907fc980b868725387e98a8556e9a1409ef1e810c2ff4018f10df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3d660b45fbc20ed251b7e2e0a1960f", "guid": "bfdfe7dc352907fc980b868725387e9836c3e8693805691e1dc8f8060ff35222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5e0414a0afcc5ceb16511cc9c201dea", "guid": "bfdfe7dc352907fc980b868725387e98aee56779b3c6b745ba34d2d39400f33c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831265d1ca651fbb8fe78a889cb7eb5ef", "guid": "bfdfe7dc352907fc980b868725387e985927043941ba621f914296518dcd1227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91234bff92e8a4180a395ea0cdfa84f", "guid": "bfdfe7dc352907fc980b868725387e9847ca3da4bb71cdecd26c733145ba372a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c971e690461d78156f74c01da5c666f4", "guid": "bfdfe7dc352907fc980b868725387e98e81bfc3072e9e400852953f158c5b16a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a5f064e72303a2c1c3757e5ba5abb1", "guid": "bfdfe7dc352907fc980b868725387e98c1fb16389237b528f2b558514d48cce3"}], "guid": "bfdfe7dc352907fc980b868725387e981dc6c74a16cfba8a83762779a3d77231", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98eb859191fb380d6123366908e9d83212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98edf784ab35a1463adb684be75e6e6d50"}], "guid": "bfdfe7dc352907fc980b868725387e9813c3aa62a8539f67bddcde12228b2098", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98612e8cb18450f3f85475311a6b90c4cc", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9802ba93cdbd2af3915de41248911b0615", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}