{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989eda9f3bea59511e4f20c2c3ae99c2ff", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818a8bac4414994ae4d67ca74336caca8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a1e4df8c3b8e4f584b549b8867e9126", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd4e158422524a6be1be2678afeea2b0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a1e4df8c3b8e4f584b549b8867e9126", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e64608f29e201f37d029ae2a437ac31a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc819435ae6042553200d4f755d1b39c", "guid": "bfdfe7dc352907fc980b868725387e9847715c9f64c40321dc26a3952ffc06c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f7cdfec987ce70653979fe99c8295ed", "guid": "bfdfe7dc352907fc980b868725387e98e9c8f865d01cf8fe61e252806065b4d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04d186a7587568f702fa7bdd8516704", "guid": "bfdfe7dc352907fc980b868725387e9895d80b25f4b6b6167f67f90aad0e0e91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73b7ddb84cc504ea56c1d633c6d6d60", "guid": "bfdfe7dc352907fc980b868725387e986558ebf4ecd2bcb0ff058532d50ed673", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980251203754b1972588909ccd555ba611", "guid": "bfdfe7dc352907fc980b868725387e9848d3e09643bf3d4518b42035c47b7e57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a281d9e8a3a6a5919d7fdb30d6fa254", "guid": "bfdfe7dc352907fc980b868725387e98954cb414b46bb49fc38d7b2a1d775d22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980042dddf28d159f932bb8aae4facbddc", "guid": "bfdfe7dc352907fc980b868725387e9863ff9e03c0b37ba717da95d80dc4b1fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98157cff0befb12ece3eaf002173823ab3", "guid": "bfdfe7dc352907fc980b868725387e984cfacd145cdce4ed64b65c9557b2c5d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc4f022d8c7200df2db948a81fe1e059", "guid": "bfdfe7dc352907fc980b868725387e9801f8bfb2e0a4b1a170d0c4338af0832c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df38f3db92a0d718d13930770a3ac8ec", "guid": "bfdfe7dc352907fc980b868725387e988ba7f23f4c9bfb9fcd23a66e97c95574", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a0d6fdbd8462fa778a5d8f661c424b", "guid": "bfdfe7dc352907fc980b868725387e98446dea2eec3672df9224aa2c264ea8d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc945672c312a15ce3e49975c8db3e56", "guid": "bfdfe7dc352907fc980b868725387e9875f3644162a4a5a3612bc7429fc32a81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d7e934e593f7c060b8ae7b164c628d", "guid": "bfdfe7dc352907fc980b868725387e98e89f0942a689ae05974d055482df9e24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200b301c209962b7f381445c45ac8a15", "guid": "bfdfe7dc352907fc980b868725387e98c2b0e87672d5499bca0e83aac4e94357", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c2022ad2dd52e08c8b3341f3e9d23a6", "guid": "bfdfe7dc352907fc980b868725387e98156e041ff1f334f1e226df27d6abf702", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd33d369452b085f25175a1807d5869", "guid": "bfdfe7dc352907fc980b868725387e98ff3070fd7a8b4e05feb8b97f9d4699ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987feda9fe67a4ded65cf9006d9aec67a4", "guid": "bfdfe7dc352907fc980b868725387e982f7c51cad3023cbfe930c3a1d7a6999e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d38c7d8c120ed26a6b8d58c89bd7f8", "guid": "bfdfe7dc352907fc980b868725387e983dcc2d7af20d29c98e7b8c19e9f5e9e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b0724960c29592febf52cfa3d1c97cf", "guid": "bfdfe7dc352907fc980b868725387e98d06174af2365ec6713fec618655c660f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574da8c60ed144832015de5330710abb", "guid": "bfdfe7dc352907fc980b868725387e981b2a9c1346d7dbd42c7f98738d451114", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98534b48423c13a6f7d933933c40881604", "guid": "bfdfe7dc352907fc980b868725387e980723168ab5326e922bcc5eef3fe438d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3832358ae556043e164f3faba345d70", "guid": "bfdfe7dc352907fc980b868725387e989fb04262c1b030b00dd00d2de65f6902", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ac048cc0eb05683f1c4c2f5e68d8f9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98666eb622df1dc3b739ec33c70bc0b963", "guid": "bfdfe7dc352907fc980b868725387e987437287eaf2dae96bd1372f1ae00f85f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806679ef6cc3cf8d15a5e1b08c7384833", "guid": "bfdfe7dc352907fc980b868725387e989b22e377d3771f3cd787b9413cfb8823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9fd3cf94f7253a425237c1cbf0ea298", "guid": "bfdfe7dc352907fc980b868725387e9892c0d6ad964c8526f308b93c1551bfda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed8f02ec5a4286cdcf153107385928db", "guid": "bfdfe7dc352907fc980b868725387e9820319612372b9ce7f42efb34cb9ea9f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f640e711c652aa38b3f8d3eac566e4", "guid": "bfdfe7dc352907fc980b868725387e98d4d4b23e3cff7ce2ca98e3c4e20dc5ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864afb2948a5bd401902328b78c25d118", "guid": "bfdfe7dc352907fc980b868725387e982ff4ecb87e1d15685098c7e6b5381376"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136a859e8e504d5363640575c741e226", "guid": "bfdfe7dc352907fc980b868725387e98c2663e1749cd9c93f216882e94c629ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824aa687ffbd39ea77f3d69305935ffbf", "guid": "bfdfe7dc352907fc980b868725387e983d674f37c2684ac9d5c0cc1922e76c15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883e8aa0228ea2ea1289c8d373b97c573", "guid": "bfdfe7dc352907fc980b868725387e98f145828646c91af05140d5602ac1cbbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d450874555e29c560190e5b49df632cf", "guid": "bfdfe7dc352907fc980b868725387e98b929d85af5c49f0d161b0829fe637986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf3b0bd0081a036bc0c4f551c795842", "guid": "bfdfe7dc352907fc980b868725387e98dac9f317f1e82bae974e327df425ac26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b79902f3795aefaca0d61e92204775a0", "guid": "bfdfe7dc352907fc980b868725387e98de16f980e659cefb337cd6dad93b231f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b06ef83cea5b8521875f346b5557d5a", "guid": "bfdfe7dc352907fc980b868725387e9847f7a4bfe660b331939394f792a1408d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab8074d232928a51d0c64f5780d2bf5", "guid": "bfdfe7dc352907fc980b868725387e982558d9f52af7ca6e71b34e788c74c4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c13207952c0768a4078fb7ff65b6dcf", "guid": "bfdfe7dc352907fc980b868725387e9885a501af426a36d035667016510c4f60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a917cec2a9c44fa06de7a530db7a98a4", "guid": "bfdfe7dc352907fc980b868725387e986796698810686b17e8af729052fa71ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398c170212839408e3759ff8209d48dd", "guid": "bfdfe7dc352907fc980b868725387e980e894359ad62c34baf8d6f970e1193aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4c026080a56143bd8457aaff24ad65", "guid": "bfdfe7dc352907fc980b868725387e986b6ceea4d95ff3cfa77f3013f59f3ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5328bcff6783e1ade259dababc8959", "guid": "bfdfe7dc352907fc980b868725387e988647e50cec7e9820e2c485f301f2cf70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa1ee22ef6d1499a8aba1732ffb7657", "guid": "bfdfe7dc352907fc980b868725387e98bd9900324b9976104e837d3f8548a360"}], "guid": "bfdfe7dc352907fc980b868725387e98fef99919b724763168e27062121c47a4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98565c9eb096b3d4ee68cd1a8c011b5866"}], "guid": "bfdfe7dc352907fc980b868725387e989fd12e154b62453cdd1a75b1973ed817", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fa66806295490bc51823c5419b3f4174", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9837bb7499d449594054f754d641de4512", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}