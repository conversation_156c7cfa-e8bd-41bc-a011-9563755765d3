{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839565ef456c1cf5722da0c00ee8e2020", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee3cc2b9a84ea73926d9a9898af22713", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce1000ab565496a69b66f41b35062460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d64e443f732bfc5adc196469c774af01", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce1000ab565496a69b66f41b35062460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a17acc70adaf4d1dc7a0eb3071f4c35a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a2230a180e455378d1a50cbb781eaa6", "guid": "bfdfe7dc352907fc980b868725387e98c4ef34a23e62ef22db2f984ca1758050", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2048717c8a59230965ba07825c9d0eb", "guid": "bfdfe7dc352907fc980b868725387e98d4e1d8811853a34cfd07d3cd4396a954", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ebaee5bfbe58da9386390a576c949a1", "guid": "bfdfe7dc352907fc980b868725387e98cc48f2400b1ff0e1d7d9e01558da3781", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9057013a67f2faa89c2a6192c98b4cd", "guid": "bfdfe7dc352907fc980b868725387e9863ba0cece4622630a389fea1b0cd64a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e0bea2095ba3f687b08f2f8bac611e", "guid": "bfdfe7dc352907fc980b868725387e9898a9503f0bb4cc45c3eb21cbc000e815", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888c86fa90f719e5300ec9aaea2c85fb1", "guid": "bfdfe7dc352907fc980b868725387e982c35a2e0286d60b963d310dedb6241cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c8432ea688f90b39a9b1ab44f9be6d", "guid": "bfdfe7dc352907fc980b868725387e98ab13cb9b06420b19a91c8ea793c147fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1de19562773d4550567c35feb031f7f", "guid": "bfdfe7dc352907fc980b868725387e983dd6f0ddfb419d13d01c8310943e5b96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d46c20c01abdafcbf2a44fd8e50dcbe", "guid": "bfdfe7dc352907fc980b868725387e9805bacf31e6b0f9c8a897c43159709eb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e9f5b512f2ce597152a93aee65d78a", "guid": "bfdfe7dc352907fc980b868725387e98141a5ce99b88c5d0ca29f0b4e3b7945a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dad9212b0cab2645cd2a2c4243c09ce", "guid": "bfdfe7dc352907fc980b868725387e98c1860e09266206602476067d6e7c663b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fefbec9b4d7fa0d374c02ee189b4d0fd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a747ec36116844c600fcd2b0dfc1d19", "guid": "bfdfe7dc352907fc980b868725387e98c28ba19c23210e5ef7b1ebe235a73f40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c488d5e887e1e84df3746a01c7169b24", "guid": "bfdfe7dc352907fc980b868725387e98bda791b2b975d5c31f0606df4c9798b3"}], "guid": "bfdfe7dc352907fc980b868725387e9819b07557c4fe60a772258777e1748379", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e988952a24efd70307c0e5379d95c175983"}], "guid": "bfdfe7dc352907fc980b868725387e98a474ff767503e26c4e37595f53ca60b1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9876d55c011bea012e3c1a8983256969c4", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e982457d438936746d1447168a376811029", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}