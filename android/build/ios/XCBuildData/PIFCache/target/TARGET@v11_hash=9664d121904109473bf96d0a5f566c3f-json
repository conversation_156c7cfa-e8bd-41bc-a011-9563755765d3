{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ea7f769fda33353d549de9b0ad2ac2f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d71c31d0b41329842ae0d3e01072fd6c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98374a0ee647592544c47b76093c9fd042", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc4633536a663d3f1712bece2222bc23", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98374a0ee647592544c47b76093c9fd042", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68530f355a73642945f56f6cf0f6e5a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c25d5e5aa3ffc6ae477ae86cd4bf0bf9", "guid": "bfdfe7dc352907fc980b868725387e98f2ae6f3a9a00748f87cd81b039abe4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba15d1e85121ff67db750db39c4517a7", "guid": "bfdfe7dc352907fc980b868725387e98959117a239d290728d5da6df1e214b61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981623f15d6507459b1e07912ac63e8907", "guid": "bfdfe7dc352907fc980b868725387e98de6910cae78c915fce9e60a0fe9336ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebd27cc8ff52cf21bf50e18a244fd1f", "guid": "bfdfe7dc352907fc980b868725387e988d8401d367659c78a0ef8b0ff6e6cb9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc5d844c1d9de68686afe5602f795e7b", "guid": "bfdfe7dc352907fc980b868725387e98b72df06278384be2e5bf670b9e537a31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2edaa5dda7413bc6862ea288763bd7", "guid": "bfdfe7dc352907fc980b868725387e9857ef6073f81b7acea6a9829fd95f975c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b1c8ac46faf211c97ce7e0cfb15493", "guid": "bfdfe7dc352907fc980b868725387e986fac90b746b0719717619e28f3fea22d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e8f7586e56b8d96371934445caac8f", "guid": "bfdfe7dc352907fc980b868725387e989540a85fdc6cfa47fbd4e8fd024bd6c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307d3413ec271fad7e0ebddde4e6902b", "guid": "bfdfe7dc352907fc980b868725387e98e1898754ca596260e56e05513a3a3ce3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adcc1e620f2e5a9999f6d52953d83808", "guid": "bfdfe7dc352907fc980b868725387e989282aa9814aa55cd5ec14065a4af20b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f833bc5de0d69f4e834e59cf0d6669", "guid": "bfdfe7dc352907fc980b868725387e98aa529efd3193c685f4696f32b84e4137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b24a91ce94b673b7ec411b6d65542d48", "guid": "bfdfe7dc352907fc980b868725387e98edd1e443680fc27081b89fd5e8cbef94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5d33cb577d08c3e40f6a76332802b8", "guid": "bfdfe7dc352907fc980b868725387e98fb246837160dfbbef5749212ca87e11a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901c0fa245c8a68c3eabef2c93b13094", "guid": "bfdfe7dc352907fc980b868725387e9819e5813c99e875f5b129a94966dea3a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843753587a8a1c62f6b4317e18eb4f85e", "guid": "bfdfe7dc352907fc980b868725387e98a88f0b148fbc8a8befed655a53604df5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870de2d8598caaf3190ea1f85a599f36e", "guid": "bfdfe7dc352907fc980b868725387e98b2728b7436783b71d6d478a4f4a2bbc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa8ca407202919e7f452fe25e593935", "guid": "bfdfe7dc352907fc980b868725387e98b103f93c3cf20dee3347666edd010e3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e1236095832605d3e6f6d371b4493cd", "guid": "bfdfe7dc352907fc980b868725387e9886faec5701865c9b3eab81fdc7a09b48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8bc7a058c8b62e8e7b2ff0181e4239", "guid": "bfdfe7dc352907fc980b868725387e9897771ec006df3280c54ebfad380df0c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2cc5411185ac46f2536e80303612a8", "guid": "bfdfe7dc352907fc980b868725387e987ee66d8f75ece32bcced570106eb613e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63981b02aa5c570bdc48a715cb57aaf", "guid": "bfdfe7dc352907fc980b868725387e984b3047749d189ba9fd54e50deac08637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad4e4f9b34a35e275dd18b4a9037621", "guid": "bfdfe7dc352907fc980b868725387e98b6f0936e13e54ae9a1750fec2d1c8e9f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989b67f43a7a15551157adb9be1d3db512", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879b5af52f11a50af6e947371622b2a30", "guid": "bfdfe7dc352907fc980b868725387e987d3ca295aa953ebcea2dcc5a186b6e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200bb5252e334c1f9f2c8d2c226c85b2", "guid": "bfdfe7dc352907fc980b868725387e9830d2ba970ff3b86ddca01bdc81ae6206"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b8ae3614b10a6f03b2cc6953f43839", "guid": "bfdfe7dc352907fc980b868725387e98a579eed414601cc04c8a2e824f1f74b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985369bec87e0110242c927e238e2382b0", "guid": "bfdfe7dc352907fc980b868725387e987524f596a40482ac2d5665430fd90b6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98644fcbd1990c4df409854b153bee4aeb", "guid": "bfdfe7dc352907fc980b868725387e98cb39f839477e0d80e056110301841445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f98c633856d402603dd726b85a38748a", "guid": "bfdfe7dc352907fc980b868725387e9855068df43f52ef2dc634c71bcf822f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d46330f70322a55a87cc0b2b3a4788", "guid": "bfdfe7dc352907fc980b868725387e988a8ec497a0a6560b80606cddb7149739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf4769d57897628f4797c6db673118e", "guid": "bfdfe7dc352907fc980b868725387e985f1b1094d777e5b1949e89f69bea9885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986652f4f1f835b74c9f5740fe0a0a9d18", "guid": "bfdfe7dc352907fc980b868725387e98dc772e8c6d08ec3436b04e21e1d7236e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678a003d4c7f78664577d08399611d94", "guid": "bfdfe7dc352907fc980b868725387e98cdfdbdb6091ebaa252977866421e74f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c9f17cf5c6d87c3bca8660979319602", "guid": "bfdfe7dc352907fc980b868725387e9888c356334ac710f3c22c7de218d48338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f64ab6229352a46d2b69596b4079306", "guid": "bfdfe7dc352907fc980b868725387e98f6f2099ff6463be1f36bbf71ad4f7eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9bf8618eeb7a5a833d7e8da1f728d1", "guid": "bfdfe7dc352907fc980b868725387e985124727967fd67b96385d7f085bb080c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878743b0b70633e37e9605f362dcc9c47", "guid": "bfdfe7dc352907fc980b868725387e984cd37ca6690e11c7cb3f10e32cbc5cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8c41b456a352fc222e2d9f0eb88b681", "guid": "bfdfe7dc352907fc980b868725387e98005eddd7e2c9ae4a1fc18782607d731b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ae8e8e0bad61b170931c064ba70914", "guid": "bfdfe7dc352907fc980b868725387e98c7a1b75cf94c35f0dab1fdacbba4c819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831161eaeb149f42d723d5a195db784c5", "guid": "bfdfe7dc352907fc980b868725387e984d1cb1424e8119c352260e61902da032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82e87bd4e660a618fb3d4af65623990", "guid": "bfdfe7dc352907fc980b868725387e9899dfb451e0d74438e97879561bab02df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809b475aa32c728035ca9fb0740ca26d4", "guid": "bfdfe7dc352907fc980b868725387e980f92dfdd56bbf12acba51e753250a042"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c5feee0ca43579bd8952e7b82495e1", "guid": "bfdfe7dc352907fc980b868725387e98e4854db579f33d9783948c7198d30e91"}], "guid": "bfdfe7dc352907fc980b868725387e98832b5113e772b2dc28aa605403a969c4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e988403dfc06ebcb1008dc16671d85a36c1"}], "guid": "bfdfe7dc352907fc980b868725387e98ab175fbab67afb0abba1bfc8c8a42180", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dac1324660ac938230507f862fd1712b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98d4ffa8c3b5638c2544dcd747975e30fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}