{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f73c15e069a2f21d47b4ac4d6cf5abc7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba5682d4bc6e901f0ad90f01fd8f888e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987026e7c1693c9763dd91c59678dcb308", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e0ba913f6f5ee9479e3ce8156d656e3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987026e7c1693c9763dd91c59678dcb308", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989eddbd504f2330dd397cd0a4ff4dec5e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985528199fbe1b5b0786f7af3d67ad38b4", "guid": "bfdfe7dc352907fc980b868725387e98d5072855a8aa84587cb577323a99a9c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ff1c7abe1874c28cef4b7089c2f62d", "guid": "bfdfe7dc352907fc980b868725387e98d2c16d71698243f4d5f2c522f65e482d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106b63a74b06c724f76c803d474d12df", "guid": "bfdfe7dc352907fc980b868725387e987ef3e1a4b593c1f26f6fcf30492637e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d093a9fa1cc1dbb00f7cace087d3811", "guid": "bfdfe7dc352907fc980b868725387e98eeaa176b8d39c3910ef89886ca216977", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845722ffd2e2f54bfb0280c280883e36c", "guid": "bfdfe7dc352907fc980b868725387e98ede5a421b706455c2167a82f43cb40c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f32447b33cd14cca28cffc138ab1a9e", "guid": "bfdfe7dc352907fc980b868725387e987ed5e606a9ea666df10e9499027b9346", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d5425116cd1945ecf1040b8b0d260f", "guid": "bfdfe7dc352907fc980b868725387e98e491e2194dd2587db46ec9af4ca4efc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cbd37de550d84b675f9a2013da2886", "guid": "bfdfe7dc352907fc980b868725387e98e61c2109b9b2194c05449a64dcf2adde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988830be388a52514c72f09bef02121d2e", "guid": "bfdfe7dc352907fc980b868725387e98d635d719844d3b12fa4cc2173773511f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985cebb52c69d9095bf2bcfe716909ecbb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f619eff56541828cb0628f11a99723eb", "guid": "bfdfe7dc352907fc980b868725387e98dc5101dfe07b52c3775d89e00732db38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e32cdcdebf2dd3794a61b23a84b325", "guid": "bfdfe7dc352907fc980b868725387e981ce84c95137783c7396287df4357a741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989459bf7337cabe463f363f033be266c4", "guid": "bfdfe7dc352907fc980b868725387e98e4849c4bce37155561798a9107d98386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3df6639f9ebc4430eb804ddea26ad2a", "guid": "bfdfe7dc352907fc980b868725387e98f96a31495b6716444dae575ae60f49c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364f249b0840c5ca00b96def57600085", "guid": "bfdfe7dc352907fc980b868725387e9818537c9b612111ce7fba7ea82d418f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70f325bdddaf606618b686925c23acc", "guid": "bfdfe7dc352907fc980b868725387e9812cf65bfde3299e2828579b535fd8b80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982903554429cdbb23f8d1642dd09e6a4d", "guid": "bfdfe7dc352907fc980b868725387e98c2160782cee9dbff4acd382aaf49eed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40b7e6e64fe5a4fa04bd4e3ee98a53f", "guid": "bfdfe7dc352907fc980b868725387e98683576462a866c8443e0ba008e013d81"}], "guid": "bfdfe7dc352907fc980b868725387e98822809419b353940b7c7823e6b14ebb4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e985b574059c2ea073a0c5240926176f0ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e984c6cb5b3044a2c855873e351f95b5a55"}], "guid": "bfdfe7dc352907fc980b868725387e98ded11af9d88d1a5c3894123afaaa5d61", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981e912e77ead05e6a6f841d0f1360ed0b", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98a9fac73b765ffe037b9f8288d3596339", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98479860e07d2325cc74b220eb2f58d65a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}