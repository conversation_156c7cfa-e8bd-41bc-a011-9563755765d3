{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891417992cb87de0e04648d063361be2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed843e934847e71273aceb80a6d1696b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983612353379ec42879e967d2368da3007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c98899be497a63f4bca6ee6f66e0d3dc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983612353379ec42879e967d2368da3007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c6e0cfe40e8493f6719c3f9b4eecfe7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982abf151ea94cbdb9c1beeed79d27191e", "guid": "bfdfe7dc352907fc980b868725387e9860e4ed112dd781843fcb143065300799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98653e2d774a034ebb42d0cf29647bd569", "guid": "bfdfe7dc352907fc980b868725387e9829df717189c081beadbdb64fd863a167", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e143714ab903e10bdc250852ce4abe", "guid": "bfdfe7dc352907fc980b868725387e9854e99c919c388131c6877dd88ecc5ab4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839802e0cbf9a859382c296813529da01", "guid": "bfdfe7dc352907fc980b868725387e989edbe15044cd7154d7fed20f78037778", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91dce245706e291630510c350fa7181", "guid": "bfdfe7dc352907fc980b868725387e98068482f657bc170a8165ee8a6fba1fe7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6f617155b5feb0c34c22df57fbc148b", "guid": "bfdfe7dc352907fc980b868725387e988f367df22900fd8fda80d7b1c04b9675", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a399825fcfcaa7520139865fefb6421", "guid": "bfdfe7dc352907fc980b868725387e98901b7fad19e45f2908eb5ce7e39fa145", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982140455e4b4a149ecfe0dc4934802918", "guid": "bfdfe7dc352907fc980b868725387e9855715008a6aa75b843796a1279e70ff4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98198b7ecf1a04aa7659854f3e49cc99ac", "guid": "bfdfe7dc352907fc980b868725387e98b67d0d5f26edc6d977dc2bb8df6538f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d0ff68ef4b0bce6b9a82934c2f5ad8", "guid": "bfdfe7dc352907fc980b868725387e989d5a9b334e39c89e3efc8ae921763545", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3a427d67964db123c0b45c94a3684f", "guid": "bfdfe7dc352907fc980b868725387e98c4596f4d378ee609979f3a1c8feab263", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c90263247eae908b4071c7cee82dfc", "guid": "bfdfe7dc352907fc980b868725387e9803334f284b411c8226a5cd8ed21fdcc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f2607a47bfdad133e849c7cc2963e2", "guid": "bfdfe7dc352907fc980b868725387e98b7c34ae6ca078d91b135b9005f48f01b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844f65ad92e437de11d0edb109168325d", "guid": "bfdfe7dc352907fc980b868725387e9841800c1ea975ddf07b7d3da86bb9ae32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0273ff49e01d42871d17162219c9bf8", "guid": "bfdfe7dc352907fc980b868725387e98962f17e17e4ae6fea895ccd516293fce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d5371ac5f9e371dbc123dec77cb75b", "guid": "bfdfe7dc352907fc980b868725387e9894b0cc0380ba31a311caa3b3aac9b0fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be3780214d3bb49e6e2fafe701032c68", "guid": "bfdfe7dc352907fc980b868725387e98fcb91dd7d94787a76457c4ee94cefec6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816dcd1809e1d78e9df09ab459cb07d9a", "guid": "bfdfe7dc352907fc980b868725387e98476bbe7743f63411e23afd731cf13e5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7ea018617a6fc29d9071d79ee9f00a", "guid": "bfdfe7dc352907fc980b868725387e982679b9ea571b16bb306779f0527bdd25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eedaaf69f69f37c9cded4c1d0595bd2", "guid": "bfdfe7dc352907fc980b868725387e980dc1d8af59b0e3d0639a980a17a6d715", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3d86837130c296c93e0c533acbbb03", "guid": "bfdfe7dc352907fc980b868725387e9878446bc4ebffcea5443bdebaf4b4086c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98993f9479d519d7a17ebab9e82ecf673b", "guid": "bfdfe7dc352907fc980b868725387e9820b160f2d55bae05b3197471f8accd78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980adc80373be48a18414602e8b44a196f", "guid": "bfdfe7dc352907fc980b868725387e98b3282b2ee860fefaf873f3ed8eddea05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982437a44465988dece625059f4f7e8f8e", "guid": "bfdfe7dc352907fc980b868725387e9822115f2531d236c0a7db3177ff2a9bcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed6c738bbca30d948e0879603738332", "guid": "bfdfe7dc352907fc980b868725387e98d48c2f04c60302f64e1a88b01d14b265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf504c2849260a0f820e62226c2560e", "guid": "bfdfe7dc352907fc980b868725387e983d3b1c96feebf708da291c034fe3cb1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e20e02975dc937fc4992ffa8efa1a9", "guid": "bfdfe7dc352907fc980b868725387e989b95474505929cb4062930a3e4e54c49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fc602a84f687dcf960f124c97d3fd3", "guid": "bfdfe7dc352907fc980b868725387e989a5afb2be61d751decf7c53b6df20d89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753a579621baac1c57964cda7d06f5d8", "guid": "bfdfe7dc352907fc980b868725387e98ee809dcdf9db651befaf91b4fbfb79f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0451ffa87ca601a2571830545660f0", "guid": "bfdfe7dc352907fc980b868725387e98a9a68e7aa8b5d445c1e225165457f559", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983f50bc2dd1cfdb8361e1e0e564a223ed", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ecb8dc2d3dcb2bbf9538a7f1d4d19e2", "guid": "bfdfe7dc352907fc980b868725387e9862aceeda48519eabaaf1c84fa2e4f28c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabf0fb3701ae6816cd025df0c162907", "guid": "bfdfe7dc352907fc980b868725387e98250a8c367e32063767beba7070790cd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2decc922894ad0397e329fa9578a58", "guid": "bfdfe7dc352907fc980b868725387e98794bac0560699412208ac96d2c978468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98386e9343f52d39a69675f30a8e1b8943", "guid": "bfdfe7dc352907fc980b868725387e98ba70c6f4a6ded0e848f4125ca80401ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b4d66a1dbcaf812ab6c61ef36f65b5", "guid": "bfdfe7dc352907fc980b868725387e986b04f068f1fe408a9dc28ab0a14fbb83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988582527fa07888da531798401b42d877", "guid": "bfdfe7dc352907fc980b868725387e98718ab0fa3ee4003aa0a703ac2db4114e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c33560475bf8edb7bd4e7c54c2808e", "guid": "bfdfe7dc352907fc980b868725387e98178f84ab10c73fe613dacce093bdebfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d6468cda039bf403591dd14ca1798a", "guid": "bfdfe7dc352907fc980b868725387e9856a467952766be606baa5b8251a04f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984450d2f5997dba4f8f49a15eef121be2", "guid": "bfdfe7dc352907fc980b868725387e989b46ab5d81920287bc8968c9d26f1b99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b350e7110eebf47c1c3b144210e0d158", "guid": "bfdfe7dc352907fc980b868725387e98506f4eb0db8339934b90acfe62a3f66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de96a1108e335a27288c80c75409d91a", "guid": "bfdfe7dc352907fc980b868725387e98c241b7fc48f3c8bbf3f6484eed593cc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988589bfadd7ab20eee2993103e89b660e", "guid": "bfdfe7dc352907fc980b868725387e9837f84ce6fbc46ca5bed429e78b3690e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699a476bb7f99ea31e3787fe6a4ae91f", "guid": "bfdfe7dc352907fc980b868725387e98d088a32b53c72a13e634e245011a1853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804acc36184d7c6a9906dca705444d0ff", "guid": "bfdfe7dc352907fc980b868725387e98930f51116eeced0ed8be046fb9970d46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989658df9d74b66b02107d38db4b2efc33", "guid": "bfdfe7dc352907fc980b868725387e987f5ae32cf8d0d0afeea782ce88e08818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2881be5defc06402ebfaa5f9155afec", "guid": "bfdfe7dc352907fc980b868725387e983246f06ae195d3a18c6f461aaded3582"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66a32c3d9fb631da29dd5d0c330bf28", "guid": "bfdfe7dc352907fc980b868725387e98795870398605db44e0dbbd8b7e90e436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def0e2891af5d4775321ee56d641fb1d", "guid": "bfdfe7dc352907fc980b868725387e9852990d74238c2fe4767aa5e630fefcbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321653780c6d9de989e61018d499fc85", "guid": "bfdfe7dc352907fc980b868725387e98ff1242382d3070497fd60a91c9558a7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416c31da60cbb8222cfdb352253ce288", "guid": "bfdfe7dc352907fc980b868725387e98c85242bf970e9db793fafc7635662aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62cdf0b2c62cbf57d2bf7d5498be902", "guid": "bfdfe7dc352907fc980b868725387e98d1d9d7e8fc6a8bc244a8cb0f9e49de21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe1c5980fd3681846186f3b2aac4657", "guid": "bfdfe7dc352907fc980b868725387e98a093aa30d95b22915520e7d2cc65cda9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981120f86e55d9d48b5139fd7e34de841c", "guid": "bfdfe7dc352907fc980b868725387e985be7ab8143c3cacf8fb2da20c2bfbc05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3971ed3fc38e3252a743cfadd48e1a", "guid": "bfdfe7dc352907fc980b868725387e98c647fc06c522429a4520786e9e42f2c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf003b3072fdada796f76696af29b3e", "guid": "bfdfe7dc352907fc980b868725387e98e1aa4e9383bfa03bf4d04d330980afb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e0067ab5c922076fb78b5b8cab0cbb", "guid": "bfdfe7dc352907fc980b868725387e98c4b5b19cab16b2ed12f92e92d83ed1bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7506e1ee72dd3b0f047fd96a3992c68", "guid": "bfdfe7dc352907fc980b868725387e986c63e75ea1731552acc32435cedd2b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35bd38e604f22a29f47d6e6d9103fa1", "guid": "bfdfe7dc352907fc980b868725387e984c5af68b258b297a7275ce560b5966fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96b18163ee0706b20cb0e142c916675", "guid": "bfdfe7dc352907fc980b868725387e98e6a3fad187cbb35be970b13ea716ba4b"}], "guid": "bfdfe7dc352907fc980b868725387e98f708e00ecb7c3905cbf781cc78edc23e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98d45a5351d53308c3e59bf06dee59a86e"}], "guid": "bfdfe7dc352907fc980b868725387e98e17c3039e79524d4709739ee50b169fb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984cbc84ecbb868965561979feef143a02", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98779041c33936652444cbcf88c8ac6abd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}