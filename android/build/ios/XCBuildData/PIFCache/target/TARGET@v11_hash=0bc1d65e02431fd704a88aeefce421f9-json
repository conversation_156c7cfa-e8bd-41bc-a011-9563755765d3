{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a1fe98e6b43b47c6e6d5f54e0b26e47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e68252a83b1f1dd4dcade0416b27163", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982efc8a732a2fa8b253ccceae5762e519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9f04a313968058400e3006ed5ac32f2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982efc8a732a2fa8b253ccceae5762e519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d4187fa034a707fcf80ccc90e0eef18", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894a422860384b9b66366ce8c0c4cb5ae", "guid": "bfdfe7dc352907fc980b868725387e98f9d5022867c480b8fea3708d8333b253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6f9ff972f3cd00a1c6ec875931fc91", "guid": "bfdfe7dc352907fc980b868725387e98a92cfeb7c0d188b0df2a38147618cf70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc53f85d05f6f21522435372bc3184b", "guid": "bfdfe7dc352907fc980b868725387e989af17d80f807580df5e01de04b254d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983059d93c4990b8b0353b686c9186cd1b", "guid": "bfdfe7dc352907fc980b868725387e983206f87588a772004ceda9e1c1de28f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a80e3ea7ef674e913c4ee05475da0e0", "guid": "bfdfe7dc352907fc980b868725387e9869eb93d0d2b3a874efb23237e102338f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884bcb7340d43cc7f48dddbc7a60be8c6", "guid": "bfdfe7dc352907fc980b868725387e98e71ccf87ff783ec3cc54f95f3b9d257d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4226552e7c6654865ebd550bff79623", "guid": "bfdfe7dc352907fc980b868725387e983779942d1bf5f54adf539e186d25bdba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f620ee2caf00079abf986e5b67bc8fed", "guid": "bfdfe7dc352907fc980b868725387e983486ede272ee71c037d81e33ce276b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333e6fe3ed81e4b376235d4647a19dac", "guid": "bfdfe7dc352907fc980b868725387e98e9b52e52d5baf411e55107c54d65097a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814fa295b20f0e14ff11c19d564568987", "guid": "bfdfe7dc352907fc980b868725387e9893c62a1ba8a72112de93e2e900d253a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae3177932eca589df23baebfb1a760e", "guid": "bfdfe7dc352907fc980b868725387e98d891a42df77577ef8ce31ea94feb97c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca0f48c69f4cd419b0b4eed403d5276", "guid": "bfdfe7dc352907fc980b868725387e986ac5cf6cf0c8f711d95d2b3cd5d127e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657b3542c83d4f47346bc5586f551e42", "guid": "bfdfe7dc352907fc980b868725387e98cd40645b63ec51a20756f83bf5a478a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ccdc2e38d23ec0a535a21ba396162b", "guid": "bfdfe7dc352907fc980b868725387e98a83c8ca15a72438b6d00ad7f4e264f53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810b2c68c30bafb96bc77ed3dae87d1a", "guid": "bfdfe7dc352907fc980b868725387e98636f65e79e1665b95d892d6c9d8ac8a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851937f79b076672cdd58990f197a66c8", "guid": "bfdfe7dc352907fc980b868725387e985b3b8532765e27207f17413ffe9a8eec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4e2314a54022a10e45506219095017", "guid": "bfdfe7dc352907fc980b868725387e981abe7fe6040971d63d466725760807c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd6d0a377d7887bf8c105b2f6b3ffc70", "guid": "bfdfe7dc352907fc980b868725387e987dbc748ab3994a5661056969ecb0a812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9e653d7a8aa8e28d322f3179bbc982", "guid": "bfdfe7dc352907fc980b868725387e983b45149d93b93fb40f73ca7ca81323a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd893ad91f9d7b88ed73835d752561a", "guid": "bfdfe7dc352907fc980b868725387e98abacc3d3054353cf9fb13011323940b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7746b11dd069111c5f8f88913a28a00", "guid": "bfdfe7dc352907fc980b868725387e98fb7ec0fe3765c5144ff75def6f68f50d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb5618e0b6e53e9b61ee5ff2eb6cf1ed", "guid": "bfdfe7dc352907fc980b868725387e9840d75cd0b8625c12c546f9ec4435f78f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878aa41b952bc97353f5b21e57d7092aa", "guid": "bfdfe7dc352907fc980b868725387e983c5780411d6a82ce79995ac0a5995ced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7fa8b94aa45bfc8a194ee2140f9b7af", "guid": "bfdfe7dc352907fc980b868725387e98c4afc30ca14014864d67b844eae2ec72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6b0fd4027e2e970403ffe37a6e63db", "guid": "bfdfe7dc352907fc980b868725387e98107169a50011765ba0febf8fcd47126f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d984a5a167958051d9e2010f3b7867d", "guid": "bfdfe7dc352907fc980b868725387e9867f53d8a3fea2dccaa2f28fec590925b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989adf4c632240162788ab413cdecb3f7a", "guid": "bfdfe7dc352907fc980b868725387e9802c2982f7c39f44a7cf96a1e598a4894", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f41722adc572becdee9e0c476629c5", "guid": "bfdfe7dc352907fc980b868725387e9850ad57587ed5de8fe3df7d215fcd7234"}], "guid": "bfdfe7dc352907fc980b868725387e98477536e35af8d78595cddc6f008f5e23", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847ace5f65260b4d3d64e2acaffb07f29", "guid": "bfdfe7dc352907fc980b868725387e9827c6b985006f66c3af829a6b8b09e7c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881bd12bda0f7060691bb8b971fefe3e3", "guid": "bfdfe7dc352907fc980b868725387e988337340c900375e6d6537d6b9b96634f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e24a34f4ae5d89b1d200e6060e58a65", "guid": "bfdfe7dc352907fc980b868725387e98569bebb9890e3073fca84906938cceea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ae677a51dd97aae2217a9edbb9dec5", "guid": "bfdfe7dc352907fc980b868725387e98223f12863ded91328434db598d206d7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835bfd4690001b46bcea1808eb8be8008", "guid": "bfdfe7dc352907fc980b868725387e988e9abb015882b880b1e4ab688d60f352"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847bfd7cc6cf9d2e5cd641ef141b0f7f0", "guid": "bfdfe7dc352907fc980b868725387e98661de858954f44503f4b97da9c19305f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce627366b372c5c6593cea7dbd04e262", "guid": "bfdfe7dc352907fc980b868725387e98d6be21c78ef9e0b49a07a030feab0bf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871be7b7997106e24f0659f72c2fa946a", "guid": "bfdfe7dc352907fc980b868725387e98689a9af01b31888eb1250c5db805e848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a961d0e992d889598da4b93e3fa86fc8", "guid": "bfdfe7dc352907fc980b868725387e98affbfba9db941d6c76bffc630c373a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456f4ca81438d9a7cf6dc5a86e915e63", "guid": "bfdfe7dc352907fc980b868725387e983a6c8ad0c17e8656d3dbfc0c9e10b1e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9703d349be7b92b84b8f62a0f18d374", "guid": "bfdfe7dc352907fc980b868725387e985c166cbf4d1f8bbfe8f4c759f9e96830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e943622e5557755605f206852379da", "guid": "bfdfe7dc352907fc980b868725387e98f8b3621dcc5527e1f245112fcd084c5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e8b41c5512b10955bba38e65e900f9", "guid": "bfdfe7dc352907fc980b868725387e987e36bf993c52c98b75979b0e904b26c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997175ab3590821aa8b3a2bfc94e8932", "guid": "bfdfe7dc352907fc980b868725387e98b8bf7b17bb8137c217ba4b88a2485ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef92bdf8ee2607d36607ec5a6e3645d7", "guid": "bfdfe7dc352907fc980b868725387e9800e1672ab66f7de4c5da96dd676ae24f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e3d9705e284870035c1b0e19fdf59f", "guid": "bfdfe7dc352907fc980b868725387e982652cc2ec8818026fc1a8dcff5535fb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc98da639328eca2252a3877a100270", "guid": "bfdfe7dc352907fc980b868725387e9873e53dc9a6cde35708cc59d089490642"}], "guid": "bfdfe7dc352907fc980b868725387e98b0b3dac95d1f3a3bd0e52e1b59ff87b1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98d347ecbd2d105d33f0d3dcc195fbb8e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98fcddd57b70c8631613981215e9370b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e98f046ccab0e9996f30349ff22683e3dba"}], "guid": "bfdfe7dc352907fc980b868725387e988d2c288083d0cbf37c55c8c90592c93f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98982dc1214c231fa3e623624029f3ea27", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98a22760b9bbf13027de33410d6031e1f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}