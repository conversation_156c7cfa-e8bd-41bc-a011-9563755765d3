{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848851bc6898143b4be19a590b3474299", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8a605a5f8f7976e229344485149acc6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a47a9f2e1b9d82d46376f41fd515ea42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b6a601554099bf01481401d1d60bb98", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a47a9f2e1b9d82d46376f41fd515ea42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897fcfb83afa8c5efef78e1538c094814", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980cdf42ab708735378317494689440ebb", "guid": "bfdfe7dc352907fc980b868725387e9873a2b6aa281e35cc22d1370978b5ae04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e0a35934dbfb2fd6ca4b40cd35ed907", "guid": "bfdfe7dc352907fc980b868725387e9800f95e2f0048fd0747d2ba2474eac31d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccfdacd3bbb2ad7ff8dfcd1e4b8a42d", "guid": "bfdfe7dc352907fc980b868725387e981114790fb79b00a7d13a9e8b815fd8f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98743e85da9f1d08d7e1f1b4f7f2ae7302", "guid": "bfdfe7dc352907fc980b868725387e986107818473ad79a79d3f29f9ab0388e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678048f7e9e4840058a868096d37aa94", "guid": "bfdfe7dc352907fc980b868725387e98e2db2a4a4d5b3a24376530678b215277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2a83b084e1cf090284f30b85d5422dd", "guid": "bfdfe7dc352907fc980b868725387e98a23925bdb9a6b2e1f27d0fb75ed19397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a6352a4e74a54c5d046a629364ce29e", "guid": "bfdfe7dc352907fc980b868725387e98da809168f84f04b23d09b50cc3b63e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818edb11af55dd28960f804584ebed4e6", "guid": "bfdfe7dc352907fc980b868725387e98c561c201fec9d73e62d2647c91a3f392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6c2e710616f89dc0a92aa18b23703e", "guid": "bfdfe7dc352907fc980b868725387e98dc7f42e23271a625fce90d855e930ef8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc670cd7fb0bcb082778b22779ee4ac0", "guid": "bfdfe7dc352907fc980b868725387e988d8e360f51294c4bb970a21323882947"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98944f4c6e7e532f8f8f2bceb654a6afe3", "guid": "bfdfe7dc352907fc980b868725387e9880f243e3b0e1a622072e43014a0a6b7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d24abaaf55053f2130b9000f1f7b45b7", "guid": "bfdfe7dc352907fc980b868725387e984197cdc1d20760ac8e33955994cc6d36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98931daa1e8577ffe602a5da4ac1f978d7", "guid": "bfdfe7dc352907fc980b868725387e987b25b4c4ea98641627f65a8c53160830", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b7c874122c9c0bfbf9adf767f1b1b5", "guid": "bfdfe7dc352907fc980b868725387e980b779f84b57be0d0092639d80b42b09d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98037b57daa39eb8c52a834e6bc60e8a91", "guid": "bfdfe7dc352907fc980b868725387e98020173df0e967e242c80a8529caa929d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b2bd074db13c6cbe32d21163d688e87", "guid": "bfdfe7dc352907fc980b868725387e986d488cbc38053f5f09df7b1a0971a95e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb46b38d541a4ec4386bce93b62aaa3e", "guid": "bfdfe7dc352907fc980b868725387e98eb7638e34b4a10b42234bc9c7a75cf99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f8632588e6a3c78f88a5e02b10b750", "guid": "bfdfe7dc352907fc980b868725387e98b3a544c9bcc079917773988b4d5c27bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dde10ed66eab3bd5bcdc6b81a5786a2", "guid": "bfdfe7dc352907fc980b868725387e98fc51270367e53f621c62e34ad55ac7ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832444aa4b02acbb99645b82e6e1d3034", "guid": "bfdfe7dc352907fc980b868725387e986a11c9cfa0d62fd0a77d0f763ed403c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986587f2d14bbce4665ee614f749b9cd2f", "guid": "bfdfe7dc352907fc980b868725387e9873e0ba8f2ad3d30ce0eb4f9a1b5db06a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4c1242cdbe787fb055e226e4830c87d", "guid": "bfdfe7dc352907fc980b868725387e980a6b76f6b24998469ac5a16d9dea79f0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98295a807b242b81fbd8a267b67874b246", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e931aa824f11d67f15d67872990f7a3", "guid": "bfdfe7dc352907fc980b868725387e98c96588b6f46a7aec7ff0948c13b1f125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52d0e30a2124b02aa1bd9ca0e95d143", "guid": "bfdfe7dc352907fc980b868725387e98721af57f307ad35a5a4014017274e795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1dd12bb3af6d9a52aa7b33c800f29a", "guid": "bfdfe7dc352907fc980b868725387e98332dd048887bed53e6d3c3b839272615"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888078a99210eda010bb83a22e5c430ba", "guid": "bfdfe7dc352907fc980b868725387e98c8d75b14df039722cf34198844361673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ede59f1a9e05348dda1f1afaad81930", "guid": "bfdfe7dc352907fc980b868725387e98a57ac501503c76d63f6d6366276fff58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83df9ea4b0b4c9eaa01d39513f9ca77", "guid": "bfdfe7dc352907fc980b868725387e98e95373fbad2ee2dd85d51ba78441b759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885ae5e93edbef90a13c546a3a508e6d2", "guid": "bfdfe7dc352907fc980b868725387e98bf88ebe222e0c18926d804b50893d715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c52a9fb8f1779afc2f22562df78cd45", "guid": "bfdfe7dc352907fc980b868725387e98c38f0b420798b719ef3b3f5f82825c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c486a64981ec47a1ed4522e162e192", "guid": "bfdfe7dc352907fc980b868725387e98cf7e345a26fb5a41a6555a1f17ff6e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989636ccd0090ebd99e651d9762eb318c5", "guid": "bfdfe7dc352907fc980b868725387e98a32fb2ab11c4ff01164ba5e13d8e97ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa0d1d273fbb44b372177292395d1a7c", "guid": "bfdfe7dc352907fc980b868725387e987eb5e6d80c9484895e96287b8e140758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b388c1338626644411b8c0870110cdc", "guid": "bfdfe7dc352907fc980b868725387e98949ed0ff15b095c9b3586a7a50790af8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c3bb273cc4d7a9c28de5b6779a7422", "guid": "bfdfe7dc352907fc980b868725387e98b449b9c31f16b1db29817098cb011c1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f1ac323468b1ddeb95d0886f87cb099", "guid": "bfdfe7dc352907fc980b868725387e98767f347abd001fdd7d015067af3c1ba6"}], "guid": "bfdfe7dc352907fc980b868725387e981bb9d67b9bdb65c6bc78e1b009a7e4d5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98ad8b8c31d2454ad8c49a948df7c65f82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec82dece765d451054cf9546cb2f0d4", "guid": "bfdfe7dc352907fc980b868725387e98815be987e1171bb1fd4eeb3fc78bf3a0"}], "guid": "bfdfe7dc352907fc980b868725387e98fb700cc2c6b0c853e817dce683dd4fa0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c20991a56eeccf03983e0afe8b864691", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e988cc864b89684e48fd51c516b4a17105f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}