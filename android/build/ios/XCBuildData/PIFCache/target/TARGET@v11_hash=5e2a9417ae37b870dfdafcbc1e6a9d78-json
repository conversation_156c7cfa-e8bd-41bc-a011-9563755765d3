{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a7c43962a132e0251ef9aa963ebf31b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985531c4b7ed545bf718b619d958c7a67d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f75ec35ec7792f062781ae6d2cbd470", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98951e1b34709842776eb04f52161e9033", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f75ec35ec7792f062781ae6d2cbd470", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9c48167191556cab5be56774b3ff4c6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98637bc8c31b787b2369a9b5f16393e71a", "guid": "bfdfe7dc352907fc980b868725387e98eb0051d14ec821ae7103243510db906c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f516e9aeca4b1da636907817e2cc62cc", "guid": "bfdfe7dc352907fc980b868725387e98cb6869be6a75a27150b1a79460b610dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a536ea44dc067c4db01cba0081bc50a6", "guid": "bfdfe7dc352907fc980b868725387e9856d0426febd6c4f276d35866f279d0e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ccb3d0fe3d28aefbbb9387f961d466", "guid": "bfdfe7dc352907fc980b868725387e98a7d5da174383671ab02fad9caa0854aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb9a6d136aaf9050287aea0fb8be62a", "guid": "bfdfe7dc352907fc980b868725387e986c5fc7b0591c354e20da4108c2477da0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806b86e4ccc098ba384d69e49aec83ea1", "guid": "bfdfe7dc352907fc980b868725387e989e78dfbb3eaa7f2dc7d35b264974430e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f4156dd77cc601fca2fc87586f7435", "guid": "bfdfe7dc352907fc980b868725387e9866f0847c3df8d0dee8d1518c6524e8ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9b82d8c471d6b311e6fb91666cf2bb", "guid": "bfdfe7dc352907fc980b868725387e982c1ecf86c530ed1b39348ea55c1cff5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c3cce0d2ec5eb7555d5bb463aef06b", "guid": "bfdfe7dc352907fc980b868725387e9873238ec330e1862b0081169b3e8cc24e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ecb56de30ea6f54f66b93dda2e9946", "guid": "bfdfe7dc352907fc980b868725387e98faf99ce7c3a2508737cbe416332c5704", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c490313926d24b12e2c7313d6b9bae", "guid": "bfdfe7dc352907fc980b868725387e987d4206d3fc259686b3240df04c06c137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5427848d572b9b95b032830f38367ca", "guid": "bfdfe7dc352907fc980b868725387e985681c60d4dc7652eff5c5cd7235a1ffd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea881ed2b535c05e6f70090c5eea800", "guid": "bfdfe7dc352907fc980b868725387e9846a9680d2d92f4586d9e3084fe11a9da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302173bea1d75cefed50ddea9b35136b", "guid": "bfdfe7dc352907fc980b868725387e98d03d10e0b984b65bac9db34392a2ad0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf466c70cf99aea23798cbb9ac772189", "guid": "bfdfe7dc352907fc980b868725387e987164eee56ca7e5ef62a0c33e2f83aebd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb7d1005125e7b94cbae95de617d34f", "guid": "bfdfe7dc352907fc980b868725387e980ee476740bcf7b217bbf88c5008dbd66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac780d7be420469e164ada1c011f4750", "guid": "bfdfe7dc352907fc980b868725387e9893f450bb84a680d072c8117289dc75f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981508b4547067f354bc6094efbbefad0b", "guid": "bfdfe7dc352907fc980b868725387e989107bdf3901a401982cf122ef634cd19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2d97f860dbcb37ae004506da6f506c", "guid": "bfdfe7dc352907fc980b868725387e985272a2bd902279aec02ba3e747b983f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc47784f96bf3f7acd7bd9adb91790f7", "guid": "bfdfe7dc352907fc980b868725387e9804e9c55cdca0557622c7c1dd58fbc1fd", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b900f64aa469b5c0286bf137006c26fa", "guid": "bfdfe7dc352907fc980b868725387e9852e6a20b82019917c2c7e707692c4b40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb268f92a9cd808d248854ddc686bb12", "guid": "bfdfe7dc352907fc980b868725387e98d1f69a10a284d641a1cb35eca9db5022", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1b94202fdc73127ab18b0ed9c131f0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850b25eab51ed3ccfc420d551b0e96b64", "guid": "bfdfe7dc352907fc980b868725387e989fe14964e925fa087f30c220e43d3c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef84e005a1da4ae1700e9a7e1a75211", "guid": "bfdfe7dc352907fc980b868725387e98d305cb904781d449b7d48af080b59460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a78c971407d24243e82a3c58eea4344", "guid": "bfdfe7dc352907fc980b868725387e9886e58649467e3cfa9f3938a98252c390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46781a8e01fc706b626f6a051d3975b", "guid": "bfdfe7dc352907fc980b868725387e989ac53f091f6abfc7be70da8327accbb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862966cc3dfe76a902ffcbd254703768a", "guid": "bfdfe7dc352907fc980b868725387e98f7ae4e104b2beeb8404b12d7c51f227d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64d327698f0c28c3654203a05446335", "guid": "bfdfe7dc352907fc980b868725387e98e9422b9de066c15cb157d8b54bdb8e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818edec3f91047dcd04243f22130b64ea", "guid": "bfdfe7dc352907fc980b868725387e982fcaecfaa6432e7fcd929ea6be5084d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a9755c27d9b48bf9ddcaa90eda9361", "guid": "bfdfe7dc352907fc980b868725387e9843e5c84cc21bcfff25cc16dc4179c18a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816afe72f7fa64976ef187296c5a0e210", "guid": "bfdfe7dc352907fc980b868725387e9843654f43d60a6f0d48c5f0682a7e8c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987531075dd37a7d35a3fe1872209c74e3", "guid": "bfdfe7dc352907fc980b868725387e98eea0de7c501f3ee1e721b3409866d026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8b214748c0505d702f07d1d43ab8ab", "guid": "bfdfe7dc352907fc980b868725387e983dddfbb907d4e6aa62ad021413b79e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d05b6f5006b30f00b3393838a58809", "guid": "bfdfe7dc352907fc980b868725387e9874fda920d86f6d08f185ba4a350874ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a505ae8bf7863a5fd6c45fc956eddf7", "guid": "bfdfe7dc352907fc980b868725387e98cefe7bf97b23510e5c48b796916932c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b78f5c821ebef804f5f6058116c515", "guid": "bfdfe7dc352907fc980b868725387e98886e0c2c40f0312a9a66772addc55a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379f99cbbc71c294565546dc13469bda", "guid": "bfdfe7dc352907fc980b868725387e9836c03063104e1ad7b2c5c761afac8385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875eee71a6749eddf9e17fb90adddc963", "guid": "bfdfe7dc352907fc980b868725387e98cf7c913996695f4cb6f6ad729a40315f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98019ca1a0b2dc206183e9dc758a161ceb", "guid": "bfdfe7dc352907fc980b868725387e98205fe7ab9682978e4569d46ab295d3cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea2817df21ba7bfd8b083a1493119a1", "guid": "bfdfe7dc352907fc980b868725387e9858ab980f4cd59bb86148085f3a4920d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983480cbf4eb1ea31c230ec55b52c21131", "guid": "bfdfe7dc352907fc980b868725387e989d61082c3b4fbedcd46b82e5f3ac938a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45455caa98c7509a6417e2e356f5e7d", "guid": "bfdfe7dc352907fc980b868725387e982d614627ef737d0bdfafe8fc467d9366"}], "guid": "bfdfe7dc352907fc980b868725387e9845a64ac6a84b97fe7466002dac8568dc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98b1d5b137b895c52ebb436682a7774cb7"}], "guid": "bfdfe7dc352907fc980b868725387e981609d4c229b01cf45f4980c37f61cd44", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cced392ce5e79e42bfb67f8fa221d54d", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98b18e175554b6851243bc2da834f60f5e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}