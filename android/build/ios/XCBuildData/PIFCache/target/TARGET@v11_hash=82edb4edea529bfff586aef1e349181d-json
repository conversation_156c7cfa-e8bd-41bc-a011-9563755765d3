{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98695d20417f456c89e43774853a50d959", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98687e59883890482e520620cc894df5b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98687e59883890482e520620cc894df5b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea70217edfe180ed4fba0559fbd23dc8", "guid": "bfdfe7dc352907fc980b868725387e986c642d959ab0b3a74cf2dc9af7ca6575", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b45366e74764a92b6d483f87881f4f", "guid": "bfdfe7dc352907fc980b868725387e98589145a49a93692586adf7e483809213", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984529e6505f6e5552809baad0636e3eca", "guid": "bfdfe7dc352907fc980b868725387e98a3ab02bc76ad21854a394106652b714b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e84e1f33666899e9edad9e12f0645df", "guid": "bfdfe7dc352907fc980b868725387e98aac7aae5133e3cb198d493e2da7edb95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6969e46d8b46c393d2cc1c779952d0", "guid": "bfdfe7dc352907fc980b868725387e9833f2875a39b769b84ae89ca0c330fa89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b398fb782bb6c280bcd25c7a6ea4803f", "guid": "bfdfe7dc352907fc980b868725387e98095355b5d46bdead464a8ab3405237eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0e65a9e79a1c86068eff5fb7da35fd", "guid": "bfdfe7dc352907fc980b868725387e9895642136ab6b437c709e2eec70dfc1bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98104bd721e728093252ecc98792129356", "guid": "bfdfe7dc352907fc980b868725387e98bb70de040d11f52e08c72407a0b7f2ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b1da2a1aba46de3a2e539fd500f7fc", "guid": "bfdfe7dc352907fc980b868725387e98ac2b3ccfaec700b91880d00d1aeef55e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d789b14bc377dca9571f66148165cd9", "guid": "bfdfe7dc352907fc980b868725387e98d3467234214a5fb1717e5658ac600e74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850f007ed7c858ba194a88665ccae6a5b", "guid": "bfdfe7dc352907fc980b868725387e98dedffa98a09362a0bedd451061872db5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1620086429b643bb00227e09c782420", "guid": "bfdfe7dc352907fc980b868725387e9849ab0df8cb3c38b56b0161041e5306d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981317de0991cdaa5df1d1ed364e8bbfd3", "guid": "bfdfe7dc352907fc980b868725387e983a81ca54b456f8a909088d5b9f6a5cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d5bcceb90b9d56c64794cbed0205df0", "guid": "bfdfe7dc352907fc980b868725387e985d891ce763262ae8564bf80524257b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04fba09cd45ebe4d7d2384c880492ac", "guid": "bfdfe7dc352907fc980b868725387e98cbccbcf2a5cb8519cb01836a36580c4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833543ea82c64ee700a168b9ea6659a5c", "guid": "bfdfe7dc352907fc980b868725387e985ef2dd14f9336c47ee0574cf2ceb0545", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98271905dca14e425f4a4f905c82dfc53a", "guid": "bfdfe7dc352907fc980b868725387e982fbf638d2a2d7134f5810cfbc8f87426", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a81f7d009ad5491f69011851e028e4b", "guid": "bfdfe7dc352907fc980b868725387e98047d9c17e150a48a150ff6780ab58cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c738d15d3d4c7aa841e0068c2e898aae", "guid": "bfdfe7dc352907fc980b868725387e9871f01842cd1e75aeaa24ca58c60abb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddc967170f8545d51eec6179461be379", "guid": "bfdfe7dc352907fc980b868725387e981f7455bdb1fbc4da3a82d543726f3d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c881d51b26570cffc1150ec94bdc12e9", "guid": "bfdfe7dc352907fc980b868725387e9804fe3192a5f30d2e79b6aedb3464d216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d577d4e844b6fccb2762685736162bde", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b4451033ec8d911719ccfe82fdfb738", "guid": "bfdfe7dc352907fc980b868725387e984028cc6d365683630f975e4546c33dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f489c69f03af49d30de65241c9d357e9", "guid": "bfdfe7dc352907fc980b868725387e981975ca6e74bac51e5bc3ea7aafa790b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3de2080a5b4b723eb9f8dcde3ee171a", "guid": "bfdfe7dc352907fc980b868725387e981c62e3d0329d35ceb953521f987e0874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d0abf8c1c47404a9386ab1e1a90f3ef", "guid": "bfdfe7dc352907fc980b868725387e98cce31e680bb7749b81cdf0cf8fd13cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e538e372239ccf621bdc8711c2a4ef7d", "guid": "bfdfe7dc352907fc980b868725387e986b03888db3bf0f6e1886d46387481e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce63b9858fab9975b16f1a7380f3d336", "guid": "bfdfe7dc352907fc980b868725387e98fd23e7a517c3bad0fe25bd525f9f575f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886f5a8aaf2d5e9fb55c9687d83cefb02", "guid": "bfdfe7dc352907fc980b868725387e980463d32e76f26f0fea0b1e1523e26914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849283a011cdbdfbaf1bb26ec5c540332", "guid": "bfdfe7dc352907fc980b868725387e989871f188dd1e4bee67360343550cc279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845bc376f920348fe4e875aa9c96e6298", "guid": "bfdfe7dc352907fc980b868725387e98f61dc4e84c1074c6c51b018070465690"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}