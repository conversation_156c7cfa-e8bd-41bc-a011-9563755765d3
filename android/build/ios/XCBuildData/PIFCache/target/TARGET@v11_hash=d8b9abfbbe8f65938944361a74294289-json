{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985aa89111f96ff38b2f062c3c485a1246", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1cc874a020a479547cea61eca3df2b6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c36a68e096ded61935fe2416df60ec00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d47dd7ba22ca9f0b4fa96fadfb06490e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c36a68e096ded61935fe2416df60ec00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6188ca4110dd4031a8a69eedacc01eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e62f16f988b52f4838ae83381cc98908", "guid": "bfdfe7dc352907fc980b868725387e98331fbb674ac975c8a3a5a53edc1c2175", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b6db95eabd6955f1a9f15c725dc528", "guid": "bfdfe7dc352907fc980b868725387e98a7e4d50623b959c1cd7adcecb21ca886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1070bb90ead92a336db302544f22986", "guid": "bfdfe7dc352907fc980b868725387e987dba2a5d1feafafedef2b0c9f55bb460", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff5bc964918368c1d7bffb1cc03260d", "guid": "bfdfe7dc352907fc980b868725387e980cec3f1b4d8ecdd30fd0ff2438484b6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad933ed5a007e747e183b9db7c0ce7d9", "guid": "bfdfe7dc352907fc980b868725387e9835068442889fe0b437366132b22079df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51028f5fe26ae0400c3c4a719afdfb7", "guid": "bfdfe7dc352907fc980b868725387e983c8770dc5a9f5c75b3308585dba0b02c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981febe90b0e5132d601aed4e7e0a1b3f4", "guid": "bfdfe7dc352907fc980b868725387e982b0c99df4838438f2b535abe008d04bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987622509f0534e44fe2e22cf6d7a49238", "guid": "bfdfe7dc352907fc980b868725387e988b7e38c99b11a0aa1f5385da00caedfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec41622f9611af83289dfd33ade2bba", "guid": "bfdfe7dc352907fc980b868725387e983f9680ddebc6da30bc7af33c4385d5c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaa2db5ca98b1a490e0f1c03d40edd3f", "guid": "bfdfe7dc352907fc980b868725387e9881565eaa9a2e64b5e67ec7f4bd8b3842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba35a1d67dd6b6a726550f5a812b486", "guid": "bfdfe7dc352907fc980b868725387e98722153ab41e560d5fa4915e26372ae73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9c9fd1bd45d6fc8dafe885fee58105", "guid": "bfdfe7dc352907fc980b868725387e982116a246e41031df8f3c2d8e2546699d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ae289951d04d823a67b3b5957df0a3", "guid": "bfdfe7dc352907fc980b868725387e9889e6ceb73be0ea446c5b1d2786028fca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f64d9b34003fac9d3811b5c433be14", "guid": "bfdfe7dc352907fc980b868725387e98ce88eca1fb3a50c2d4f1bff4c19c5518", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8d8f261b059cfff569b8f614a57aa9", "guid": "bfdfe7dc352907fc980b868725387e98eb2a1e8ac5d22c9c9a05ab88dc1536b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d29cadc73e836c931f6ba08ec39c0d", "guid": "bfdfe7dc352907fc980b868725387e9876463921c331a13cf3891f5449db8f2b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a1d921d86947a34d02991e7a955939a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b4d47a9acb2be8ebf0923aca40309899", "guid": "bfdfe7dc352907fc980b868725387e98a962f0c1765e5b039f4674fac8c2bb7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834715095b8974c2d2979c175ee28c226", "guid": "bfdfe7dc352907fc980b868725387e98d18fd70129e0828c70fde33d2a933940"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c132ca79abb5e9792b6040d4c522a7", "guid": "bfdfe7dc352907fc980b868725387e9820a5b877d2094458dfb0fdc1041cd889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d3eaa7696eef327adbad5abed25477", "guid": "bfdfe7dc352907fc980b868725387e98e3ce676ef71bc4676465a9097a866174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc53427a64e7be2db4c268cb6577240e", "guid": "bfdfe7dc352907fc980b868725387e98e06a7596bd73d152d630dc1e22785882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34570feb2238a8b0dd8159396464f68", "guid": "bfdfe7dc352907fc980b868725387e981de04837f7bd45c34508157ce78c05fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888aac9723845d831d8f089f61365927d", "guid": "bfdfe7dc352907fc980b868725387e98c59068fa8503cad50b4ec66ba6fb7142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5d6570098161554278afb8d35d68a6", "guid": "bfdfe7dc352907fc980b868725387e984da8f26e498487d5ce86654f913ef583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52788f83f6e17856b566b072cdf93d4", "guid": "bfdfe7dc352907fc980b868725387e98402bf054c1bd6c27dbb825278bf5f59b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee6025e45378daf8553955b478e1d55", "guid": "bfdfe7dc352907fc980b868725387e987e45c1da7e2e1562befdda7c7447895c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f988fe16e493526437a160ce3c565657", "guid": "bfdfe7dc352907fc980b868725387e98930a4b42776df9c5cc054595628d6de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840698ad6ea50a9265041fb1e867bc8d6", "guid": "bfdfe7dc352907fc980b868725387e9882e860403262960b7a1d00f8ff04abbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889559db055a7a291e9b887484448fa76", "guid": "bfdfe7dc352907fc980b868725387e9836833e462924bfe3c3879d35dfa5a913"}], "guid": "bfdfe7dc352907fc980b868725387e9888ddff36bfdb8fba1896fffbff290975", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e21cb5e95267ac01a60b3f75a1af0e91"}], "guid": "bfdfe7dc352907fc980b868725387e980e1ce268bfb1c82d099ffdb3b17b4474", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e20954141ebd835c2535267f91ccb9ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}