{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a1fe98e6b43b47c6e6d5f54e0b26e47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d313f3a405f18f51ec743b9e645f9a7b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982efc8a732a2fa8b253ccceae5762e519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a66427912918367c0aa3fdcf316ff20", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982efc8a732a2fa8b253ccceae5762e519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea6812989333cebf9ff123fc7ff0a819", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894a422860384b9b66366ce8c0c4cb5ae", "guid": "bfdfe7dc352907fc980b868725387e98ca24d4d1adcb77d17db34981f56611d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6f9ff972f3cd00a1c6ec875931fc91", "guid": "bfdfe7dc352907fc980b868725387e985fa77e9ca224cfcc4c11be225e2ff130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc53f85d05f6f21522435372bc3184b", "guid": "bfdfe7dc352907fc980b868725387e98ba7ec6911448f7c993bf7c35aa63abd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983059d93c4990b8b0353b686c9186cd1b", "guid": "bfdfe7dc352907fc980b868725387e989ccad939389794cac76a4c1360d21618", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a80e3ea7ef674e913c4ee05475da0e0", "guid": "bfdfe7dc352907fc980b868725387e9881fc910abd7925a77c8bf4a4df0f4e13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884bcb7340d43cc7f48dddbc7a60be8c6", "guid": "bfdfe7dc352907fc980b868725387e98651fb0d9259abc0d110ff493bf7d6243", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4226552e7c6654865ebd550bff79623", "guid": "bfdfe7dc352907fc980b868725387e98deef36062d8dbffe41ba076283329b0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f620ee2caf00079abf986e5b67bc8fed", "guid": "bfdfe7dc352907fc980b868725387e982bc48e1935d1094a8f0341f0bf618eb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333e6fe3ed81e4b376235d4647a19dac", "guid": "bfdfe7dc352907fc980b868725387e98de9176ab3b7573e653cf3fdf844dbfb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814fa295b20f0e14ff11c19d564568987", "guid": "bfdfe7dc352907fc980b868725387e9883e76b7844856e48cd8a54c61ebbaf7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae3177932eca589df23baebfb1a760e", "guid": "bfdfe7dc352907fc980b868725387e98e9c48c59674923e1af2b17282d23d946", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca0f48c69f4cd419b0b4eed403d5276", "guid": "bfdfe7dc352907fc980b868725387e98008d11878fda1cd0ed74bcd603d30c7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657b3542c83d4f47346bc5586f551e42", "guid": "bfdfe7dc352907fc980b868725387e987530749887840ffbc2c6ef602d8606f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ccdc2e38d23ec0a535a21ba396162b", "guid": "bfdfe7dc352907fc980b868725387e98457093246ce093e387edbcd5c2e8f77e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810b2c68c30bafb96bc77ed3dae87d1a", "guid": "bfdfe7dc352907fc980b868725387e98b0ff60ddee15ca1c8d8d48f8751c5a69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851937f79b076672cdd58990f197a66c8", "guid": "bfdfe7dc352907fc980b868725387e981e3e6ea3d8b17573829793b61f7a5265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4e2314a54022a10e45506219095017", "guid": "bfdfe7dc352907fc980b868725387e98c841b64722337510a3b261ca9d139df1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd6d0a377d7887bf8c105b2f6b3ffc70", "guid": "bfdfe7dc352907fc980b868725387e985aca72c922b879b2802c9b3c8de7bcea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9e653d7a8aa8e28d322f3179bbc982", "guid": "bfdfe7dc352907fc980b868725387e98f69c483a18bb5a65ff755b9f50da5dce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd893ad91f9d7b88ed73835d752561a", "guid": "bfdfe7dc352907fc980b868725387e98e819690b5f1ff1b63a9d117d810ee42d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7746b11dd069111c5f8f88913a28a00", "guid": "bfdfe7dc352907fc980b868725387e981319720ce4beeab81498e08717534c0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb5618e0b6e53e9b61ee5ff2eb6cf1ed", "guid": "bfdfe7dc352907fc980b868725387e988e38ce4493d3639730883ab111371f9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878aa41b952bc97353f5b21e57d7092aa", "guid": "bfdfe7dc352907fc980b868725387e98f8c4996acba8236bc765e8142a9baa8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7fa8b94aa45bfc8a194ee2140f9b7af", "guid": "bfdfe7dc352907fc980b868725387e988970c04efa205300227741bb79c14893", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6b0fd4027e2e970403ffe37a6e63db", "guid": "bfdfe7dc352907fc980b868725387e98276ff16aef2cdf779f863b706fe267e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d984a5a167958051d9e2010f3b7867d", "guid": "bfdfe7dc352907fc980b868725387e989b57e83601003b8d3877d57ecb809502", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989adf4c632240162788ab413cdecb3f7a", "guid": "bfdfe7dc352907fc980b868725387e983b9f2935a6ec70670081fdc4cbb3fd78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f41722adc572becdee9e0c476629c5", "guid": "bfdfe7dc352907fc980b868725387e9847c7a1a4cbcf3484df379ddbbedee518"}], "guid": "bfdfe7dc352907fc980b868725387e98f04d38417f775d1dec316db6272dd8d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847ace5f65260b4d3d64e2acaffb07f29", "guid": "bfdfe7dc352907fc980b868725387e981f7048399d47c543a63fdf22e9342620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881bd12bda0f7060691bb8b971fefe3e3", "guid": "bfdfe7dc352907fc980b868725387e980152d417d5e2f9966f7b759bd9291a4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e24a34f4ae5d89b1d200e6060e58a65", "guid": "bfdfe7dc352907fc980b868725387e9899170ad1a811f18bc5720f592f741f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ae677a51dd97aae2217a9edbb9dec5", "guid": "bfdfe7dc352907fc980b868725387e9889f70f23f02f7e1e240eedd8b387e5d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835bfd4690001b46bcea1808eb8be8008", "guid": "bfdfe7dc352907fc980b868725387e9848eefbdf7484090644b5cd1276aa1f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847bfd7cc6cf9d2e5cd641ef141b0f7f0", "guid": "bfdfe7dc352907fc980b868725387e981ef01f38727ef228bb746c2f347b0eaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce627366b372c5c6593cea7dbd04e262", "guid": "bfdfe7dc352907fc980b868725387e9809596e387a750f267f275ee47a0d472f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871be7b7997106e24f0659f72c2fa946a", "guid": "bfdfe7dc352907fc980b868725387e98b2428036af7495b9b1916e9089089fa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a961d0e992d889598da4b93e3fa86fc8", "guid": "bfdfe7dc352907fc980b868725387e98a6a6fa66b47bd68ac786e5863cdf81b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456f4ca81438d9a7cf6dc5a86e915e63", "guid": "bfdfe7dc352907fc980b868725387e98dff5426054c553ea8a13262f2dc6dda9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9703d349be7b92b84b8f62a0f18d374", "guid": "bfdfe7dc352907fc980b868725387e98209f5126b4b04928d424bb668c9c50b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e943622e5557755605f206852379da", "guid": "bfdfe7dc352907fc980b868725387e986828c164034f29cf35bbf7eb0ab1b15f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e8b41c5512b10955bba38e65e900f9", "guid": "bfdfe7dc352907fc980b868725387e9844dc07e4896c64601410e13f9395d414"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997175ab3590821aa8b3a2bfc94e8932", "guid": "bfdfe7dc352907fc980b868725387e986abff199cca1315d52938720a9267626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef92bdf8ee2607d36607ec5a6e3645d7", "guid": "bfdfe7dc352907fc980b868725387e9854cfe3f94aa2a4792714737bda71e8fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e3d9705e284870035c1b0e19fdf59f", "guid": "bfdfe7dc352907fc980b868725387e98168bef820d88f48556d4e830eee29744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc98da639328eca2252a3877a100270", "guid": "bfdfe7dc352907fc980b868725387e98a42fe4e36200f05d02a9c569b93a2888"}], "guid": "bfdfe7dc352907fc980b868725387e984af0f390008653d168421f64cc69bab6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e984ec8ee59e6ace64072e47fa3a236087e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98a685d283b5eae6ae7a92372940f53536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e9860b61b9e279d4855f5dbbd38d28a0503"}], "guid": "bfdfe7dc352907fc980b868725387e983e0b90cbf006d78fba30af27dd943e36", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc8f2bb351941bdda94bcd2e52e10b2b", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9839831a4ba1f8f1df96232eff445be93b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}