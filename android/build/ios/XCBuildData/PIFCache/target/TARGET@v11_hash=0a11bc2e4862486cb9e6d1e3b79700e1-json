{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984967a3bcbc47854f1ce8e6b2dd15e35b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982bfd95f1f48ca6b1348a66db18d21287", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980fccc030eb3ab11766c104aaf95b7ed1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5219d7330cf2e66b18607e2989676e4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980fccc030eb3ab11766c104aaf95b7ed1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e842831931c06a0076ce7ac97580821", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff5220de64d73912b8a5fef64db15b51", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b5a24390e4bba61db1c7f3e737bdb20", "guid": "bfdfe7dc352907fc980b868725387e9839b024cf6f83e355d78693988f86e960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2bc674cd7a4463cabba7ed9f954b979", "guid": "bfdfe7dc352907fc980b868725387e9812f7a65b2ae225060f385b33f4cc27cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1021df62cc48a32d043a052a540ae89", "guid": "bfdfe7dc352907fc980b868725387e98cf57cf807fab8d1d41893eaa23936b64", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f518e0b893993a724801a5333d335bea", "guid": "bfdfe7dc352907fc980b868725387e982f17bda710283057e3a2f641f82240ff", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd3124a52c5cca8820e2dfd8cb829b66", "guid": "bfdfe7dc352907fc980b868725387e9853781a76a831ac4371385a899ac2dab6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98814fb66dbc406fac5a55bd384b720f00", "guid": "bfdfe7dc352907fc980b868725387e98a9edb3addf901ee4b8cd1c9372116db3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803f293dc5c4c3057466fad82b993c0b4", "guid": "bfdfe7dc352907fc980b868725387e98fc99e36fa566f7ebc989ab7471f1c0aa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad02225a0d0eea0ce3a4ee9de68dc0c", "guid": "bfdfe7dc352907fc980b868725387e98f0754fd8c182f3ab5f9b4fee559866c9", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f27b87567c4febeb9baceb9ecffca40", "guid": "bfdfe7dc352907fc980b868725387e98d90387914a9a0610360a4b491ce27bfa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a13db7467c0ce1d4937b7a3bb02ea4", "guid": "bfdfe7dc352907fc980b868725387e98d06b7724754cb0db0c11b1f9d6e4ca9d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883d62bfb83b5fa3e6211969ed77fa1af", "guid": "bfdfe7dc352907fc980b868725387e9802b6f9b7e2b9d09073a239c64c542835", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc70bf809f7393d736f6ec4fa7aac943", "guid": "bfdfe7dc352907fc980b868725387e98b40b33a0614ee8c9e8ab2cf12b36c616", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7172abe132fe9f492dfb8a2de2ba622", "guid": "bfdfe7dc352907fc980b868725387e988aaa55c16edfeed5e70b39574fb689fc", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9d2a42042649acc6820ad1cc1e5a8ec", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8e36717278313fe54bcee2d93b4990", "guid": "bfdfe7dc352907fc980b868725387e98ec68cebc430d1eed77150a74d3f32575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fbba8c247b0b7eadc5e63112ccc253", "guid": "bfdfe7dc352907fc980b868725387e98bc05c5b13b26a63047aeb4406d12fc6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a891808f9741c318077f2b83c2587ed9", "guid": "bfdfe7dc352907fc980b868725387e9800acf63e9e88478ed882e55dbe19ef90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98857a2af0c617532699884ad572304aff", "guid": "bfdfe7dc352907fc980b868725387e9889d4ba304d81b02aec62eddb8a5a0e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982899cedda54ddaa6107bda74c574a3ce", "guid": "bfdfe7dc352907fc980b868725387e9895553a5279ce4ee342b87e2efd751a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466e34ef48e276e7119f3e0c73d16b8b", "guid": "bfdfe7dc352907fc980b868725387e98faa8947fe0996a3d007a03d4553808e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855967fbefad6c994eb4c401417081337", "guid": "bfdfe7dc352907fc980b868725387e9857528f9b732a84025b689c44fb315d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e30970f6ad5d36bb1e56783c07b945bd", "guid": "bfdfe7dc352907fc980b868725387e98699c2ce29e22d171c50712a065420ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894aeb5b1d00bd039ebb08ab45c453633", "guid": "bfdfe7dc352907fc980b868725387e98fa032a5ebebc03157f4ba56755f1db79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af904ae6ba55e324ab0e0501f4576ffc", "guid": "bfdfe7dc352907fc980b868725387e9828c0bc39534369bd1593184250248e33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c53f9e6835ffbf5f11409025f89f7655", "guid": "bfdfe7dc352907fc980b868725387e98064d25dab8dc6628d9eb7abba931741d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3b0c2acb68f57af79622cdb7cdfa5ba", "guid": "bfdfe7dc352907fc980b868725387e98a40e809a1447f6fe921870f9216eb5d3"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}