{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a229475e65e835bc6f94ef757b91d7c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac15df34e327fbec5b481f2b247844a9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc029ec6bb8cef4cf9615b255b41995a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b7d2167998a81968917d4c50002ac7b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc029ec6bb8cef4cf9615b255b41995a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea6696ada555def3d7c6e63b56ef30b6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98878ea9e9bf88088275efa63ae40cf88f", "guid": "bfdfe7dc352907fc980b868725387e989aaea6f8d011ae516b1c98cff3b9e045", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ef0c70597e77d512431b27b5be62d041", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98409a40fcaa4ac756f4e590737e140285", "guid": "bfdfe7dc352907fc980b868725387e98c738e996baa5851393744d9e52078aae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19faf1b04823b30031c7c0323c0aa09", "guid": "bfdfe7dc352907fc980b868725387e9807e205b2b3c7dd4cb6254478cd61c028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a417264350c0cf76372b3bd6bc9e752a", "guid": "bfdfe7dc352907fc980b868725387e985b4e6bf4ec3ff7ad98a69d895a36ae33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98207707864dd5bbf183303892940bc93b", "guid": "bfdfe7dc352907fc980b868725387e98ab6ae8e2e6b012c6c11d93baacfe2b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c51cb8c831199f7dda888b828dd56c8", "guid": "bfdfe7dc352907fc980b868725387e98885881287f988ea445ae04df5b67a396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6c9b3aa0ae761d3b32c024eb0508cf", "guid": "bfdfe7dc352907fc980b868725387e98137c429ca1ed038857c84c9133a69e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea97dbee52efd904171b3278b277a07", "guid": "bfdfe7dc352907fc980b868725387e989a82c6f52a2c075af61ad06408ceea59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981682ec33cffc0c4dbbd507325bec792f", "guid": "bfdfe7dc352907fc980b868725387e989a3eb89264b3a3383db33cdfc3781aaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63ab6b22698ec00715e495f3edc1bd1", "guid": "bfdfe7dc352907fc980b868725387e98d91106d0cf8307d30afe62d35684270c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7ae2924e34788a40bfd0e4c3f412f9e", "guid": "bfdfe7dc352907fc980b868725387e98100bdc067f5b117fa7875dab72d33e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f81bb653af1c5bf83af226b1f4c94154", "guid": "bfdfe7dc352907fc980b868725387e98bdea099c037c762d667ad13beb5da9d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e16adc587629bc7d400b82209333bd", "guid": "bfdfe7dc352907fc980b868725387e980a9bd5458e86540b398e03de73c46a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a832f750baa3890dcd13d09e3ca77c3", "guid": "bfdfe7dc352907fc980b868725387e98e864672ae0d320475d95cca5e6fe8ae4"}], "guid": "bfdfe7dc352907fc980b868725387e98f0b28adc34473848d7f3fd87cc48665e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98d1e69dcb64a786adbb57de3d722424c6"}], "guid": "bfdfe7dc352907fc980b868725387e98ffa471363d693df91dfc620572d3e5fe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9843ef5b872e0224f91d925de3f9d4586c", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98823c8098337288594a6385804bd81154", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}