{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb005454514d92a4d3a5287c9f5f66a8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982eddaa6b1c27fbde3c95368127774938", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879cb3686c843f2bf9d9434062997bc99", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c3e16a718f520bb0b904b0c7a804170", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879cb3686c843f2bf9d9434062997bc99", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9867a66369138cdbb918adfd5bc5a7f021", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d4e27f59635e3d9c737579c56bc432fe", "guid": "bfdfe7dc352907fc980b868725387e98363361c96d6efbe59bd9d70f17bc09db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c714279e90497d44da42c08bf002705", "guid": "bfdfe7dc352907fc980b868725387e98611c11c657384f2069b25b570bc1aa7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e822f8a66825b7e8fc96c6907a4533cb", "guid": "bfdfe7dc352907fc980b868725387e987ac262c666f51bfae2dd66df2a80e419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869123ec693b10bfa445470e87caba231", "guid": "bfdfe7dc352907fc980b868725387e9808200153469f24f89b23dd2c497f58ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4930764c1bbcac368b316730f3ec6de", "guid": "bfdfe7dc352907fc980b868725387e989bb52031883022251350fb97d6806854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989812b07ffa18861effd05d8b06a7eca8", "guid": "bfdfe7dc352907fc980b868725387e9811643cde6e6bf836455d3db00e0e7f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e29ba2e2b23a6deb35c356c7fbb44e2", "guid": "bfdfe7dc352907fc980b868725387e989404ec71d2ea8ed2cdc4097b592366f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec3647fe4577bcfd69c0d0788806af4", "guid": "bfdfe7dc352907fc980b868725387e984716e4676639700199df2d71fff0b3c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953fa36530b602a240f7fb7816ee939a", "guid": "bfdfe7dc352907fc980b868725387e98953e3b3f05c5993085d9374cb1b95013", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d86cb49e239fb033c1de8f64aaad92", "guid": "bfdfe7dc352907fc980b868725387e9889a52120269ac549628f0e61634281ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a162516716484a10e87c80643bab4b9", "guid": "bfdfe7dc352907fc980b868725387e9840fc918b4f44851cc416b816479f8a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d31d295cc740f0012943ab45a9a45d8", "guid": "bfdfe7dc352907fc980b868725387e98cc9644604f34ada5594000128ef840e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984667835c1ca3de1e192e363ded89bb42", "guid": "bfdfe7dc352907fc980b868725387e987a7bff22a50131680ac99c29587282c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de57d9be94d9452b195b91ad57902c9", "guid": "bfdfe7dc352907fc980b868725387e982fa5294aa48dd3ae5f0ed32d1fa73939", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb81cfc149ce52089d014036e25449cd", "guid": "bfdfe7dc352907fc980b868725387e98f072cff916b9ed08b22938ea29f14109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dddc836a32a0eaa7ce3b650702059bed", "guid": "bfdfe7dc352907fc980b868725387e980800372f5050eec1ae44d30b1affb194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8bd761567364953a6865341cdd579f", "guid": "bfdfe7dc352907fc980b868725387e98490aff5e5173305013543f7f4ac91ace", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c01d195cc704cb84b7605846f8ca0c", "guid": "bfdfe7dc352907fc980b868725387e983aa677949ce95f9a5c40339c47e259c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801a65b768e1ef52874e8e208192d1588", "guid": "bfdfe7dc352907fc980b868725387e98c27ab417e957e4d29594955e57525eb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858696b6ca0870fc0177f3ceba40be205", "guid": "bfdfe7dc352907fc980b868725387e9890a2f90f4285a62a6d1eefda1d348197"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f7f8c3b33fd590414474fe23e3611e", "guid": "bfdfe7dc352907fc980b868725387e984ca53f2979f4f020e3884848978cb454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878f6814a125def1c61562d85d709da4", "guid": "bfdfe7dc352907fc980b868725387e98760e0cf75f45ff7a732b8466cc08b0cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ce7b22bda6fa68fade04bcf968947a", "guid": "bfdfe7dc352907fc980b868725387e98565b2c0d8690019a3f05535cc130bce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd4775df781eddd9153364e8dd9bfe81", "guid": "bfdfe7dc352907fc980b868725387e9851d4db8ee6af4fcf63379444a59d4340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eedaff646ae9c23d97106df5154b7cb", "guid": "bfdfe7dc352907fc980b868725387e98ca5431106702f48f9c50f9113aa071fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98889014d9d8c4a1d69bf866dddda67798", "guid": "bfdfe7dc352907fc980b868725387e980a23ef6cb95fea11e06d243638764c11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf98ca12e6e8af239d661d92726380c", "guid": "bfdfe7dc352907fc980b868725387e982353e5efbe8b14b42e7bd35f1a59ce2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a224b5c51fce46caf5ae446498c9176", "guid": "bfdfe7dc352907fc980b868725387e98bdba301a25feaf43528260302ae2d1fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff14b82f3afe1d51976c1b7fd00d87d8", "guid": "bfdfe7dc352907fc980b868725387e9805ddbe27277bb0d9247199f021fbb93d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b683306ef00c38ae616009a3e9ac9dd3", "guid": "bfdfe7dc352907fc980b868725387e98ce16b00ce6d271feb8944aa7492d4e4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ba38f0f520361b44dd6bb40bc11770", "guid": "bfdfe7dc352907fc980b868725387e98e7b9f3fe6678b83b41d284e976335e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef49316a9e86f0d7a7886b7dd6276a1", "guid": "bfdfe7dc352907fc980b868725387e98c279900c4d65050f4277c579b7681124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986db9da92a239083ce14f717ee15f6897", "guid": "bfdfe7dc352907fc980b868725387e98bcd3928e1a148700f4f079c01f4ee539"}], "guid": "bfdfe7dc352907fc980b868725387e98b513bcfe2b5a27c1e8d24425b203976e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc2d4bfa7fdacd5c875eb8ba1fd0a87a", "guid": "bfdfe7dc352907fc980b868725387e98e2e4a203e1124a2f18f1bc3fa8b14381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987745b1d828ec1942ee95441f51197597", "guid": "bfdfe7dc352907fc980b868725387e980ff358b793476c58528696bb67d322f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e185baa033d76b19c50e096e16aa8dd", "guid": "bfdfe7dc352907fc980b868725387e98450d301d47b5e84d033db4e5dd24f5b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8f5d816c23558b1e5fc70be663281e", "guid": "bfdfe7dc352907fc980b868725387e9850eb1bcf9602071b8c486695e0c23f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886943b5cd484567fc33f256b72a63482", "guid": "bfdfe7dc352907fc980b868725387e98dcc93d31edbce05624e34a9a0510b71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dca19a85ad4c9d4b9370c0e3012d26b", "guid": "bfdfe7dc352907fc980b868725387e98ba1a4a01631b1034f8187b3a6dc86915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d39d8f6fa00099a9854ca30f4076c49d", "guid": "bfdfe7dc352907fc980b868725387e98a8036ce69da0cda90ae022d378e37bad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b98a713e4dcc907b494c19dc1c100249", "guid": "bfdfe7dc352907fc980b868725387e986a8c21ad9cc7c2ceea6efe1a61d12ca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc073063d63a93f932ec4c55e551ded", "guid": "bfdfe7dc352907fc980b868725387e9842bfda054b5e449521b8490c0b1b4ca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929aba19c900b48367f9789f289625b2", "guid": "bfdfe7dc352907fc980b868725387e98901ed2a0c06e9ceccd411aa884680d3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840de42a55d9a2693050a934e577ab665", "guid": "bfdfe7dc352907fc980b868725387e98068476b27bbcb6f7d081fdd4127a1380"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e0a2e83654e74aae1f4459efe3e0e6", "guid": "bfdfe7dc352907fc980b868725387e988b84c53d1822fca642fd0e3d59f90ff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847be8d0d6404329e33d8c7613db34f67", "guid": "bfdfe7dc352907fc980b868725387e988306981e4e2a92e53e2ec5e0441a97a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e7edfb2b4adecf11edba95f72cfb93", "guid": "bfdfe7dc352907fc980b868725387e9891bb46bc3d4dc8884158d1c2cc3ee789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983448424762de175007a150a2d9d71c0d", "guid": "bfdfe7dc352907fc980b868725387e984d61f89b16c90d7bef7b59a719208ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824eef1f42b6496d0d999201d0c7cff58", "guid": "bfdfe7dc352907fc980b868725387e9859be64ebdeaa186e25d2b051d6998a94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddee71316046d32e3a33edac8b38ae3f", "guid": "bfdfe7dc352907fc980b868725387e9828f303ff6970c0fcb5f591dea7624db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ae96bcef6bb444416f80977c659978", "guid": "bfdfe7dc352907fc980b868725387e98b2817fbd6845b10087d2246de0f397bf"}], "guid": "bfdfe7dc352907fc980b868725387e98b7a96e502e72263a663b357fc9dfa56a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98c5f3c51306162273a776f99f6b7bad2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98fb9f8f3a45ca59b3db6d15a6d80a04b8"}], "guid": "bfdfe7dc352907fc980b868725387e98fbd8ab86d254ecf4afe351017cd4c196", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981e7c9a9b23c5bf50b1d3c918022d65a8", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98e9b308ec40ee8f95b5d96e0a37cd4e91", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}