{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9882fb1a7d37204207d8cf3bea67a85b3c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980cd6bc1cb74d60fdc6ce21b703440b4c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d79c3dfaa7f0f38490fa873fe1aa1c1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98752cbc78c7ab317e8cf9282749688d74", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d79c3dfaa7f0f38490fa873fe1aa1c1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9df2fe311209eafb19f237543847fbc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a1e0d87b5519a1c6792b363db567104a", "guid": "bfdfe7dc352907fc980b868725387e985c2ccb48020ac861c15c8f8bcd6a3ae4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980074226e021e135f8be9aafaaaa3cf5b", "guid": "bfdfe7dc352907fc980b868725387e984de9d26d4cc3ed97fed92a5dc1d31986", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e322c1dabcca798c740b20e044162cdd", "guid": "bfdfe7dc352907fc980b868725387e98bb46bfa9c9dc3ddfa11e4961d3f8f5f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a5e3d8788b2f090873614e6fbf6a9f", "guid": "bfdfe7dc352907fc980b868725387e98da7c08e1646b86d4466614a4554e62b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db766ce60e30be809392e7a9f79dbf1", "guid": "bfdfe7dc352907fc980b868725387e987a535dbba166cba2d25df52c7d96d9b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847afb1b0204444cfc78625da13579b39", "guid": "bfdfe7dc352907fc980b868725387e98ce2b61d286860241ff8f4dd701385699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0021a39df4ffa0ce35f3b3e138e1c2", "guid": "bfdfe7dc352907fc980b868725387e984949db576e14dbad76f328cb09f09d1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980128d3750a8ace1540c8f1b0aa6d0688", "guid": "bfdfe7dc352907fc980b868725387e98366d2c5d5122dcf868749eda5ae338b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7ff1718764627420ade214886be853", "guid": "bfdfe7dc352907fc980b868725387e982969f565f0fd04a5b082681cf57d19ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d474fe20d3c3f077a0e34f59cb7cf8", "guid": "bfdfe7dc352907fc980b868725387e98278833d7f2772c24db207dd668356c37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848031eed4481ae612e0508ca5c41b369", "guid": "bfdfe7dc352907fc980b868725387e98069a8d005f73555dda4ee119a4440657", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae29a272391191146bf70c4d5f41330c", "guid": "bfdfe7dc352907fc980b868725387e9841ba74377d5acae7b24d6a5c36a175f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff37aee81927fff529d31dc89043fb1", "guid": "bfdfe7dc352907fc980b868725387e9831be55f44226c12dffd1f8eb23560269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998b829fff6376c17d00c8993a9fce19", "guid": "bfdfe7dc352907fc980b868725387e9886d7f97e4cd4ed15b46197abcd771da0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e632af89ee8dea33cb085f5e1d58ff7e", "guid": "bfdfe7dc352907fc980b868725387e988eef80c7d936723a4625644e6f6d97e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e54791b11e18fb756ae62af439fc55", "guid": "bfdfe7dc352907fc980b868725387e987d168527a779bfe6d6ff94b432bc27a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f394f5788dd17969387e609f0113d4e", "guid": "bfdfe7dc352907fc980b868725387e9829b943cbcb3d548b442d00770c3c55b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc46d8f651abb387cf4eb24ed106e9da", "guid": "bfdfe7dc352907fc980b868725387e9840e40ec3fca7df1e213eb039a54b9708", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335e6299907904553cad23821c22bf9e", "guid": "bfdfe7dc352907fc980b868725387e984ec10563853bcc8a75deacf3632ba265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892439101ac5f40d11af4c8af43e9e62d", "guid": "bfdfe7dc352907fc980b868725387e988bbd8f3ee7d0eadb89a95c1e32025491", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989107f732d81e2827c6bea369c4b085ba", "guid": "bfdfe7dc352907fc980b868725387e98a31e2e5d8ddedf4308ae2476863577ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff5511f855c819cdf04a755db220d92", "guid": "bfdfe7dc352907fc980b868725387e98c02d42552ce560a84d2032e042b1f029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af8a661a3ad4b57f35629d546a8c3da6", "guid": "bfdfe7dc352907fc980b868725387e98d1ce08b370bb781d0ff8d6757a1f4dbd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98870fd785d7c8804922c92a8ba60467f6", "guid": "bfdfe7dc352907fc980b868725387e98cd398b67ec59d3bbcd95bbe4d7b33731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba15baf1fabe1709a8f3f44320d591c", "guid": "bfdfe7dc352907fc980b868725387e98a31f1fd69867ff1497a8add8adb9a4f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cfe2276ec61ab46a17c7060612d894a", "guid": "bfdfe7dc352907fc980b868725387e98b490414b2476a14efe981bba76ecc1f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe351f73750d278589c1a865ca48e9f0", "guid": "bfdfe7dc352907fc980b868725387e9803cdf769aea4f931777b2ef6007a27a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3beee64f49ec5e58e848881a4ee4f9", "guid": "bfdfe7dc352907fc980b868725387e987409feee0017eba215120a3a763bdb94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdaf3828eaa7a23f9b15714b91b22f8", "guid": "bfdfe7dc352907fc980b868725387e98763f8295fd5fa666324242f2586b205f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806196516fa10ae846dd63b069244b0f3", "guid": "bfdfe7dc352907fc980b868725387e98aae59177f8757f941bddb61b4e507c9a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f75ef3a40f2fc131942d32942c98958b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9866d8529ed7e3004d1d58659b04e53a59", "guid": "bfdfe7dc352907fc980b868725387e98339f9d940ac130efe438f3409362d758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d0102c8fe662c9b6e07705aef6fedf", "guid": "bfdfe7dc352907fc980b868725387e98ef3d1e30338d1d083462a3e93a6f8d55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c09eb89e1d233e93a64b73ef660788", "guid": "bfdfe7dc352907fc980b868725387e980695fe2d33cfd9fde0bd5755bf42264b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa7c87a1ce3529d9a9fe7c230022cc2", "guid": "bfdfe7dc352907fc980b868725387e98a132baf76f26bffab6d0d4644879ef78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e4c56cd93b90b4c3ed40c309d0897d", "guid": "bfdfe7dc352907fc980b868725387e98bd381220316230017b19dff9d2d80da4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c02807b6fc29b99f1d2854a10521728", "guid": "bfdfe7dc352907fc980b868725387e98fbb52f364d9a03ec313d84e7d4dbc6be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5ee1d8e07d6fb51b3004755d8c4986", "guid": "bfdfe7dc352907fc980b868725387e98b931e4ee3db2b7982a3be7635ef5dd38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bcec1a1478b599f2c633817bd32bed8", "guid": "bfdfe7dc352907fc980b868725387e9800394714217cf70c99ae62cd2b23b217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e77b08a4617c5de93c60d0555159340c", "guid": "bfdfe7dc352907fc980b868725387e986be4f0cf4a9be50dc9e077acd119d59e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4baa2b25adb955deacf43be578c3b7", "guid": "bfdfe7dc352907fc980b868725387e983af1a3b3e9ea4f2d20d39e51bf0deca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eab936ea948e88d6a6945941cb239d0", "guid": "bfdfe7dc352907fc980b868725387e98ca6c667529e00e8a7f4f508e2fdfa155"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897859bb3a7df934b9a9d9bad8eeb3d95", "guid": "bfdfe7dc352907fc980b868725387e985020067abfd551c664cb024ad2392b83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896875779affb8abdbf52ae2b0b8ef81d", "guid": "bfdfe7dc352907fc980b868725387e985a74e65e6c02b3a4abf866106772b9ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984edd56771c59a376280904049559a071", "guid": "bfdfe7dc352907fc980b868725387e9814705a26902d9b3f8617ec6d7eaf824e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db58ec48b5c9b2df636d07a3997f06c", "guid": "bfdfe7dc352907fc980b868725387e983b5882c9f9f419411af15b14c9db1824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e1b9b82dc61428203789f355b1ca52", "guid": "bfdfe7dc352907fc980b868725387e98ac13e42d373baeb162e39f03dd9b0054"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df330cf2b84db67d397a7e90c8aae12a", "guid": "bfdfe7dc352907fc980b868725387e98d3921f7d94cc6f71d5a20683618805f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9317eef738a389708b9bca758420345", "guid": "bfdfe7dc352907fc980b868725387e98b72840e7f624917efe909e75bf8439f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982beac2e7090bd1151b6b9731f3169b07", "guid": "bfdfe7dc352907fc980b868725387e98eb1fd6c1b6b0878cfe417d0472eb2cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d0f843cd5c5bd8dbe00f235532cf9b", "guid": "bfdfe7dc352907fc980b868725387e98cb717bc31354877fb1b03b696f9619bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab6d1153ca93fb2516697d37e2cd635a", "guid": "bfdfe7dc352907fc980b868725387e98529d822dfa81355b0b84a7a66d6b5649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984736436c0d6714ebbcbca8b4cc03ca55", "guid": "bfdfe7dc352907fc980b868725387e9886e55df8801f3f3ad650b1ccee8b8ce1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9cabf0dd7de744a1d44652b4a8d3e53", "guid": "bfdfe7dc352907fc980b868725387e984bdbb6b3cc30b9d48fbe510c6f8f793a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e73f9cef21cafd1cef77056ffd2c9ee", "guid": "bfdfe7dc352907fc980b868725387e9896c4214197bd97ee09976571e6cbc2e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8f87575472a0590155b8cec4fcddc3", "guid": "bfdfe7dc352907fc980b868725387e9839038ea6c5dee575ffad10d137df34c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda023ec112329aaff46b72282dbb63e", "guid": "bfdfe7dc352907fc980b868725387e98b63f7cec88032da5ce2bd3551b3c2940"}], "guid": "bfdfe7dc352907fc980b868725387e98801f99136cc6087ddc94d490b2948f7d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e986ae5d58f2e660c459b1f3aedeaa6c429"}], "guid": "bfdfe7dc352907fc980b868725387e981809c2b2cf787314927129f742ef02cc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9802644b4dc3a7db7a1f4ed836cae03dbf", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9851df16ad3a34507f22430dbf4b1b8434", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}