{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f47598afc04a91179b84cfd152342ce", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9856fa82f53e05bf89613c237e7dd37a68", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9856fa82f53e05bf89613c237e7dd37a68", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989bd59ae8aa865a2f5d74108be439523a", "guid": "bfdfe7dc352907fc980b868725387e982ea0ca19c0771fd6d266f2b66446b37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98445fc71e80b270a6f4f87e83a657e4d0", "guid": "bfdfe7dc352907fc980b868725387e98f1887403300e04b839877af836998497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841959ed32a01827fee1ef3521002ae66", "guid": "bfdfe7dc352907fc980b868725387e9899c4b45e473fef94c750c44c19300e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f40aa4605baf1f71694309d66f9a9c81", "guid": "bfdfe7dc352907fc980b868725387e9801e957e9f2cdc171382139f7af1a5681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed25c8dd048fb80f557b156db60f37ba", "guid": "bfdfe7dc352907fc980b868725387e98f9dcd7bfc2b70fc0334d5a2b029d2be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d74e2d1c69d6eab51205f6a880f823b", "guid": "bfdfe7dc352907fc980b868725387e983c0142618f0c80e86891f682ede039d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ead4c18de261c31dc44fa46619631fd1", "guid": "bfdfe7dc352907fc980b868725387e98a01fc3746b75fa29083dde20e4e057e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98698a26412a23f37d1d8b39212707db32", "guid": "bfdfe7dc352907fc980b868725387e98cd73b22058278a9b4f651422cde8e6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512d36c28d0cfc5c3712c57cd2db1037", "guid": "bfdfe7dc352907fc980b868725387e9858da164c6f05b97dc4f70eeaf0ab7f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ff90f6e5d83785d3ade08b5de5cb21", "guid": "bfdfe7dc352907fc980b868725387e9810ee0ac07ad1614d2a0f4f4692012057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf7347938139066445c5b74c63e95da", "guid": "bfdfe7dc352907fc980b868725387e9898f6f1d0225d5da11e073619b459ecbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98600467a7bc3d59cda5438851b046e375", "guid": "bfdfe7dc352907fc980b868725387e982567c545f94782ef792faee70a9f28e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693e0712ee89a75a18cdd4111226a412", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823ba452f2dad475e73459f19067f3cd4", "guid": "bfdfe7dc352907fc980b868725387e98616c37b3aed90db7f9fdd15a498d9b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031aa42f7056e79bede418f21e4896fe", "guid": "bfdfe7dc352907fc980b868725387e989ec464f79f509c315509332bb727e800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0c767eb6265ca2e07d990deb516524", "guid": "bfdfe7dc352907fc980b868725387e983249d575db8b5cbd53c0564d7472fd4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846bed86fa81093250bc3d6254b934ecc", "guid": "bfdfe7dc352907fc980b868725387e980d76b669df9576a70807ec449fb952b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1bac274b5eda67167d45400d4c1cc3c", "guid": "bfdfe7dc352907fc980b868725387e98bc7c56555dd219b78ffdd88c372b232b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852f49bf7075657d4b0ced5e20192c017", "guid": "bfdfe7dc352907fc980b868725387e988e7018dc0c9e1f0c9f93b5994bcbc130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d101ec56abe528781cf12174293f742", "guid": "bfdfe7dc352907fc980b868725387e98dcef7cb18e4d73ea8eef002919db5632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852ee4f30b254b2319fd9b950fc3c2eaf", "guid": "bfdfe7dc352907fc980b868725387e98955da4966904666f6adbe480ce93a36f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d31805145915865fa0ad5e97b9247e9", "guid": "bfdfe7dc352907fc980b868725387e98232d0a9b2be5b70279bedd97f191826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5447e8f5f5d8fe73be4771384ad0ade", "guid": "bfdfe7dc352907fc980b868725387e987211db59fabb7296cf1568fb71a68255", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f23e4fa001da346b2a1176c2749b8436", "guid": "bfdfe7dc352907fc980b868725387e98c8af4857ac32d849c3b447bcff03aa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6509a242341f83b7923767df7915f2", "guid": "bfdfe7dc352907fc980b868725387e98dc950c62ee46ede361cd98ae90dd27a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b235789059f7447fb5fa140997cf1f3", "guid": "bfdfe7dc352907fc980b868725387e983f0af89b0b8bda8241febbe4723f8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd39a6d8a52cda4a99538430de61d530", "guid": "bfdfe7dc352907fc980b868725387e981a333127dad81374abf6fe8c83600eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a6a9adbf46fd83f4ac2b368ed83462", "guid": "bfdfe7dc352907fc980b868725387e988f8e2ac76c48795c8610f17a754a59c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1729812266034376656fbd493b6f1dc", "guid": "bfdfe7dc352907fc980b868725387e984b1e4946f1823465931d4e8492eb19a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98737060eab8f0d1ece7d566e5d95fd492", "guid": "bfdfe7dc352907fc980b868725387e98486c17b42526ba81b0e47a838401edd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2e7bf11084ada65b3209582b793597", "guid": "bfdfe7dc352907fc980b868725387e98e24ce56a008cd8fb5a8cdae1e9811e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a20fb944efb08785856c53a1013cfcd6", "guid": "bfdfe7dc352907fc980b868725387e98243ca78b2974c64cd01083596d8fe9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4a62d882b4ac98015d409d64323a04", "guid": "bfdfe7dc352907fc980b868725387e98c41a340685df4d1ce3783021dcf54fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980279e29281262a0a8875926695a01da5", "guid": "bfdfe7dc352907fc980b868725387e98506a4bb5c9b087dedda179e379e974b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf87ff68b15b7b249c254dae8442e75", "guid": "bfdfe7dc352907fc980b868725387e984463890dd80178e8bcdc0b520010feee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822400be6ae9863e365c209d7f61d3432", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877626606d34d1cc60ceb7b02e4dc5f5f", "guid": "bfdfe7dc352907fc980b868725387e9859e3b579695a572ae5f0cad67ec2876d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7448a640cf77018bc3df9aa855a6c32", "guid": "bfdfe7dc352907fc980b868725387e9885aba67d2d135f9806c9a05487a1a98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca745ff84fb4e4aebb5659bf21743829", "guid": "bfdfe7dc352907fc980b868725387e985805bc10d6057738af5938ead2b69fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816ac8cf57ca4bf24a96819646e9126ff", "guid": "bfdfe7dc352907fc980b868725387e985658d819a1ceda9c7580d1657159b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ed30e8e2249fe2d87c9d9dbc5be21a", "guid": "bfdfe7dc352907fc980b868725387e98c8c90bc417199af9668b9601526e858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c0cbf97f4c9dae4b7e1d0d66737fec0", "guid": "bfdfe7dc352907fc980b868725387e98a79aa5b656ffdfb7930448cc9a744fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981673ae325c23938a150ad38667d62722", "guid": "bfdfe7dc352907fc980b868725387e9822c0d109ebdbc53ca874ef63b18251dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a497e467effd62d9f7bac751cd5c98e1", "guid": "bfdfe7dc352907fc980b868725387e983c3ab34f30d3bcea13309294f772a70c"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}