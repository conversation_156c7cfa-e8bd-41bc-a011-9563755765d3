{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981743f384f15f289cf54eebca43a72c9d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c846f957f8cdf1863d82d4e7656aa828", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855917891ff94144a8809f398d8ab1259", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e736c77b95d18f4cc0bb4510866f9e23", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855917891ff94144a8809f398d8ab1259", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b64292fb4f64215e823374c66e4b7ef8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9826ee97a7c075e6e5d5dccfb6686ca9d8", "guid": "bfdfe7dc352907fc980b868725387e98aa30ec20d4709054f3d802cb4d1b2a08", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a5f03272055ae4eae90eb920c8cff2a7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880c8df9ef9dc9b4e73b4709398531ce0", "guid": "bfdfe7dc352907fc980b868725387e98968aa8761f77e671e4ac9569fede58c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984501d519fc1172f0dac2272b925331f3", "guid": "bfdfe7dc352907fc980b868725387e98233ce6d3744e4c256d2799416e5278b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98385ce3905c10185291a88ed0abebcbdb", "guid": "bfdfe7dc352907fc980b868725387e989efafa635e6f1a75d1b5034b874cf526"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01fda140c5db5fdaefcaf5cdb976d2d", "guid": "bfdfe7dc352907fc980b868725387e9885598d0dd2f3c281d86a7f95cec6cf0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a889276df4b2b9f0782aa39bbf1563e4", "guid": "bfdfe7dc352907fc980b868725387e984c10a219f50c61c92edfef5d3207aa3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5cf180468bb120a9bf77c73358136a3", "guid": "bfdfe7dc352907fc980b868725387e982bcb968d0289cc8ea0a5a1018650ca55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810b2670eea075ce38673f3b620abd2d", "guid": "bfdfe7dc352907fc980b868725387e9855bce57d4520803968669c40649f52e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980453c6707101df840ef7b4a90f558e94", "guid": "bfdfe7dc352907fc980b868725387e98247628004723487f25ae23e8fb898441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc469bb8fc01dd79baaea21c075221f", "guid": "bfdfe7dc352907fc980b868725387e98bdc7faa614da525a962faf51da8acc29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a34f956612fa98312147c8de22a023f", "guid": "bfdfe7dc352907fc980b868725387e98331b2e9237c72069e35ed1d07e8657e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7bb390f51bd7a18502795232f309464", "guid": "bfdfe7dc352907fc980b868725387e988bf96574f738338ffac7471e51076bba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98778f37a401239be11e71aa42457caa52", "guid": "bfdfe7dc352907fc980b868725387e987730fdfa37903b0e225d0dfc0504a079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6fd4bdab6d9240d4d7e1c367be64cf6", "guid": "bfdfe7dc352907fc980b868725387e986f12774af8d6e44bf27641b2e75d4369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2422af0244c6019b1330643deeb935", "guid": "bfdfe7dc352907fc980b868725387e98736d71ddf5fb39e79c44216129ae3e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982649235733e0d81120c17992f6e11d51", "guid": "bfdfe7dc352907fc980b868725387e982aff83edfb10a2e30ffb9bfcdd748016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c04586adc21c557f636ebb790a4aee8f", "guid": "bfdfe7dc352907fc980b868725387e981bea325db6062ccec6329bd99508bcc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98747d6305c59481ccfecd9421e5f953ed", "guid": "bfdfe7dc352907fc980b868725387e98ceafc3284b369410d30a4993c5d993b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554b1687af9629d0c355ae93977cdbfd", "guid": "bfdfe7dc352907fc980b868725387e989edf4f600a82adc947a2f5c2c0bb58f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956d791c2b444c13daca95acbb1c128f", "guid": "bfdfe7dc352907fc980b868725387e98755bf3ce034be751f7450109604a0642"}], "guid": "bfdfe7dc352907fc980b868725387e98801fd21c59ea54f986de614e4ee23666", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98aefe86c768c2ffcdb7ef77441bfb9299"}], "guid": "bfdfe7dc352907fc980b868725387e98948b719e4fec60a1b279f86768bbb1ba", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b0530875beee3c37dc40c7480a69b5f3", "targetReference": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c94bb7c54398f9c96d3af30c3e278a8", "guid": "bfdfe7dc352907fc980b868725387e989b3c20c53458434dd884c1dc5f44d3c1"}], "guid": "bfdfe7dc352907fc980b868725387e98718e5421351eba62122ec356018414fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d", "name": "PhoneNumberKit-PhoneNumberKitPrivacy"}], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}