{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4abd61ad8e113d70d13e0c824e3b7ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a4ebe56a982a264b71807282c5d1756e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988debfabeb82fdc88e05b5818380c05ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d4a5f3e795c6b3107b7a65023d6869e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988debfabeb82fdc88e05b5818380c05ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986817c1873e443f68f8001a83d6a321e7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea80a1af7a68409130c9dd60d2f95484", "guid": "bfdfe7dc352907fc980b868725387e9821c11c960706ad42cba10f0c2fe6ff56", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a8bd904d9fb80f54fd7b4ef6a8883683", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c36d323d5d7872bd1b59dc5d4ec6cd4", "guid": "bfdfe7dc352907fc980b868725387e98ab38bac9222230e11b6ceb2de2b97cb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d404d16e59bb973c907f322ea1c333", "guid": "bfdfe7dc352907fc980b868725387e984aef3014eb2f7bc018a4630a1f524e7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610aaeee30d0b9cfc506ce745ff7fa7e", "guid": "bfdfe7dc352907fc980b868725387e980dfce45633490ef58984d16eec782802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aba5304b3b281e4ca6670af05dc8b62", "guid": "bfdfe7dc352907fc980b868725387e98d8a6ac7ca9cf3824bfa28af412a3b20c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e983fe82fac8fcc72d13c5607897ce", "guid": "bfdfe7dc352907fc980b868725387e982485d55da526d26167b049faf452a047"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048606fb8f31d084b4b32af529bfe8dc", "guid": "bfdfe7dc352907fc980b868725387e986093f080f6a93f70157a346be58fa55f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982744b38c5520e9b0785175125f89e141", "guid": "bfdfe7dc352907fc980b868725387e98a0b9a29bc91e4fec9b68e6b3c5126fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3384c859cfd4f6d635bf94838c9fbd9", "guid": "bfdfe7dc352907fc980b868725387e98db04866cceff1bddc163128491a43471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974a187e68f854068e3a64293ffc99b1", "guid": "bfdfe7dc352907fc980b868725387e9826d803aa5394f571cdb97193ba469062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e35cd0ad0ba6336b89ed524a0a533e53", "guid": "bfdfe7dc352907fc980b868725387e9861d3872bef4126e8cfab3cc363ab36ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b409ca97348b4ac04d40214f4401048", "guid": "bfdfe7dc352907fc980b868725387e98e955058a9da6896da0c5e37dbdd0f185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982705253f99bb4658fb10529d0d78d33d", "guid": "bfdfe7dc352907fc980b868725387e98505a3c80e6ba7abc564839e129b9c19f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345ab0cdef8fa1aa3bd88a56461d0e56", "guid": "bfdfe7dc352907fc980b868725387e980b636f7d39df9bdab96916f51a06da4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d45e989ceba75ffcf6634a3054138c", "guid": "bfdfe7dc352907fc980b868725387e98800bdd7a56d792a49bbbc78f2f183524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d3518f5ec928a93a83222d0c09d47c", "guid": "bfdfe7dc352907fc980b868725387e984de53cb28467b3eff88e0598e8e45c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982299f9644f184fad9189d4b080018434", "guid": "bfdfe7dc352907fc980b868725387e98e350e2025a439416eca95a1126748b61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98641136a65ddb2171422124db1b7b85ea", "guid": "bfdfe7dc352907fc980b868725387e98fb30dcb712fe392e1ff49a995f822671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf492034387b3aae23ac00961325ad8", "guid": "bfdfe7dc352907fc980b868725387e9843659d607c563e6c0c477765359412a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160b84aa208e54bb6889bf102647b10e", "guid": "bfdfe7dc352907fc980b868725387e9886616c758d25102eccdcdc5048fc983e"}], "guid": "bfdfe7dc352907fc980b868725387e98cf27cbc984ae7581ab96ec957e2c2e8a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9859da112e4b4091fc2eb5478730a3c96d"}], "guid": "bfdfe7dc352907fc980b868725387e981cc0d5b7bb883d953d89a734e150f3c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989b82ea03a7699c37764e7a53267ba769", "targetReference": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da4279e30eab9d595eac8801781d87d4", "guid": "bfdfe7dc352907fc980b868725387e98142583d13941c92fcfbdb971c9b7cf3f"}], "guid": "bfdfe7dc352907fc980b868725387e98cd6430f61921e6d522d0d5af057ce0bd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d", "name": "PhoneNumberKit-PhoneNumberKitPrivacy"}], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}