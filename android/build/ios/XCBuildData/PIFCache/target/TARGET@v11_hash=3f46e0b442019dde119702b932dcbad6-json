{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989cbe5d7b7e9c41330ea0c7c1ecb61a31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f93ebc1c97a1d3b3cddbcc5b0fb8ccc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f93ebc1c97a1d3b3cddbcc5b0fb8ccc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b56b38f10ebf216b6aa8c706689d8c8", "guid": "bfdfe7dc352907fc980b868725387e986c642d959ab0b3a74cf2dc9af7ca6575", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b336e6172b75e5a0eca34a20bd189b3b", "guid": "bfdfe7dc352907fc980b868725387e98589145a49a93692586adf7e483809213", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f933bc2e35f2a060979f6ef02e56bd", "guid": "bfdfe7dc352907fc980b868725387e98a3ab02bc76ad21854a394106652b714b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e316c0aa3f855b8818c7aacfc01bfb30", "guid": "bfdfe7dc352907fc980b868725387e98aac7aae5133e3cb198d493e2da7edb95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee218bbca1a029a12e3117a31b8d4e64", "guid": "bfdfe7dc352907fc980b868725387e9833f2875a39b769b84ae89ca0c330fa89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7b7153ac07f2bc8d5f8b8ec02128a6d", "guid": "bfdfe7dc352907fc980b868725387e98095355b5d46bdead464a8ab3405237eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bee3720d572a091b77ec0c4dd6517df", "guid": "bfdfe7dc352907fc980b868725387e9895642136ab6b437c709e2eec70dfc1bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e695d28a3d7deeb513d1010d08a4cc", "guid": "bfdfe7dc352907fc980b868725387e98bb70de040d11f52e08c72407a0b7f2ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4c899088429c946e70beeae7f4f01a2", "guid": "bfdfe7dc352907fc980b868725387e98ac2b3ccfaec700b91880d00d1aeef55e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c3b03f0ff76b2f437335a9b0b017d37", "guid": "bfdfe7dc352907fc980b868725387e98d3467234214a5fb1717e5658ac600e74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e61fd3ccb587c7ac07745d3ab506f3b", "guid": "bfdfe7dc352907fc980b868725387e98dedffa98a09362a0bedd451061872db5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495caaa8446820ba395b1ea05fdb757a", "guid": "bfdfe7dc352907fc980b868725387e9849ab0df8cb3c38b56b0161041e5306d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2fd7f170f5d49df714116a990c60bd1", "guid": "bfdfe7dc352907fc980b868725387e983a81ca54b456f8a909088d5b9f6a5cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f28e81f1738d83fdf4e709fc5bf9baf7", "guid": "bfdfe7dc352907fc980b868725387e985d891ce763262ae8564bf80524257b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1709b4ad684d3a8eea7ed99faee734", "guid": "bfdfe7dc352907fc980b868725387e98cbccbcf2a5cb8519cb01836a36580c4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ad4d5bbec9a3d6b6eb3fd81378d962", "guid": "bfdfe7dc352907fc980b868725387e985ef2dd14f9336c47ee0574cf2ceb0545", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5062516ce8cd42c1ee1bebc9af915c9", "guid": "bfdfe7dc352907fc980b868725387e982fbf638d2a2d7134f5810cfbc8f87426", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820bdb666ed7c1071b1cefe5bd83c3ee0", "guid": "bfdfe7dc352907fc980b868725387e98047d9c17e150a48a150ff6780ab58cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ed0dbda8b96cf2912407699598bec1c", "guid": "bfdfe7dc352907fc980b868725387e9871f01842cd1e75aeaa24ca58c60abb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986491b965b5f36463e3c111aa9a543cf3", "guid": "bfdfe7dc352907fc980b868725387e981f7455bdb1fbc4da3a82d543726f3d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c46cf1d0d6466349ff31c51b0d44aeb", "guid": "bfdfe7dc352907fc980b868725387e9804fe3192a5f30d2e79b6aedb3464d216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b155e4e6e5328023c8a1a6551ba155", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897dc5688e26fd4a84b101582270f4ab1", "guid": "bfdfe7dc352907fc980b868725387e984028cc6d365683630f975e4546c33dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98796a3f23e276e12b860260c44d5c2d02", "guid": "bfdfe7dc352907fc980b868725387e981975ca6e74bac51e5bc3ea7aafa790b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d7b62985f1970c65bdd0c9ac66eaa5", "guid": "bfdfe7dc352907fc980b868725387e981c62e3d0329d35ceb953521f987e0874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984adc1cececeee29aa165fe2ce5dea76a", "guid": "bfdfe7dc352907fc980b868725387e98cce31e680bb7749b81cdf0cf8fd13cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985513a72be8e08130af4434b6a6061444", "guid": "bfdfe7dc352907fc980b868725387e986b03888db3bf0f6e1886d46387481e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f9fb39a7736fe0ed93954a84cadc48", "guid": "bfdfe7dc352907fc980b868725387e98fd23e7a517c3bad0fe25bd525f9f575f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826f4c3617c0c3958fe3f11b55a92ebba", "guid": "bfdfe7dc352907fc980b868725387e980463d32e76f26f0fea0b1e1523e26914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f8133d3aac21fd2f4ef31b337aeda38", "guid": "bfdfe7dc352907fc980b868725387e989871f188dd1e4bee67360343550cc279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e548f5535607535210663b7f034ba2d", "guid": "bfdfe7dc352907fc980b868725387e98f61dc4e84c1074c6c51b018070465690"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}