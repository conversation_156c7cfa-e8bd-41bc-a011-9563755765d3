{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4a6340650432554e93fed7304f0dcae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f6f7eaa875deefa6d574b7f9615f632", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc3b35e3e9fab78dea538820bfad539e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d4707053e3aa009c1892c31222a87723", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc3b35e3e9fab78dea538820bfad539e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839e3ed10ba1545c34c0e5fd0ea4fb8d7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c08767796e5384ba1c24fa6212cfbc5", "guid": "bfdfe7dc352907fc980b868725387e98a39504c471235f937b9b114ca5fb784b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987970ce3ee40bf1168b6369f6ee18d4f7", "guid": "bfdfe7dc352907fc980b868725387e984bc5e566087eafc87d5fa3c1a4eb9488", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fed0938a1f75f974b2d4b836b24edeef", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98756b9b799723fe4ef945f4dc103259d4", "guid": "bfdfe7dc352907fc980b868725387e98283c7f50714d2dca76c7016ee4b8d738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8575ad928c754f12c3a426849419b75", "guid": "bfdfe7dc352907fc980b868725387e98d47cafd3c597828a78891b8eabd5aefd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d5a45af995a3fe57c231221ff3d0d5", "guid": "bfdfe7dc352907fc980b868725387e984401640466622e0a520a1b0adaef1f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c779bcec85c61f767772644bb37e086a", "guid": "bfdfe7dc352907fc980b868725387e98004b3db8df36820f462b824319f09010"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbf951ccf3b5762a42d3605941a60e8b", "guid": "bfdfe7dc352907fc980b868725387e98c09a810921692a74918d6b87da8173b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd28209efff8b9c5201c53a5d1e9016f", "guid": "bfdfe7dc352907fc980b868725387e982371a4642136d10e6f64870ab87570e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e593af2d765e615ebd49d75ed51b1ed8", "guid": "bfdfe7dc352907fc980b868725387e987313d0a47df9e6be3af76e1cb96b1a60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db886ebd2af8b2463995ca9f1de501d9", "guid": "bfdfe7dc352907fc980b868725387e98a205311f28b0e6128c16b0965dcf5c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f388c91a16e48ba3ee19ffee6a667b8b", "guid": "bfdfe7dc352907fc980b868725387e986aa56a3d48c81f0f1949e818bc0d860b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c40cb48c0b4647f79d9b28187de948f", "guid": "bfdfe7dc352907fc980b868725387e98738ce2379f7088dc9c3ad97b326fa61e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367167873077023521051f8ab478b33e", "guid": "bfdfe7dc352907fc980b868725387e986f789f30095060a475ece82b1b5d62f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e1b9b17bcb31515469f95a06154b32", "guid": "bfdfe7dc352907fc980b868725387e98a96e1986b184bf19923cb61bb82b27ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc89c972405c519e55e59623b52bfb6d", "guid": "bfdfe7dc352907fc980b868725387e982c64276f794a5400bc0d216c8cafaf01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0603bfc4d8f468c62a8941dad85ba99", "guid": "bfdfe7dc352907fc980b868725387e9862d5dfb4b30ab5fa10836b12cd737c60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0d9e0e22d5cb95a016cf1879cbd7a6", "guid": "bfdfe7dc352907fc980b868725387e98bfa5d3cc30008556ea3acdf3aea3c88c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162bc4d2df36ab2884b862c166018d8e", "guid": "bfdfe7dc352907fc980b868725387e983fd98b439c073972cc333ae843475e06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8fc895e530945f0e3464b711110938", "guid": "bfdfe7dc352907fc980b868725387e98edee1e1895f5aa92a29a155d71d12e0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98593604f9c8f1d23a121f37a5346be73e", "guid": "bfdfe7dc352907fc980b868725387e985fb00525805475f3778861398c49e108"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94d96bc97c3a55fe616ea2bc7a2cdd3", "guid": "bfdfe7dc352907fc980b868725387e98ce516e662696fbb34e8a5fa2a3e93250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f0bb4b18b23ea298eb43e80e68e42f", "guid": "bfdfe7dc352907fc980b868725387e98857c5844c88941e15b6e0507ff64b275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98204017fb1f124a6e8c96769173cf8ac5", "guid": "bfdfe7dc352907fc980b868725387e984ecb30ddced788aabda5a60a1edd2345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad512e3f405875930710f70058c3372", "guid": "bfdfe7dc352907fc980b868725387e98b196dd031f7fb606271a360e459c2704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5a83503aa35ecc855296dc8281632c", "guid": "bfdfe7dc352907fc980b868725387e983231f582cba6853a8028afbb54716c91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d498b04593efff55bff873208ea8ed", "guid": "bfdfe7dc352907fc980b868725387e98b2fc70ebc79bdf7055c9af56363ffaca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef0caa394bf3e521129f5e378b13617", "guid": "bfdfe7dc352907fc980b868725387e98e6814e720a9065532330b5d06536dfcf"}], "guid": "bfdfe7dc352907fc980b868725387e98304f8989fb6a52cc6fc79f2b48753511", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e989d17688ef29dd034a60f75914308678a"}], "guid": "bfdfe7dc352907fc980b868725387e9845308734ae7cec8d8b6dbdd30ebc26d0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9899407c37444a2819330c7672d131d16e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}