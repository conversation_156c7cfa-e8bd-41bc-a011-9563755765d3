{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817265896dee84218619e1dc18fe69a64", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982adec9daa7bd199b0a014807d3b66424", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862b80a70506839c2e62d2227ab2ccfde", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c55bee7d8949d2da8233ccc1f9bd470", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862b80a70506839c2e62d2227ab2ccfde", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b2fe738fb998344e72c28b73646bba9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98985528fb863a4d533095554053c1cfbe", "guid": "bfdfe7dc352907fc980b868725387e98806d577bad2016bb80bbae4622f5b0f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c580c10a2b86eca46b008fd4e41bf14", "guid": "bfdfe7dc352907fc980b868725387e98b52aeddf01615b933ea3f7b1301bd5d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0213143bb66f25765f86786f4873c3f", "guid": "bfdfe7dc352907fc980b868725387e981f0c4ee31df4effca09f018d090901d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988add967312c04003caba5bef402979b7", "guid": "bfdfe7dc352907fc980b868725387e984bd29d1c5424203a0a8dbbe89a2d5b70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f9d4672a1b14efbebabfb428470bb2", "guid": "bfdfe7dc352907fc980b868725387e9820a253d24d61cc6afe2ec71ac6f165ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd8c5df0a532edc46c868c69dd681b8", "guid": "bfdfe7dc352907fc980b868725387e98c024b990787ab9cb3e90a4dbc9ac4978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c045ef5d213f1b2a49d9091556a403fe", "guid": "bfdfe7dc352907fc980b868725387e98066c2c1add0e89de57b36d0e29a1e87d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984beb735e5ebe91e3315989ed4d79e8a1", "guid": "bfdfe7dc352907fc980b868725387e98631feeb5266786d1c3bb0a55d76fda01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ff3bc71e7e8c773756ea0631c6180b", "guid": "bfdfe7dc352907fc980b868725387e98ada9d39588245fa6085f8cc6535f4d60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c31ccf2b19e1f0671783bbe589e037b", "guid": "bfdfe7dc352907fc980b868725387e985452e4e134f034492bcb006d6682ac5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854dd7d8378e2aca78b74cf5b767cb45b", "guid": "bfdfe7dc352907fc980b868725387e984e6964ce4cb18daa9e642463bde705cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea50d8bed18a9c6825f2782668985147", "guid": "bfdfe7dc352907fc980b868725387e982854599d643bc39e9b12d34d7dae1e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b887424dadd021a19ca718a36f0bba", "guid": "bfdfe7dc352907fc980b868725387e9805adfe8c34e3f169ad5498ce4abbd97b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982469705cc66ff537dddb6413b449a2d5", "guid": "bfdfe7dc352907fc980b868725387e98302a5b19e7005d8592622a5cff8f95a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eefb9c1110cdf8ffa2c1e9ef43128d97", "guid": "bfdfe7dc352907fc980b868725387e984e5147a47a180cfcab2f0bf6b4426416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a35073206692b487bd1f6d09e4f3f5c", "guid": "bfdfe7dc352907fc980b868725387e98188c3d9573569928f5ee9f15726c3908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7229a5747adb8d33280953709e9742c", "guid": "bfdfe7dc352907fc980b868725387e9868db6b56461e051eaa26c19b11a2a643", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8bf27746bdfdbd1bf11f2248383915a", "guid": "bfdfe7dc352907fc980b868725387e9831eab19b1b7473bb88ed5bc194390bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c7c4e648f7c284049aab42eceb645c9", "guid": "bfdfe7dc352907fc980b868725387e981128dcb7a549845e4bc592abeecc459d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e321cd6d3c1472c6dbf3eea443702eb", "guid": "bfdfe7dc352907fc980b868725387e984fc6c0daa1b25e893c4ecec825a42e9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b70fd017637aef8757fc86d30b0a56f", "guid": "bfdfe7dc352907fc980b868725387e9858d41b3c378c78b69f61ac72915fcb83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea8feb25c7225fa1d9dfc8fdcf08978f", "guid": "bfdfe7dc352907fc980b868725387e9822e8770adc4cf059bdf5b3f4dbfe42ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792e4ca6051aa54441fa24bdf821cd2d", "guid": "bfdfe7dc352907fc980b868725387e9811cd6b2970e9b422b995a69528369665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98435ecaecbab0931f56a7e77304a9d013", "guid": "bfdfe7dc352907fc980b868725387e98cdc65435808a446c0643c07c34f59585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75c2b56b87d868030c54c8543e22081", "guid": "bfdfe7dc352907fc980b868725387e9894f3a21cca31e1a09fcdafeaea72ab80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93c777743566e161d05f3f37e7e8ebe", "guid": "bfdfe7dc352907fc980b868725387e98082cf411fcbe74fb23a61afd9bedb1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4665a00cf2116cece3b1fe1e957a43", "guid": "bfdfe7dc352907fc980b868725387e98d54e69fad7910d525524d112c6e7d2dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ab486b50e1cd2446bde5b02196915c", "guid": "bfdfe7dc352907fc980b868725387e98f57c60869a755422a10504d247af146a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a81954ab8fe62b164c455cb0a5bd8bea", "guid": "bfdfe7dc352907fc980b868725387e983a0f7028169cc406eb03bc300345650a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbaf164eda7fd8ca001d3dad277df119", "guid": "bfdfe7dc352907fc980b868725387e983fe22d2ac352d8046628d96476337160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574450a19d14e0331898e573a1142433", "guid": "bfdfe7dc352907fc980b868725387e98448bfacde8c2dc67d9c1534e20f44fd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd9e3d57ecc5b4f3d49daba5f74844b", "guid": "bfdfe7dc352907fc980b868725387e98ba2f9a3057cc1f7dcd22cb70ddd79daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ad73886ab118ad987371aaa77977a0", "guid": "bfdfe7dc352907fc980b868725387e98cdaaf6ba907e44c7b82a0401bbde2a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98198bb23a5f2b31e0bba1472938ba3513", "guid": "bfdfe7dc352907fc980b868725387e987c36f34a5cd3f35d8c57c8f7eb043ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985741d0be085dd18cdabf9a979fd94f0d", "guid": "bfdfe7dc352907fc980b868725387e98b4295129df590939e25c137f47ccf1f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98122fd549a451c6515436e905756cb729", "guid": "bfdfe7dc352907fc980b868725387e988abed2dc3193f070f0cf1a3fba2fdd92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fd7cb1d664900500ceed0910c4c114", "guid": "bfdfe7dc352907fc980b868725387e98ada10369131e74d1b788a33f7dcd6e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a680a3f2fde607cd72e8f282d2388503", "guid": "bfdfe7dc352907fc980b868725387e98f9df3cd61852b428dfa680bd35ea754b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3096176333bf46a8a91d1639433c4a", "guid": "bfdfe7dc352907fc980b868725387e9898b212dc8787ce3d4b7fd6a87a0a4415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98655bd0aca354522f973070f7f5f18ec0", "guid": "bfdfe7dc352907fc980b868725387e98b6cb90ce781b07e5914863ad71fa8377", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980776d457bc196d41de7b91475f5bd42a", "guid": "bfdfe7dc352907fc980b868725387e98a89e2b5db28b4c2e870a39ff023e4205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1c2ca729a876c26130c9dda9286a3e", "guid": "bfdfe7dc352907fc980b868725387e9853c68b4efc105e2de2e7d3ceb51e9aac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e554f525234d74a41f6a61e73c581a", "guid": "bfdfe7dc352907fc980b868725387e98a8473b3693a6ee75ede363a092b64add", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989225b14fec9ebfda33b038d64cbdd976", "guid": "bfdfe7dc352907fc980b868725387e98f170c1345a02f6fa218d9153e7fc8b08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3c3b79a15f568ecab384de43f6df47", "guid": "bfdfe7dc352907fc980b868725387e989d2461733f5d7437ce547fb0f0bd4558", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c5ea82cf2eb447a4cc330f2f71553653", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988f9ca84699fd112ba3e9b8e3d8de5da2", "guid": "bfdfe7dc352907fc980b868725387e98b33f86cb67045f508cf2ace5a98d6bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93a2cb635ea698c19d67f4ae6f71649", "guid": "bfdfe7dc352907fc980b868725387e9816b1bb8c09b939ba28fdce5a0b98a268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66db0ab26b3d5e1d31190ca4cf7521b", "guid": "bfdfe7dc352907fc980b868725387e9820dc39a5d544b98fd5e56724280ccf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980094d5ed867c3759cd4ad6a13424ab62", "guid": "bfdfe7dc352907fc980b868725387e987fea851d30d2e76ca37fe8cb655f6cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98322b0f5ed9aed2dc03a123e7187e24e3", "guid": "bfdfe7dc352907fc980b868725387e983e7d9aed5ea8dd70a745196c3fde312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac9767462d241ddd9ba4e7ccd00b900d", "guid": "bfdfe7dc352907fc980b868725387e98cc890457a2f6ee0481b12d5821c2f63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805eb29c1c41c7435b15f169d2e3d1d7a", "guid": "bfdfe7dc352907fc980b868725387e98a2cfa2a86b5160ed3ab4b0751e230e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cc6cd781ad4aca526b219d90e59e667", "guid": "bfdfe7dc352907fc980b868725387e987449a7d3d6711189c31322c4a363ddba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889afbeb379635435b456968822d43931", "guid": "bfdfe7dc352907fc980b868725387e984560ced14dd67a0b0b0aa9351d88659d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d360aeed672c5a76f7c9c971c8b998", "guid": "bfdfe7dc352907fc980b868725387e98205d03e4651a24e7b82c9efed3955094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086cda43c62204634c0e293646c5ec2c", "guid": "bfdfe7dc352907fc980b868725387e98d07d7f42027750948251986290dc286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cfae49845ece538f528c9bf8c355663", "guid": "bfdfe7dc352907fc980b868725387e980aef27fc8b4247a5df43db05e928558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881963aa18ca79e73996e3dafeb5017ca", "guid": "bfdfe7dc352907fc980b868725387e98f27d34ee038512bf48712b585743a4c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e4e8f4d6b67c344b009a5e4761b0ee", "guid": "bfdfe7dc352907fc980b868725387e980eb550bcc03a690ffee730145a3883d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987343c7df73e880b1a629d5c0b7e375f2", "guid": "bfdfe7dc352907fc980b868725387e98be83f66df92c6bf6fd4bd75dd680b3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981311980ab9e710198e6baa20ee37a561", "guid": "bfdfe7dc352907fc980b868725387e98616d57624f2ddab6779ec3118a0b11aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882e536a2a0ab166ef2cb6d68bdc68092", "guid": "bfdfe7dc352907fc980b868725387e98010d359cd88ba87b6ae74f76583700f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a9989cccc9a8ec66c22f37b75f2e0bf", "guid": "bfdfe7dc352907fc980b868725387e981b38d5d55fde4d9fe1fde443b8a34c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870078a7eb149745e338f41cd72c1d619", "guid": "bfdfe7dc352907fc980b868725387e9814865e40f3a6969bcf218e4f80cae81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98178fe8662f6f3e15291be24781cf1cfb", "guid": "bfdfe7dc352907fc980b868725387e9897b970cd6c69b01f4c1406906297cc16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2106685a88fc45c6a766bc63804698d", "guid": "bfdfe7dc352907fc980b868725387e98ca499965967e8cbdafb5206b2bf3caa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5fe7bf2c177e7ea0c4c9c3483955970", "guid": "bfdfe7dc352907fc980b868725387e9845230914e97a9b04dc0fc28658e2bd67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e244dbbdeec1316fb091b3cac8dec9ab", "guid": "bfdfe7dc352907fc980b868725387e9894e802b6dfc20de05da7c8b6f92afa12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd5059ace80554d1774b9146f63d310", "guid": "bfdfe7dc352907fc980b868725387e9850112d6b0263163250ac7e0216ee460c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984538306d8d1faa57946ab11d7e245c5d", "guid": "bfdfe7dc352907fc980b868725387e98ae312ffcb316a9955768bdc4ea82df1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bbcc2cf9a0b7017b937e0f2e31ef06a", "guid": "bfdfe7dc352907fc980b868725387e983275b241aa919477874218fd448bffb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274af7dc4bf5584601a3f93b0a292ce3", "guid": "bfdfe7dc352907fc980b868725387e98b30b6825f205b76836fb475ed9a4fc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a46e9eb7c3e54a3c3351fc53d5f6fa8", "guid": "bfdfe7dc352907fc980b868725387e98002743e5ff7bb69a9944eea5373b7602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a50f808b20f4760f08915dacda3bb8", "guid": "bfdfe7dc352907fc980b868725387e98314c0bde4525ecf85bbcd48043b723f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2c8a47ace14060d0115d3685e13b00f", "guid": "bfdfe7dc352907fc980b868725387e9834227b514fcdfb509a53694ce72ab88e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874c4947258ace06d5c1e791bddbaa2b8", "guid": "bfdfe7dc352907fc980b868725387e980f24297b47a67a8153c561ca8afa8397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980526c207eafc1c4068790ddb4f4966a6", "guid": "bfdfe7dc352907fc980b868725387e982237441783b6acbcdd3fb1dc435f04d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d377e333b9733382c3aa7094b9048b", "guid": "bfdfe7dc352907fc980b868725387e989d1770b9a2f6b37f93a514cb958e29ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7944f0e12b3ba4d9d08c437a5f6ee6c", "guid": "bfdfe7dc352907fc980b868725387e98327d8d630b70bdfd6daa649feec67bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06d7916cbaba1d816cfc0e36599cab0", "guid": "bfdfe7dc352907fc980b868725387e983ee5b469a86f27b8e2c7aca3d4570043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98685802dfb4523b434b079196a62e03c6", "guid": "bfdfe7dc352907fc980b868725387e9878c1eccc3cc4fbc3910550194fe42f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f26621acd4083db93e9925d6556fe2", "guid": "bfdfe7dc352907fc980b868725387e9878a216d321f64171005b4a39d23418fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868ba1b1b104e499156b33c546c988ff0", "guid": "bfdfe7dc352907fc980b868725387e98221fdd9b392796c927bbb85166767248"}], "guid": "bfdfe7dc352907fc980b868725387e98a4aa9c79cd0ad589e5986c6a793c7569", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e982f53c31d49d3039187aca0202c785aa8"}], "guid": "bfdfe7dc352907fc980b868725387e985a87658fd90a1d7db61dbb5b1b82aefa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9811a6ce420babe3491436f103a769ee18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}