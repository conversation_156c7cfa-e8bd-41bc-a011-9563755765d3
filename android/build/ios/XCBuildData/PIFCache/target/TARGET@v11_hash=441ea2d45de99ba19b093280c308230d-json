{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cc45bc62c8462c3dcee6e10d6aa2038", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847654a8afab05198d0a7d1755c05aa42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847654a8afab05198d0a7d1755c05aa42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860aee1867ab94b52b2a75101ca552a17", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98185e92bded9b04e21f9c25abf5be02e4", "guid": "bfdfe7dc352907fc980b868725387e9857b9be4490be6f042feef2177171132f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846fa160d9689b5ffc941ea31665f00d0", "guid": "bfdfe7dc352907fc980b868725387e98773f9db85c53cf45934a2dec7f4d8305", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c20fa9db492b469e860ec602809b275", "guid": "bfdfe7dc352907fc980b868725387e986d6f837413a5bc6d9ce033aa161d4e1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5d443d3659ff366de17c319c11e51d", "guid": "bfdfe7dc352907fc980b868725387e988be93303bc1d73b379e8ac5e527f358f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823689b7804b180a6a3d6d458444b7864", "guid": "bfdfe7dc352907fc980b868725387e9811e61da5efdc46c49ea9e9347024cdc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483408f4bb03ea036a13f91cb23bc3a1", "guid": "bfdfe7dc352907fc980b868725387e983b615f05b4d7227cc5ab8bee00617b30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53619a184b7bc1e5cc08514fed09a9c", "guid": "bfdfe7dc352907fc980b868725387e98d267c00bdb03910aff17969c8dc9509b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de760d7e514a41d89a4fdf644e5166e6", "guid": "bfdfe7dc352907fc980b868725387e988153fb10e7827d910d4e9d51409bd3f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb16d1269acf82c30573781ae5e6531", "guid": "bfdfe7dc352907fc980b868725387e986c63d42787468b59d034edb8d8187220", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009800e8cdf9f963f394566bb41e86c1", "guid": "bfdfe7dc352907fc980b868725387e98732a576c62a0702582d548a9c6991ceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f92ca130879f8a0cdb5b4537f5279d3", "guid": "bfdfe7dc352907fc980b868725387e986c0667d117d942af0aa4ab9a3ab1b694", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041532ea91aa3f6ce56f02fdc83d19c3", "guid": "bfdfe7dc352907fc980b868725387e987943766325ce8a5108dcf589e9f18de5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8ef0296fcecfdfe17c75755b82bc87", "guid": "bfdfe7dc352907fc980b868725387e98a36660d829a9569acb1789e68546f9d5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e40f47e2fc40f123e834f30b860dfc1b", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a490483152a9c63ce5ee3438b2f0ccd2", "guid": "bfdfe7dc352907fc980b868725387e9880fe23f1ef3f3fbeaf6971ffdca2bb03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983282c679a9726f957919a72597a94612", "guid": "bfdfe7dc352907fc980b868725387e988f958238505193406a0fbe430b71c4c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e76a9d1b9f0b2b23ae4ae2a4e16e026", "guid": "bfdfe7dc352907fc980b868725387e9882c8ba8e51bd7a612bdc5b3bfd3f55bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f9ce7f208db7bc28dde4991151b6f6", "guid": "bfdfe7dc352907fc980b868725387e98ad0d77200164c8cffb3105bab32ab5f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870982a93c5461c7026bd424dba9e05f4", "guid": "bfdfe7dc352907fc980b868725387e985cd904b997f940df0ed18f6f500aa13d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c2941213bde6775df13c943c707b42", "guid": "bfdfe7dc352907fc980b868725387e9845c81c7bb26602fa282cfe822315c4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c470bcb618fef71f06acfacef71bcb1", "guid": "bfdfe7dc352907fc980b868725387e98c512421a9fc09eb498ab1ff56423ae47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ceb0247e981ed91aaf51426ba6436a8", "guid": "bfdfe7dc352907fc980b868725387e9852c43a24fdf86126c6e7fc8b1942319b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c0b139c232866dfc4484fa6ec827d9", "guid": "bfdfe7dc352907fc980b868725387e98d261942bd7034f65cf79384bbb8ae11d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982817dcb0b9f8c6eb7c04c71c293444c3", "guid": "bfdfe7dc352907fc980b868725387e98ce978135d3591807824d42dafb975f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989203d982fac2a43f30dc336de2c4e516", "guid": "bfdfe7dc352907fc980b868725387e98310c9524b14bdb4f80b9f8297c6e521f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b209c9490fa31c1cebc35bcc83a18a98", "guid": "bfdfe7dc352907fc980b868725387e98be9ca2949f5445df03c872d69e5cf67c"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}