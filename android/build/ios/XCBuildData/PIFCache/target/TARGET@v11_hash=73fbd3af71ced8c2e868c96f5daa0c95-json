{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893c40168e30c07c5fc80491531b2ccf6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9878a8c384dba2155b0da9eed2f9909c79", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fd2f82243ced7a8b9d8adba6da56ae6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982aff6deddf3116c30dcf05181a845fcf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fd2f82243ced7a8b9d8adba6da56ae6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c07028e31dda5e9f431eed2d61798b44", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7ca3eb6764e81678cea835ddc879dcf", "guid": "bfdfe7dc352907fc980b868725387e98aa9fd2dd0147133adaf178f4927931f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844435add845967353389654bb5c7485a", "guid": "bfdfe7dc352907fc980b868725387e984f257d1c832d5dd0d67c2f6f71e7cfeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ba1f7f5ad3b05172d28f7bcccc6e347", "guid": "bfdfe7dc352907fc980b868725387e985331b0167c31657001f6c8a6f967ffb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d14e1264c1e1f136779ab7cc7002098", "guid": "bfdfe7dc352907fc980b868725387e988f4cb53435d370c752e16434a960d5bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed65f18c31a89b0c8222a49a82c25eb", "guid": "bfdfe7dc352907fc980b868725387e985a925547ef1b4a8feb0c2fb6f588b0ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a007602e7c4afa84b6b7691d00165f", "guid": "bfdfe7dc352907fc980b868725387e98652df605064a4b915399e75a06bbb1ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b00ba5b07d016f1127c6eafc17fb107c", "guid": "bfdfe7dc352907fc980b868725387e987d55d5a8bb100cab773e41bb0a1d16a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd89400bb6dd88a2d4d4a950934a1643", "guid": "bfdfe7dc352907fc980b868725387e983af6988efa4a45b41c8048ac104f2979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01103b24774e27fa1742d2a91878636", "guid": "bfdfe7dc352907fc980b868725387e9898af757bae29f793cf309cab1c918aee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb58769fdd07e620f3a8322e6e4dd10", "guid": "bfdfe7dc352907fc980b868725387e9831f1852bdc4c580268c3deb88de03c2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1a672f0321aae56bb681550765b3fd6", "guid": "bfdfe7dc352907fc980b868725387e98d5197e86815ff48ec59d1c647fcc3ed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29a38d2b7b15b4882a964143538dd6d", "guid": "bfdfe7dc352907fc980b868725387e9807ce56fc279b9cd755c1daa24837da1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98251147ba8f749d454b848f400a147717", "guid": "bfdfe7dc352907fc980b868725387e9899b70c2c90b3fa246b0098ff53fecf16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983590144c087a0cae25028bc74a21a009", "guid": "bfdfe7dc352907fc980b868725387e98520ed411827ba3e2e8961206263a8717", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872984226e16f6745fe8d9da24731f350", "guid": "bfdfe7dc352907fc980b868725387e9898eec8461766e6e0e052394ac0a51ef7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46415c23d2a7d890fcda21709dcccc7", "guid": "bfdfe7dc352907fc980b868725387e9836debb7f7d93c887e2380bcaccbee682", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b454060448e4b5c4f9eb32b5b3b1c8", "guid": "bfdfe7dc352907fc980b868725387e9803517f380829a4757c78292c5a91bece", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fc33c4a79ee159a2254da220c7794e2", "guid": "bfdfe7dc352907fc980b868725387e98e8266c9aa12757c2e833185f1aeca0ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9aa066a07816f9407c303e5155e6d71", "guid": "bfdfe7dc352907fc980b868725387e98a51499c53e5e37f35df329a151df5332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79eaf066a1accb43f06905432733e79", "guid": "bfdfe7dc352907fc980b868725387e9876cd3ee39b6e2a6d90d868bf36f24ca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5ddd74c03449ab6ac9a3cb85975eaed", "guid": "bfdfe7dc352907fc980b868725387e988add2e8fb121d3b51fbc97d05bc2fd0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860cdcd8f6b70d8140da6cd82ea722a7b", "guid": "bfdfe7dc352907fc980b868725387e98368c791127ab31869cbe4bdf4ba2b515", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a984921766f6f596b57f478932e674f", "guid": "bfdfe7dc352907fc980b868725387e98af469dcc8ee73d62420bee70a688ee1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8e041dea8b2f3ebeffd5fd8a881635", "guid": "bfdfe7dc352907fc980b868725387e98e4f059fb2d20e9cb6fc0c834f6ee4513", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861bbf9be6824687014a19b172fe4d9f9", "guid": "bfdfe7dc352907fc980b868725387e9893a6c5546b629c456b5ffa1dca4e2f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca90e6d88c1beeef875056b461ad6fec", "guid": "bfdfe7dc352907fc980b868725387e98224a77e64dbd45bed21eb6419f0a5feb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3b35a389ae47c2a6093c91f1aed913e", "guid": "bfdfe7dc352907fc980b868725387e986effede6369b24403d288a09891a6a73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d628ed9bc63901da9138eb3d4808b48", "guid": "bfdfe7dc352907fc980b868725387e98aed6ed54b00dc1363d8923b5d6a46d21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078a485965bbf1b89af167a73bdd0328", "guid": "bfdfe7dc352907fc980b868725387e984ce4442d3a3e316a5386ac90dee9d8f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f07486d2bdda8bb7c1a909c6ea40a7", "guid": "bfdfe7dc352907fc980b868725387e98c752f521be3619b7a96ed14b9942e513", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986946e17a0c8c8e3e6bb2c796940d771a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870640cf4d7ca485bf9aad65d2ac58a96", "guid": "bfdfe7dc352907fc980b868725387e9859d56626d048f816140261931868c525"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836be8a9b1c39a69f3a221e200340f07c", "guid": "bfdfe7dc352907fc980b868725387e986848d776b9e2f5eef30722c608ead75a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb72dd4394abdc44a8cda7753c92f06", "guid": "bfdfe7dc352907fc980b868725387e98835675f9cd0edd31558aad7e31c6c4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873a823eb5a962d0acb6326f1aa2c5349", "guid": "bfdfe7dc352907fc980b868725387e9804ba28b5a3387497f6c11e1339fbda57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c8b3245dfaf60a725419b965c0f73d", "guid": "bfdfe7dc352907fc980b868725387e98af0a8256a0f3564819105e2f9a4d6fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d176beb6d2e6f7ca24fd2f547ee6889", "guid": "bfdfe7dc352907fc980b868725387e982900fbc4358ea94ea437de0c4c54157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f87d18aa37dd0269597da3f767ffbd0", "guid": "bfdfe7dc352907fc980b868725387e981d6fffe668b77f37dc0009f8a13f4bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825718f9e1234d2eace5abd82d1afa061", "guid": "bfdfe7dc352907fc980b868725387e9853673959310f119e4a0239709f08c75d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044f4f6ca3fd1e477da95384a1099ef3", "guid": "bfdfe7dc352907fc980b868725387e981aa88db93e5b13ff9b5ec699b3a5bb3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809041f7fcf650c413bca379709bf542f", "guid": "bfdfe7dc352907fc980b868725387e985601a3158c00dcea230b12a0ad9b2c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98181a4e6ac947c4341efcf66f9da5cbcc", "guid": "bfdfe7dc352907fc980b868725387e98353d3e2bd956d692f5cf6512a429d82b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a65415b0034dc61699b1963cdebd21", "guid": "bfdfe7dc352907fc980b868725387e983f3ad64c5ef4d8c8a205d87a2a20bb34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d961e764f22d4fb575ef0bda8419d8b", "guid": "bfdfe7dc352907fc980b868725387e984a23530835d34e4a18bb72c495030b8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbaa70553fdae59a5dc8993e80298f46", "guid": "bfdfe7dc352907fc980b868725387e9832c4c36f1244950b53f5653e36cd81fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0eb23f824963a78ac829813f6b4db87", "guid": "bfdfe7dc352907fc980b868725387e98bcab311550e50385d42180e9cd6fe993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baffce1ed2d278d650383a93d75f0a6a", "guid": "bfdfe7dc352907fc980b868725387e981236cc5e945f093e0f440929f8317318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d6e2d9b82fc3ed7cf35284687a56142", "guid": "bfdfe7dc352907fc980b868725387e98cd0cc99968159b91dc7ebf7beb290623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a7e17bf6ba473f8c57dfd56d164d80c", "guid": "bfdfe7dc352907fc980b868725387e9871ce4464f673dfc0f780a6cceded6290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef81f279d0e4b839069445eb3844f80", "guid": "bfdfe7dc352907fc980b868725387e9867b47cee235fcfdfc1a1b918d62951e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98659be785e36453f534030ac903c1a796", "guid": "bfdfe7dc352907fc980b868725387e980d6b3490423f6816e8104b88cf572306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c18afed2550026a8245b463cfd4448e7", "guid": "bfdfe7dc352907fc980b868725387e9817e356da27d48b400874736710fc9ba0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d08171e7aeb3440fe3c6c0955102aac", "guid": "bfdfe7dc352907fc980b868725387e984a7b8cee5a6690d56bd6050cd4ee1273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a059a0f86376d0b96d08dcde85624873", "guid": "bfdfe7dc352907fc980b868725387e98ba789f6ccaf1e972854f194b00f7aa9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982493de46561ef641cb894f80cf4f2e33", "guid": "bfdfe7dc352907fc980b868725387e98f9655d71790b8dbd09d06ebea924591c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed8fce425464e665222f08acb2d2f33", "guid": "bfdfe7dc352907fc980b868725387e982d8da5ca0a6a825523a25627f6dc4494"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb9e5a8a3ceed22c9b9620e5270dae4a", "guid": "bfdfe7dc352907fc980b868725387e98d95373e23acd1a9e07ee081133ad4eb3"}], "guid": "bfdfe7dc352907fc980b868725387e98292b2959989c48117ca5b4d0a3396ff2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98689b9a34162faa3864ea1fe0ca03b22d"}], "guid": "bfdfe7dc352907fc980b868725387e98c9ecb260a151256e8a0b0d37dffd4b5e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980aafefec89131ee1483f51620e16f29f", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98e2a8b398a5879b225f86a2c77549810c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}