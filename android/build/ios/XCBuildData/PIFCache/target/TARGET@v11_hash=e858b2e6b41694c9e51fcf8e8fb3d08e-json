{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891417992cb87de0e04648d063361be2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a6a220db6b6baf200406d26e8239cbbb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983612353379ec42879e967d2368da3007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98abcc6a5708c07186676eec557f5ed8a0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983612353379ec42879e967d2368da3007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986dada52cca593f50eb62f29fe82d7661", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982abf151ea94cbdb9c1beeed79d27191e", "guid": "bfdfe7dc352907fc980b868725387e9892c9f777d1157d2c21f49d944244f091", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98653e2d774a034ebb42d0cf29647bd569", "guid": "bfdfe7dc352907fc980b868725387e9888034544b0f57cab03622ad13d05180c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e143714ab903e10bdc250852ce4abe", "guid": "bfdfe7dc352907fc980b868725387e984b1ec68b946b7eb8dfc92593074bf919", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839802e0cbf9a859382c296813529da01", "guid": "bfdfe7dc352907fc980b868725387e9849631f612fd138fc951d7a75b905c2f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91dce245706e291630510c350fa7181", "guid": "bfdfe7dc352907fc980b868725387e986e337d790dc17dffc19586eb4036dea3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6f617155b5feb0c34c22df57fbc148b", "guid": "bfdfe7dc352907fc980b868725387e98448f8812bf6295fd9d0d810591b8e593", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a399825fcfcaa7520139865fefb6421", "guid": "bfdfe7dc352907fc980b868725387e98a1b0f2980fa02dcfd7e684cc4f71092a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982140455e4b4a149ecfe0dc4934802918", "guid": "bfdfe7dc352907fc980b868725387e986147e3d59ab2a2380876711197e8bdf1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98198b7ecf1a04aa7659854f3e49cc99ac", "guid": "bfdfe7dc352907fc980b868725387e9817c432dee472acb0b5c84bbed3c5cade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d0ff68ef4b0bce6b9a82934c2f5ad8", "guid": "bfdfe7dc352907fc980b868725387e98508e29162167e3c2bffe4ba8a5ac35eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3a427d67964db123c0b45c94a3684f", "guid": "bfdfe7dc352907fc980b868725387e98692d5bb337b7451f874117bd4c11120b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c90263247eae908b4071c7cee82dfc", "guid": "bfdfe7dc352907fc980b868725387e98e4b267560d428033b827e95c5d2eaeb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f2607a47bfdad133e849c7cc2963e2", "guid": "bfdfe7dc352907fc980b868725387e98a38c5883b2fcc344852cd8724786c052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844f65ad92e437de11d0edb109168325d", "guid": "bfdfe7dc352907fc980b868725387e98bd0e7f20471cecf3c852027ed3a6badd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0273ff49e01d42871d17162219c9bf8", "guid": "bfdfe7dc352907fc980b868725387e98ccfa0e9e7c64a2fc1fb21abde78e2f81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d5371ac5f9e371dbc123dec77cb75b", "guid": "bfdfe7dc352907fc980b868725387e98b4be9b4ca4c67e58e31b856221ff715d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be3780214d3bb49e6e2fafe701032c68", "guid": "bfdfe7dc352907fc980b868725387e9855b6e15d8dc01cb1df374dbd4bd01969", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816dcd1809e1d78e9df09ab459cb07d9a", "guid": "bfdfe7dc352907fc980b868725387e98807b59b0c2934c7dc995f76622<PERSON><PERSON>ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7ea018617a6fc29d9071d79ee9f00a", "guid": "bfdfe7dc352907fc980b868725387e980bbda9671171de18389bbf18038d1182", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eedaaf69f69f37c9cded4c1d0595bd2", "guid": "bfdfe7dc352907fc980b868725387e982dad5579a4ccb6fac0210f00144e094f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3d86837130c296c93e0c533acbbb03", "guid": "bfdfe7dc352907fc980b868725387e9815a10ddc4c2aa054e5f0fc171389cc2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98993f9479d519d7a17ebab9e82ecf673b", "guid": "bfdfe7dc352907fc980b868725387e9867c284f5385c99d02ef6303c38f5a2ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980adc80373be48a18414602e8b44a196f", "guid": "bfdfe7dc352907fc980b868725387e98970bb821b0361a7920657733f8abc3eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982437a44465988dece625059f4f7e8f8e", "guid": "bfdfe7dc352907fc980b868725387e98b92f9e39a500097659e72b523f07dafc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed6c738bbca30d948e0879603738332", "guid": "bfdfe7dc352907fc980b868725387e98e71d709ec1b3161413759a97f72ed71c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf504c2849260a0f820e62226c2560e", "guid": "bfdfe7dc352907fc980b868725387e98be92b00c2bd1af61a6e7442ec8c7930f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e20e02975dc937fc4992ffa8efa1a9", "guid": "bfdfe7dc352907fc980b868725387e98919f0c0acc05f817e2d43f8c2f8bd9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fc602a84f687dcf960f124c97d3fd3", "guid": "bfdfe7dc352907fc980b868725387e9860490bae246ba7868ca281211204a6b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753a579621baac1c57964cda7d06f5d8", "guid": "bfdfe7dc352907fc980b868725387e984b2664c74c26be742d3dec8b3473676e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0451ffa87ca601a2571830545660f0", "guid": "bfdfe7dc352907fc980b868725387e98bf61f9dd303f2f1aa21cd8b2b5376f30", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e561920d2cd248e279993d1c0466939e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ecb8dc2d3dcb2bbf9538a7f1d4d19e2", "guid": "bfdfe7dc352907fc980b868725387e9822d5317defc969fac618783353c0d6f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabf0fb3701ae6816cd025df0c162907", "guid": "bfdfe7dc352907fc980b868725387e98433bd8e41fff41187c9a9e5247ee8520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2decc922894ad0397e329fa9578a58", "guid": "bfdfe7dc352907fc980b868725387e98e12765780b30f3f17630da2baa0f0de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98386e9343f52d39a69675f30a8e1b8943", "guid": "bfdfe7dc352907fc980b868725387e98caad89f67b34ce1a2d6f08a89f812c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b4d66a1dbcaf812ab6c61ef36f65b5", "guid": "bfdfe7dc352907fc980b868725387e98ca165f1cca1db4b888e2885d93492e42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988582527fa07888da531798401b42d877", "guid": "bfdfe7dc352907fc980b868725387e98b286a845f17c3937cf51c5c92ac82cbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c33560475bf8edb7bd4e7c54c2808e", "guid": "bfdfe7dc352907fc980b868725387e982064cd247f782c67d4a5103d9e6c2dc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d6468cda039bf403591dd14ca1798a", "guid": "bfdfe7dc352907fc980b868725387e988e4146aa177e9a081c952e7bfcb76ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984450d2f5997dba4f8f49a15eef121be2", "guid": "bfdfe7dc352907fc980b868725387e985ba251cc0eb614f658d0822ff77d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b350e7110eebf47c1c3b144210e0d158", "guid": "bfdfe7dc352907fc980b868725387e98bc3049a408e42e7aabc15707b1cceb01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de96a1108e335a27288c80c75409d91a", "guid": "bfdfe7dc352907fc980b868725387e98dd80ccb849fde37f8575ca14b8332827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988589bfadd7ab20eee2993103e89b660e", "guid": "bfdfe7dc352907fc980b868725387e989f81f445bd0459e5393d0c57d805f730"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699a476bb7f99ea31e3787fe6a4ae91f", "guid": "bfdfe7dc352907fc980b868725387e985a39944db3b8877e4f8a15e3728175b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804acc36184d7c6a9906dca705444d0ff", "guid": "bfdfe7dc352907fc980b868725387e984857d019020ff1d77f1fc01dd65f477a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989658df9d74b66b02107d38db4b2efc33", "guid": "bfdfe7dc352907fc980b868725387e98ca7f01471cd9a46ac8e8d4356f2283bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2881be5defc06402ebfaa5f9155afec", "guid": "bfdfe7dc352907fc980b868725387e98b2167c9767266a2c59ec5e8b3e9a49a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66a32c3d9fb631da29dd5d0c330bf28", "guid": "bfdfe7dc352907fc980b868725387e983f018a18f7a3cb84b689ac0eab529995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def0e2891af5d4775321ee56d641fb1d", "guid": "bfdfe7dc352907fc980b868725387e989e6044fafb0f234333a796e4bb4b26c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321653780c6d9de989e61018d499fc85", "guid": "bfdfe7dc352907fc980b868725387e98edda3fcc7be2bbb3016d9941b3343807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416c31da60cbb8222cfdb352253ce288", "guid": "bfdfe7dc352907fc980b868725387e98b96c61a7d35ece3de1b133ec44331741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62cdf0b2c62cbf57d2bf7d5498be902", "guid": "bfdfe7dc352907fc980b868725387e98210d78e180d3fb42ccde2c4c44cf71a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe1c5980fd3681846186f3b2aac4657", "guid": "bfdfe7dc352907fc980b868725387e9811a12a0a717f08c026e6be4f3da26c9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981120f86e55d9d48b5139fd7e34de841c", "guid": "bfdfe7dc352907fc980b868725387e98154608398d0f0a4109148603b920c049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3971ed3fc38e3252a743cfadd48e1a", "guid": "bfdfe7dc352907fc980b868725387e9853964814394775813e3f00f2ab57650f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf003b3072fdada796f76696af29b3e", "guid": "bfdfe7dc352907fc980b868725387e98bba1a7f243847d59cd634bb9cf6a1215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e0067ab5c922076fb78b5b8cab0cbb", "guid": "bfdfe7dc352907fc980b868725387e989d7c39bb151bda21ea6289115df3188a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7506e1ee72dd3b0f047fd96a3992c68", "guid": "bfdfe7dc352907fc980b868725387e98c3e09c89fa3900f01b12b1c58a066c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35bd38e604f22a29f47d6e6d9103fa1", "guid": "bfdfe7dc352907fc980b868725387e98dc4968ccd1f94172a7e5d8bf5bcd7610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96b18163ee0706b20cb0e142c916675", "guid": "bfdfe7dc352907fc980b868725387e9853cf374dcb121552d349f732543d5e14"}], "guid": "bfdfe7dc352907fc980b868725387e982bb78becb1ddd8200af0f483ae78108a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98011ced3427d9445329df4c68ea2a39a2"}], "guid": "bfdfe7dc352907fc980b868725387e9899177693cecf10b2d53c3bb0759bfacf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982312ef8638f28c183860170c7142eb76", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98798b3b6d8b6c866f4df1822d9151c325", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}