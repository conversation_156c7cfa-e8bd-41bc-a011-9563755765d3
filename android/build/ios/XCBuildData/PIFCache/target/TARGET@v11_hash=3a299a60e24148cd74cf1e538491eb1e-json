{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc57acc85393e677b2893bbd5a8ea7ca", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0154acc7640775d7ba2d428c532f409", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986eddd9f5a009e15cd5135ef0911bf123", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988e6f696e74b933375794829338077bdd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986eddd9f5a009e15cd5135ef0911bf123", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980da1167392d65fbe3000c5055fd3b7da", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f033ff3f6c0d223fbf3431a612454eb7", "guid": "bfdfe7dc352907fc980b868725387e98603843c71577c889888b4da195bbce0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb837351051bd73f59bd2cf6d19b576", "guid": "bfdfe7dc352907fc980b868725387e989a631f1b0cfb1bb3a26a404f3f4f6562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98702fb1f4ce6faf3839481108ade504d5", "guid": "bfdfe7dc352907fc980b868725387e986c9b61ed97248b0f7445d5ddf4809746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f429cd8fca45c205421ddca09f579c", "guid": "bfdfe7dc352907fc980b868725387e987692f33f72492d00b943130396aa8beb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843d35647af2db38060040a81720af3d7", "guid": "bfdfe7dc352907fc980b868725387e98b15dab59398bd294d691c4aa765a3016", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f39ee1a469d0947b38850d6bc4c3acc", "guid": "bfdfe7dc352907fc980b868725387e98d26780e68144f48ab05291dc66f38700", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa552311f7c8032e69337b75b2eb7892", "guid": "bfdfe7dc352907fc980b868725387e98c876b33e874d17f53db98465587a96a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb8ad44d72f1e431c9f33f765c02846", "guid": "bfdfe7dc352907fc980b868725387e98729527e56168ced3647d6d85a5cf89b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98317655eeed37e61419fac8ea265257f6", "guid": "bfdfe7dc352907fc980b868725387e9823fa5d22f04eea951867a1ffbbc8ca16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec1d5c5fbea25919aece068923c28c5", "guid": "bfdfe7dc352907fc980b868725387e986b956d50380d8098675695ce1ed37937", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814dcd8f64de6a7d5c46604a76b028f84", "guid": "bfdfe7dc352907fc980b868725387e98083adc6838f0b972ab06c820ddfb8804", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893575d4c62aa2976eeaefee79bcb5367", "guid": "bfdfe7dc352907fc980b868725387e98ff68d41c39d437e29005277eb071b0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb614c5d620fabca0d5892e1f3074b5b", "guid": "bfdfe7dc352907fc980b868725387e9823151ca7b350cbcdf96b825308836082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98582be92ba4bf4a9758774915cef6979e", "guid": "bfdfe7dc352907fc980b868725387e9879c3ff46a0a5d37e0f91c1fe679bb02f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ecd7c6d60f4c940e07bb9b720ad365d", "guid": "bfdfe7dc352907fc980b868725387e9861074bb6a2585b2a0a42baa195cefc80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824379728f81a78f2bfd60d2e69cf8c0b", "guid": "bfdfe7dc352907fc980b868725387e9879f010b4928f1f1827ea36cf4484dcf5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bcacf983cbb29062bcd2eb8787ce38e", "guid": "bfdfe7dc352907fc980b868725387e98adbb9e38765e479f152482dd3e2d69d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943c0d4622d15e94040ef5780d61f306", "guid": "bfdfe7dc352907fc980b868725387e983ac95c9f48fdae1daa3c1ab7f88e6e12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812973811fc4c100d0efb14c1793d5810", "guid": "bfdfe7dc352907fc980b868725387e98a996346df0d9de219ac82cb8955c7a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b8c21663fffdf5236c566996bcc652", "guid": "bfdfe7dc352907fc980b868725387e98cd34997bdf0d8fc75ac0db0dd1cc8e43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aaa1e089ffb284828fe977fb211904d", "guid": "bfdfe7dc352907fc980b868725387e98481abf4168e5b1dd9ea8bf09bc5ae8ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1ec6ec8c50ebe9181db3d56a16c4a9", "guid": "bfdfe7dc352907fc980b868725387e987fd0b9ef1178cf6acb80de13b5bf71b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d3c3525d26044125bc8711dc760651", "guid": "bfdfe7dc352907fc980b868725387e98e37c7d44bb09477510c85f243eac3abb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5413e8f6b218f2af5932211e71724d1", "guid": "bfdfe7dc352907fc980b868725387e98b04fd10f44689e212c4d3678cd3ae556", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5bbfac90cfcb724f006d11c1f993b8", "guid": "bfdfe7dc352907fc980b868725387e9874435646d69ba301a622095565a0368a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f680a8160e28cbd2547449fea0c3dde", "guid": "bfdfe7dc352907fc980b868725387e98c1b4d9a2146f514c7f3d0cb8e956bc63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc031531d01ddf55373cea6090601cf", "guid": "bfdfe7dc352907fc980b868725387e98b41f73c41510cdb51bdb2689ba591495", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895768e620978b4233aa3a197934c549", "guid": "bfdfe7dc352907fc980b868725387e98e070e0a48df1a7ae73e1e0ef0de8f42b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ba1a406695693b3dd714113df32d21", "guid": "bfdfe7dc352907fc980b868725387e98df95809a0deb7ea12ce2c37417ff59e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98469326a65331ba47b742ea35dd44e53b", "guid": "bfdfe7dc352907fc980b868725387e983dfa8ff964461f5aa477460cfc318168", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c75b343c39cd0177f38eebeb677a18", "guid": "bfdfe7dc352907fc980b868725387e98de61cee893443395d200e72ad618e303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de32629e6bb0f3483f7372575e5b389c", "guid": "bfdfe7dc352907fc980b868725387e9830c659935f25b82a986cecc781d628de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162cc307857801d1712aabca797fbc2c", "guid": "bfdfe7dc352907fc980b868725387e989335b8af1e652bb48f16482d7a0c1ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825276a4b52039b50fdcac3bb26423f5", "guid": "bfdfe7dc352907fc980b868725387e987ee957798de75a884ca62b0f72cd246f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988b50d06995b747b70c54dfd0e57173d6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98779779e0d019a80b204066355e14c0dc", "guid": "bfdfe7dc352907fc980b868725387e98c7f6f2a268ee0fa9243f4569e8b32d92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945abb890a417c283eb8c8f6041161fa", "guid": "bfdfe7dc352907fc980b868725387e9895acd3ba7dd061f3083d252955d1f75a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159dae899515410e2776885858dc3aee", "guid": "bfdfe7dc352907fc980b868725387e981514d898423cbc5b7854797fa5e2f0e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf340e02527e6a0d286e57b57a27f9bb", "guid": "bfdfe7dc352907fc980b868725387e98fa0fe1dd6021e1ca5afd11f46189b24a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3b52a9024ad966423e5d78c7b0a2d5", "guid": "bfdfe7dc352907fc980b868725387e98b8b0d25d9251bd478b1e4ef6b110ffe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c65af9ed2f323777054ef88e23aff9f", "guid": "bfdfe7dc352907fc980b868725387e98103c820727fcb99b8fe15709b24cde19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247b0ef6e9f034d3f54f6fae9c970480", "guid": "bfdfe7dc352907fc980b868725387e98c190cac58e0bc69f485605f42464d093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131b034679c95acb9b377840eaa4fdb8", "guid": "bfdfe7dc352907fc980b868725387e980815422185b8b90e64ab69c81d4c1114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ab40b0513879204cf483ab406af1fb2", "guid": "bfdfe7dc352907fc980b868725387e983f175b4e7497d2f4255cc79dd03e691b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984777852986d084e6f44dbec27b49b984", "guid": "bfdfe7dc352907fc980b868725387e985ba34ba3ab9ebcf3b12861b884e83834"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98294beb3979e774e0f8d732dd23159840", "guid": "bfdfe7dc352907fc980b868725387e98fda58caf702c697206148ea4e8fa47fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984075e52955ed41d4a1a514a39aaa4f86", "guid": "bfdfe7dc352907fc980b868725387e98e0f30dad5e82ad1bd274f877d6bc9aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632574d3614668160138414fad6f6cd3", "guid": "bfdfe7dc352907fc980b868725387e9836898fef8e0e0d39f58a3540b35d7588"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896cbbd0f00ad1ce20a701ef110549c33", "guid": "bfdfe7dc352907fc980b868725387e98bdb6a263bcd6bbd7c040873dcc68ce30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc261c807bcebbc1572cf99e9de58112", "guid": "bfdfe7dc352907fc980b868725387e980ab7346462f3cc5e603227cec7a0cb35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98026d0c7ca8c9aa7aec0a2aa6a77708cf", "guid": "bfdfe7dc352907fc980b868725387e9818a5007cf21e7eeb24ca559da6cb8d8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c4aa6441de3c3b6dbab73b2f0e01fd", "guid": "bfdfe7dc352907fc980b868725387e983f067d4eb19971f6d6ecdb889590612f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b71fb7c8a359e7840b6983a07f32433d", "guid": "bfdfe7dc352907fc980b868725387e9807529c25758004055144a06101f447ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986592e744de77f2976550a0b2c8c8958c", "guid": "bfdfe7dc352907fc980b868725387e98307ca4d6a038c06cfd8e28c12b685abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e81028171a416ab32d1685e82c6039a", "guid": "bfdfe7dc352907fc980b868725387e98fbbba676b393e87f39d371f192464893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf737f69339d2981ab7962d8436d37d4", "guid": "bfdfe7dc352907fc980b868725387e989437b0491e884522517d20b8fc956797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803ab9ce4bb7bff496d9078801c2f2088", "guid": "bfdfe7dc352907fc980b868725387e983298b8663e833d9a8dcd2a07be71f7af"}], "guid": "bfdfe7dc352907fc980b868725387e98d641615f3fe459db51bd49702356e5be", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98cdeef2620a001e0a390bec9a28b2d3ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e983f23fef75db955b1437ec95c45c7d81e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e98ea7515522cce33587244810959752e71"}], "guid": "bfdfe7dc352907fc980b868725387e98a71ad5743134b4bc96841b8b12799a34", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98feca33ed130df24b3e4409ac46598436", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98566a0fb5f475c81b1e34ab6d04cae5bb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}