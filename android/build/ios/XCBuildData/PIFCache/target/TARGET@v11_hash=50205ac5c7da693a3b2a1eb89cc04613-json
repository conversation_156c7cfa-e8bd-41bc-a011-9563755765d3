{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838d9204ec3384813193ef649139665d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efbf0289064c70cff9716571b4817e90", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98169f80ff378cd44085dfe771fd40b2ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c35e8fd75597be9a3e2a54e9f04cdb40", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98169f80ff378cd44085dfe771fd40b2ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98117766a9e832e77497c56525b774ba88", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d007435af6229c0e962d66eb9c5a770f", "guid": "bfdfe7dc352907fc980b868725387e98979a868ec6cb25a6822a4624f9dc5c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220aa86dfbb3f7ca4a7f42cfd43e673e", "guid": "bfdfe7dc352907fc980b868725387e98708f97dd10be9b986c9bf25f380f3fe6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c1bfde4f4172365acd55bd939a411d", "guid": "bfdfe7dc352907fc980b868725387e98424ec3a6b2e8d464d1b5754bd1af672b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706d003f8d4278b9226f436744b4d2f6", "guid": "bfdfe7dc352907fc980b868725387e98e7c956a3a644d7fef458daa1aa41b7f2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ba1edaa7b384d7a0795da0d9acb3ad", "guid": "bfdfe7dc352907fc980b868725387e981a8a3267e7c0009456bb6d848047b37d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef46b4427e6ae988f3d9f7460e2ff8fb", "guid": "bfdfe7dc352907fc980b868725387e981d4cb5af5bec94320728af90cefbd4ae", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b2915826d233d4dbbfa0d4ea70457f", "guid": "bfdfe7dc352907fc980b868725387e985d9d489685fb2ccd77365e14c48f8e7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988feda61e869b2f3c22c7f3f5abcc44e4", "guid": "bfdfe7dc352907fc980b868725387e98315cb22437aaba27c96fba18c148ab40", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888267e11d2a3d97f1c663279dee60bfb", "guid": "bfdfe7dc352907fc980b868725387e98037620737ed58dfcea8127b427e4addb", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d59ea4ea23bf3c8164d53687897559b0", "guid": "bfdfe7dc352907fc980b868725387e98be0b93645cb9354109376156247f74d6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21ff2ec291b9b338f4d7f3979cffab0", "guid": "bfdfe7dc352907fc980b868725387e986a73e798bbdbc9c4c3c18283d4120fed", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0ab6074a974f78d3d37bebc9861e91", "guid": "bfdfe7dc352907fc980b868725387e9835940d8e98ad1dff2ca4c401eb4d185f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989efba2c667ac87c60df0e882f3a1b96d", "guid": "bfdfe7dc352907fc980b868725387e988fbe302129b4ce88f6f79b6f993e7f3c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf20862b8a239e73a9fc1ae9bd385740", "guid": "bfdfe7dc352907fc980b868725387e9824300c5cf4faf6d36601f55bc13759e9", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98b88ef27fd18bb6e3876c3f43db5901ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98950d3b9cbd75d0feec6b75e0356ffe4d", "guid": "bfdfe7dc352907fc980b868725387e986c038aa933ee0897ebdb9dceea5baae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a3ecddebae1a5f6c1e7c400b83bc40a", "guid": "bfdfe7dc352907fc980b868725387e981694e3155fcabb035bc32b79337675c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d494a6eb34e18d7bce5a712d74156ec8", "guid": "bfdfe7dc352907fc980b868725387e9837e8d7d3ac231f341de6ac111c8efa41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd3470b46f9860d2fd9e3d71731ff99", "guid": "bfdfe7dc352907fc980b868725387e9824fbd39378fdb1c32e3438174c654a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e0ee855b2f1761787e070f5168882a", "guid": "bfdfe7dc352907fc980b868725387e98de157c580cf0a6659fac7e6c7e7d17a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb23192e756df4f70165be1de6841a60", "guid": "bfdfe7dc352907fc980b868725387e98f7e84ebf20df39c3845bbc38c37b1618"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804755a030c33356634871d21793d410d", "guid": "bfdfe7dc352907fc980b868725387e98851ff947c7b0b8cfaac67467f8e8747c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ff2fd9a7e6f7ef1bf1e1d5b3585b93c", "guid": "bfdfe7dc352907fc980b868725387e984ccfdb90728dbc4b716c511d5a7eed8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d05069c85dcc0bb5a793602a870e4a2", "guid": "bfdfe7dc352907fc980b868725387e98367352030a10accb148d6d8b29736f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93d33cf6148f402a8a772b6e93c9708", "guid": "bfdfe7dc352907fc980b868725387e981b3a693b4e07d51395a2040bd5d2d6fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751aa3a47285136f559fa82eabaa2c32", "guid": "bfdfe7dc352907fc980b868725387e980d87661b009b62372800b5cb4988df11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989958f5ec7668286293b9e2508c2ee895", "guid": "bfdfe7dc352907fc980b868725387e986316a8bad45230ad012e491d09131397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8033873ba0bdbb7d8c3a236986e7a7e", "guid": "bfdfe7dc352907fc980b868725387e981d4963dacf462621e76d0653d9ca9c57"}], "guid": "bfdfe7dc352907fc980b868725387e9835f4c8f61e73511fc560fa84bf693430", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e988aaf844137aefd0f7dadaa75c6732ac7"}], "guid": "bfdfe7dc352907fc980b868725387e985af968884e986bae48f258bcd28bb9de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980d2794352e93f0efd04427d324195dea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}