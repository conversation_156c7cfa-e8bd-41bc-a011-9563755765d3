{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1028357645975505f24daa7a3b434d3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e752ce11df8ff41bc8b290a146e7815", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989fca8e105dcfa95d434eebdcc899aefb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f27ed5d6ce0fc1cc203bf540c31f4c00", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989fca8e105dcfa95d434eebdcc899aefb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5e3eb5bf4c3530de28f1b12f2ab7628", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f1a552b4f99dc6b0c3c757f3bc87d5a", "guid": "bfdfe7dc352907fc980b868725387e988b2fe748a8f48fd2145d676c3790ee24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9a74579701dc23ac624ac290707bcff", "guid": "bfdfe7dc352907fc980b868725387e9890a02cbede06d76f304d59fad5e57965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98448d01214fbbb9c8c363358883a44d40", "guid": "bfdfe7dc352907fc980b868725387e983859baa2f511c23beafce3ae77f22f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987467a820cc1ba1bc631daa5c9f06c875", "guid": "bfdfe7dc352907fc980b868725387e983691a7d7e45026122f128a3e1995b8e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcdbe428df09b2abb459a819a2de3e2c", "guid": "bfdfe7dc352907fc980b868725387e981253187fb7645fcab288dc86dc973ef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c335f93d2dd3c0e4e5436738fc63289f", "guid": "bfdfe7dc352907fc980b868725387e9891909409b46184d7f45c4d0f852793fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0254ce5a3769791a73d0d3af0e88f7", "guid": "bfdfe7dc352907fc980b868725387e989afe71d2c5667313723cd84387017b72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892225d9a39b96a303a8f883eeba0f366", "guid": "bfdfe7dc352907fc980b868725387e98fe030458be9950d552972832c8424e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c096595b989b1bf3145566d9d5da476b", "guid": "bfdfe7dc352907fc980b868725387e987b245554c14b77800de1924f076e0983"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0cbed3a0aff857a9758620a8e935b0", "guid": "bfdfe7dc352907fc980b868725387e98e1589d842c919e126eec17b60e9228ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a003a39cf03458f888928f80a12e1d", "guid": "bfdfe7dc352907fc980b868725387e9800d61e58b2be0a7616859b20da235a4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b5e19056a542fd331a7ad49f78bd90", "guid": "bfdfe7dc352907fc980b868725387e98488dd4698e3c2299219863a0956f47d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985786346955ca7569edafe5c3b3d41289", "guid": "bfdfe7dc352907fc980b868725387e980b1723d456e0e3d2f1b6992625576b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415e08ea96104d54ad2e8423ff17df44", "guid": "bfdfe7dc352907fc980b868725387e9871eee9e634e57ba4c731f0cb4dc74080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125ea0bb1ea184469b535820c9ee7650", "guid": "bfdfe7dc352907fc980b868725387e98d3e37c02d217c2365ffb9648ccf579bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113bddb4122f497a4dcad09ce64c0b32", "guid": "bfdfe7dc352907fc980b868725387e98f5d70ff1791aaf7ef9a3ceb3e90e312b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cada8f34e8427d14deec92022158a788", "guid": "bfdfe7dc352907fc980b868725387e985bda23ac5185ee1da480022aa4685b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807fbcaba1203683da966047284368719", "guid": "bfdfe7dc352907fc980b868725387e98b9f57310f25d964449b702afa346b5f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90c583edd0ece77c759f4f3d481ea90", "guid": "bfdfe7dc352907fc980b868725387e98df0f8182421ddcf410107db7d9966f04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98240150ba050e3e18dd1c1d40a4ce3154", "guid": "bfdfe7dc352907fc980b868725387e98185b9ade1df017c8b3771501e05c4464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fb2dca249032817c7d57195265ac4bc", "guid": "bfdfe7dc352907fc980b868725387e98413a689dc93e3aca5b7c890936c3b3b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7573fd3a35a908e0170e590a2d7a653", "guid": "bfdfe7dc352907fc980b868725387e982f458cbb77a4138a3421e4c7122715c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8da5785dde36029f7c44a86a4e3f82c", "guid": "bfdfe7dc352907fc980b868725387e982cd489229842fd098a1d65de87beed7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c558269a4cf3944a1af0d215181077", "guid": "bfdfe7dc352907fc980b868725387e98cce66b36ff242fda5cdb723aea834219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8c23fba870b82965ee36fee4ce4845", "guid": "bfdfe7dc352907fc980b868725387e98fc01820027535e5fa8e6144ecf12ac45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881fa2d425a2ba754585e1beaaedaec48", "guid": "bfdfe7dc352907fc980b868725387e98c25ef467d086ffb8a09010aa9f47b045"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e7f5f040dabac6fce9c9b4e9a377b6", "guid": "bfdfe7dc352907fc980b868725387e987605f5958c28dc62ed9ab5a282961916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626013154e57d6133a7b492b4e578d47", "guid": "bfdfe7dc352907fc980b868725387e9861cf58d1e4fdfd13627e8a143ac3eb9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708f41ed5fdb314ab8c1051cbefcec6c", "guid": "bfdfe7dc352907fc980b868725387e9835711d58b9b8a88fcb71dc3b72298829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352880f32316de970b074ae42bc7afa5", "guid": "bfdfe7dc352907fc980b868725387e981d99acf651bec8117a736cb055aa1e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501ca94651d1b202d73c29906afbbaa2", "guid": "bfdfe7dc352907fc980b868725387e987fe5a9d375b53dd346c357b48e9c43fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570136467a7e2bf9b7a2b7addf1f4363", "guid": "bfdfe7dc352907fc980b868725387e98b29e4856c976ad1370c86c4ed575a77f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98896360952e9ae845c5aedebc24f12e75", "guid": "bfdfe7dc352907fc980b868725387e98d873357c09a267a55042efaba9854485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7c0212b9f293bf43ac5a72f09af540", "guid": "bfdfe7dc352907fc980b868725387e98f02370b68ddc56d56548c9f166093203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2daaa31ad9e2540bc7fbef144718cf", "guid": "bfdfe7dc352907fc980b868725387e983209052589f0a1ae2a4db04fe398ccae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1d96bd8f0ff981d83e7dfc0acce555", "guid": "bfdfe7dc352907fc980b868725387e98580baa7e27c94da32f1cf10456b151bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08f3f7a75edd5836d7a7837ace5b9d3", "guid": "bfdfe7dc352907fc980b868725387e9812ebe6d4352c31af3a3b77a7671d2e83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846042887f134a2ced2f499e15299454c", "guid": "bfdfe7dc352907fc980b868725387e98c542fbe9e2a9c3d72f37874c59c858e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982750599de9066bcd6800860ec122b5f4", "guid": "bfdfe7dc352907fc980b868725387e988f0b45af03716dda223aadb332e8fa36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef9cef997908a4569673c6b2e15d73d2", "guid": "bfdfe7dc352907fc980b868725387e9870be5a42b7f8e4417ee3e952b8ee758b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd7f70d7ec41dd93d1688dde67ef2d0b", "guid": "bfdfe7dc352907fc980b868725387e989a26cca64fbc6b7de899a9f9773e1f67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f80be589bc2352fe0a4a540cfe4ddb", "guid": "bfdfe7dc352907fc980b868725387e989d281df252fce61f0ede328cc3a12fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb20f94a8ce58bfdb3682e02b9c2c3d", "guid": "bfdfe7dc352907fc980b868725387e98c6255a700f799cc49d5152393dc54df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98076dc306d84950fad0022b01a5621bb5", "guid": "bfdfe7dc352907fc980b868725387e98550659eb6134f212c55062a346aced8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d1b05615c99e127208f5fb1b1d2d7d", "guid": "bfdfe7dc352907fc980b868725387e9853f9e2dd6d75574e6a546431b0acacdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982235a9ace7e6e17c9234c064f063adc1", "guid": "bfdfe7dc352907fc980b868725387e987e7d321e3c3efb4dab69ab6386f4c68d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a920a7bfde283f1e67c39b0ce784284", "guid": "bfdfe7dc352907fc980b868725387e982dbcc49fcb8dc56c8df33e4712f32619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980514422e6711825012e342a3bb1bd909", "guid": "bfdfe7dc352907fc980b868725387e98671c7714f1a7ba096e7f1d54459772f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b311d1a2589ce35b765d6bc686d12e", "guid": "bfdfe7dc352907fc980b868725387e9814b6dc52c4904f72ab0a8cc65cc0ce11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858cc781d129ab116acadcd5571169f00", "guid": "bfdfe7dc352907fc980b868725387e9867d8ae21f91a64843f704b0ab1e5620d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d01f0a39b7d5c5c6118f3b88b8486c", "guid": "bfdfe7dc352907fc980b868725387e98266a9c30fa31c13be4bd0fb6e190542f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6746da9effee55431ad6f57f17c52f", "guid": "bfdfe7dc352907fc980b868725387e98a35e9611aeea31757759fd13359608d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984196174bfae8c3c8c5bbcf8702119d8d", "guid": "bfdfe7dc352907fc980b868725387e984ca760a7df8acf87c86271a9f7106421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a7c707d68a74689524ed77c5fc6b24", "guid": "bfdfe7dc352907fc980b868725387e98598e66a96e5a876b6088c3f8665eba8f"}], "guid": "bfdfe7dc352907fc980b868725387e98157d9f7aaa9aff7d89981576cff7fb16", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d720f2fd2538fd14a0eadd4dc3ac86e3", "guid": "bfdfe7dc352907fc980b868725387e98bb53cc0f01a4b781f1387774f0cb1202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837323e5ced6e8d35bb4b9a730470cd4d", "guid": "bfdfe7dc352907fc980b868725387e983432d4097f0bbd5aea4f7a4c01c36167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea667fb22d0a7a6509d16b76fb39b7bf", "guid": "bfdfe7dc352907fc980b868725387e98ef84d941acd22af3dda507bda5d2517f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809cf254f87a644bc1fb4f6a6b54ffeaf", "guid": "bfdfe7dc352907fc980b868725387e98b65d91288cea2d908de8121fce47d2d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc49bae0e0c94edd983199264e148fa5", "guid": "bfdfe7dc352907fc980b868725387e98b936d36e783e7a29eda7a6d6658c05e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98772f0da16e89ecfc35702ee795d396a0", "guid": "bfdfe7dc352907fc980b868725387e98da9f2fc16627252c2cd790d9d8827e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800311ce6ef1e594a38d04b362c7f9aec", "guid": "bfdfe7dc352907fc980b868725387e9820f713c35e7f2daa19e6e74552039ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ffebd5dbed4fb93b518343af07cc4e1", "guid": "bfdfe7dc352907fc980b868725387e9829a1825993491ed977110120ad21a51b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebd4824d35f87c863babe3b8160fdb97", "guid": "bfdfe7dc352907fc980b868725387e98ca40066c4447995f9ce8b88006ad9c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fee282330b59d3efca29de99d42ad98", "guid": "bfdfe7dc352907fc980b868725387e9884e61c43cd401724c79e29ff4c6208ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98223b1f1f7d3e0eea52953eafe019c143", "guid": "bfdfe7dc352907fc980b868725387e9814297d6d5450265d30fcfed6224473ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcaed3ea7cd4369b9377e88f80e3580", "guid": "bfdfe7dc352907fc980b868725387e98199baed925e853b71a2cd947cfb7f50a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03e9fc4a76854df68018f097346cec8", "guid": "bfdfe7dc352907fc980b868725387e989178b2c18fdea6f2f4da2d1779c0a8f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc28eb97d0b59b8e575143a33682d222", "guid": "bfdfe7dc352907fc980b868725387e98a4b2b2fc9cbf01ff0a55c04e381e92ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acf396bea2f06bdeba49723f89ada51c", "guid": "bfdfe7dc352907fc980b868725387e988b511db634f4e587da1a2281872ce678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e2c73aff0db284157c6b8afaf53d313", "guid": "bfdfe7dc352907fc980b868725387e98c6d47ddc136fd1411441aee2a3525c40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051964b1d88c7b6529c317b9316248a0", "guid": "bfdfe7dc352907fc980b868725387e9870f174556f87f959362a2733ff4b6989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e84f604d34f27e7a6cc9186e25294b8", "guid": "bfdfe7dc352907fc980b868725387e98ecbf868eec294627aa1da2383037a286"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc54b6fe7e77a8fd7880bf7bf8bd39cd", "guid": "bfdfe7dc352907fc980b868725387e98ac39ffc1e2b1feded163a6b49831c361"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d29a7c7ecf6731fd59149edf5a64ea", "guid": "bfdfe7dc352907fc980b868725387e98f76df36b7ecec88b758198f0c2af342b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2bf2fa96eb5dac23534de7102942900", "guid": "bfdfe7dc352907fc980b868725387e98f9e3241b1e43015ac1b62501812acf9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981777b57e311074c6bec89b38a2cf16d9", "guid": "bfdfe7dc352907fc980b868725387e98c387dcd1eab561e5d7596cc84cdf96d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd47da02a09682df907c41bb62bb045", "guid": "bfdfe7dc352907fc980b868725387e98c27e9f02fb58ee6447f7424808c7537d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882cdac0e214eed8bdb00254c590a65a4", "guid": "bfdfe7dc352907fc980b868725387e9837a497c2237949c531ea15955a055db5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d45b5e9af193986a02e2c86355ff19a", "guid": "bfdfe7dc352907fc980b868725387e986422d4e43948ab9dec543fcedb4e01a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805efd55f4bdb09c7add4f506ab8d9924", "guid": "bfdfe7dc352907fc980b868725387e987a1822bd9ecdba94bec03ef02973a795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882b587e155253707625678a9f01e3b9", "guid": "bfdfe7dc352907fc980b868725387e98f6bb6c77fa92cfa08a4367ff980eafc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9eff5e32cf0713255668956d0129d8", "guid": "bfdfe7dc352907fc980b868725387e982492fef67682495c34033c06ad5add8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efff21d7c27e403054b263ac7bc35523", "guid": "bfdfe7dc352907fc980b868725387e98ab67201bbf040b976506904d37038b94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ba1d02542f3c539d6db20eefbe2b95", "guid": "bfdfe7dc352907fc980b868725387e98dd078f04394bc4c656013f4cdfbc8134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cce3f59332e1e81b156cf4a565505da", "guid": "bfdfe7dc352907fc980b868725387e98ff6f3bc319411511a14d979ce7a9cf95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987340931eb3098ce60c4ac18d1f2b613b", "guid": "bfdfe7dc352907fc980b868725387e98bbb7e466d36a50810663f2c0c50c0d16"}], "guid": "bfdfe7dc352907fc980b868725387e984f4e4e10f22040515f5145abedc6d969", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98a286c7eafb6cc6a502eb96b0e139df26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0d1dd6b952d008563ab75cee403874", "guid": "bfdfe7dc352907fc980b868725387e9885375ef6953a86f62120a9e5052fc2d1"}], "guid": "bfdfe7dc352907fc980b868725387e98954ffa6cd85a06b2ff2da805bd63fee4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f1ac18f7ff34793c8ef5742dc30aa176", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98276016467924fdf9bc872fa40ff7b825", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}