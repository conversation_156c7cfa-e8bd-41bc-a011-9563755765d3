{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb26925a63aaa57d5a0d00262b630f07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f6f2385a613cdbf35379b3680874011", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb19e5b6c0006d452416dafdb2d12ee0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b6cb71b8c3fdf213c5bab5f90e9f122", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb19e5b6c0006d452416dafdb2d12ee0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984f93b5e2cdc91d5e37b05c5128b4586b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855ed8c1d1d85fba8438c3d20325a836c", "guid": "bfdfe7dc352907fc980b868725387e982382aecba175ffabccea1d24f5dc4028", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98432e5a18fff8f6c839a825e45fe9a443", "guid": "bfdfe7dc352907fc980b868725387e98e16f3cbb18c86c3ee057bf3c0596f099", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d545bf73e2411a89b02acc46b7b9138b", "guid": "bfdfe7dc352907fc980b868725387e983370ce8dde52704726a6f7dd9997b2a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1af09a47cc696a194351000506b231", "guid": "bfdfe7dc352907fc980b868725387e98d09b491f6e83f580eb2fb7bdd5d36f45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19659dc2c417809c357cec8aab233e6", "guid": "bfdfe7dc352907fc980b868725387e984efa6f13b04043a2d2564d9534b41264", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e48799c56bfde9622a085093eca2a9d", "guid": "bfdfe7dc352907fc980b868725387e9884962af7660699360de445e3b2297f3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820b818df358966bca904932186edf725", "guid": "bfdfe7dc352907fc980b868725387e9829b4247db387c259e5ec5ae3658927d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98687f8e3f2201d649793aa481bb621302", "guid": "bfdfe7dc352907fc980b868725387e980c88dcdfe4841e9f3bf8f9e92b2c8c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd3d0c06134e029c11b0cf649e665077", "guid": "bfdfe7dc352907fc980b868725387e983edb6e978ff3331cfea185a46b377f9c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f4b28df729fb68dea1d79a3d1c48d2f0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a362ad0f8c9a9a7aafe722d1b96a2d66", "guid": "bfdfe7dc352907fc980b868725387e987fb9f69f9a231ebd4b394b70b9b94a02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e868b727f29784a86ed0efc5ffc559", "guid": "bfdfe7dc352907fc980b868725387e981d9e43b3490cce10516a8274d15cf250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858932d23782ed8b80b5cf47fcab5368", "guid": "bfdfe7dc352907fc980b868725387e985f9378be45238ce56d25606485fe9c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c7e01644d3a420c94cb801d12908db", "guid": "bfdfe7dc352907fc980b868725387e98062a3290c55175da40252688dcdc5d49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e94ab4cb22756cc4d46e11c176a7f09", "guid": "bfdfe7dc352907fc980b868725387e98f505e1913efea5befb8aa3ef950b25e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4c13a4800dda4703f2fce02be0954c9", "guid": "bfdfe7dc352907fc980b868725387e988a9fccdb619b24749cefa049e3bc7ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f2b6d27c780e32af33bf6a4dbb70d02", "guid": "bfdfe7dc352907fc980b868725387e984299ed7be7b4a1848ffee02313285553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98808c22bcf3f0123f013da1f4bdb80ff7", "guid": "bfdfe7dc352907fc980b868725387e98bd19d7f80ec5cf62caebf2c888ac6663"}], "guid": "bfdfe7dc352907fc980b868725387e988720774e5ac2d8164287c2dd92fe6877", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9841ca2f0c6bd26f1c30b10a2f9174ca08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e980ed59871ebc14325a9c8f901a02ec2ea"}], "guid": "bfdfe7dc352907fc980b868725387e983ae7641b2d6c99ac3608dd6d7450b5b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cdf75f4bf6acd6552172e34728c2ec66", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98236a1191e05e808428b23d22d47c42d9", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e9852fac71db81025f6750703427a8132ee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}