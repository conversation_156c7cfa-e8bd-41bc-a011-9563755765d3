{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bd695ddf38fb4ee4a177a8a4edc30b1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d33ddad937c24ae88ad965eacb23ce29", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d33ddad937c24ae88ad965eacb23ce29", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98480482f9199f15cf1f0986167e05b848", "guid": "bfdfe7dc352907fc980b868725387e982ead41a766c542bd95c390fd1728f543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efd38ceb102f7f54e368ca0b41d9575b", "guid": "bfdfe7dc352907fc980b868725387e98d0c9f3ad5114351a3b34bebd6f47672c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adbbda2f828e68af31e33d9cf3b76520", "guid": "bfdfe7dc352907fc980b868725387e98e2bf6224acbbf0f0f4dda8340dfd01af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba062b2f60a6760357f0ccd6c6e00678", "guid": "bfdfe7dc352907fc980b868725387e98475007cfa8da3d73fb136a72a36f342d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834936aa7521d766a2b666793e9d8bcd0", "guid": "bfdfe7dc352907fc980b868725387e988201d6f49dcfbbba51056f20bf3cb887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22ebb98a83f6f3ddd8a0a4ea7c853a0", "guid": "bfdfe7dc352907fc980b868725387e9810401dec66bbcb855966d7c359af3e23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245b92b5d4308fa9c19435b5db370a40", "guid": "bfdfe7dc352907fc980b868725387e984fa63564ad748ae44f5c7dda03e4a4cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be21560c45fea85c4113870fae12cb2", "guid": "bfdfe7dc352907fc980b868725387e9800f03bd306513362f287783a309def16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a46acf5d611d2509c10156a8b23848", "guid": "bfdfe7dc352907fc980b868725387e982590cede0f37b3358bec84976f1789f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4a6d320be8798a6fe5f8abf81cf881", "guid": "bfdfe7dc352907fc980b868725387e98d1674d62a9017d1965456b21e18cb2ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6858e4aba1426fda5480246160f1135", "guid": "bfdfe7dc352907fc980b868725387e98943d4b47de0d20e23485ead603a0e392", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be1d68d47af42edafe966ff5a5fc5e27", "guid": "bfdfe7dc352907fc980b868725387e9893d8dd05dfa661b3e832eb629ec28b03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862e756bbb345b2418a5d5c563f2367ad", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c07c6e01ef9585acf034c1d85b87bee", "guid": "bfdfe7dc352907fc980b868725387e98b6610f89354e4218d6329e8a2bd53a02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c47563d2d8eab651258b12847d13e9a0", "guid": "bfdfe7dc352907fc980b868725387e986e958ad960f140bdec3f5bcb1671b404", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc2bed9ea9de709093188f44fa96e87", "guid": "bfdfe7dc352907fc980b868725387e98d389f650e6aaf2405a8d6a6e2b988cf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a55512b88c924dd468ca3caa1e6a6a7", "guid": "bfdfe7dc352907fc980b868725387e98808ee42cf299ece12c71badd18d41836", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f537deaaa2dccc39a8525ce5bc4aa0", "guid": "bfdfe7dc352907fc980b868725387e98d9ac463eec43ff607ba13b9a6b5286f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfa24a29ca570bd5e6a910ac3abd224", "guid": "bfdfe7dc352907fc980b868725387e983c2dedc0f1daf42da7d572171efc9b20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d3310151c5236c03ba6d6f305012b9", "guid": "bfdfe7dc352907fc980b868725387e98a151f5197b4c9ba2887e4780cfe67619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209745a0570ca2344a1f8a41f8ceb506", "guid": "bfdfe7dc352907fc980b868725387e989eba70ed07115ce4285f6263adf09787", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04a3a715fe16157e80db68afc091b35", "guid": "bfdfe7dc352907fc980b868725387e986eb7d20421c0f5dc03345995076091be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c40fa15da50bc9901a9fbaf419a0f1d", "guid": "bfdfe7dc352907fc980b868725387e98f9ddfb3412a2183e2deffb194de5602f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eeeedc4631e6ac315e852273ece9812c", "guid": "bfdfe7dc352907fc980b868725387e98f3b5eddcdba6029da48c44755457c929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717072cc3fa070e82561b4b2af605460", "guid": "bfdfe7dc352907fc980b868725387e9859eaafa16126b83cf5f63afeb72d8851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a71bda938c4895439da215bc0f08b808", "guid": "bfdfe7dc352907fc980b868725387e9871c1d70170407af428b50fbbaf6b9182"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183c61059741df637478b74cfc018b79", "guid": "bfdfe7dc352907fc980b868725387e986df6601c4798b52451dc8f2681d54206"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e8dbbaae33d82f0a58f2c0e30d0814", "guid": "bfdfe7dc352907fc980b868725387e98571caaec6113234b5fcce710e10a6a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1b19489e22726b3ba984ba7273f228", "guid": "bfdfe7dc352907fc980b868725387e98d307a7018e2f98857dbd52fdf0b7b51e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f733b6df9c9340931d937827d2177e2", "guid": "bfdfe7dc352907fc980b868725387e9886f607277feff8203ac4eb8af060b4aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c0c349115d50a9d50052a1d1d72bc8", "guid": "bfdfe7dc352907fc980b868725387e983fa2632f1dd1475b35d470b7c00a8dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744785aa0fa0378dae32311c7758e447", "guid": "bfdfe7dc352907fc980b868725387e98bf4c472cb807678e4a5ff4a8087a1ec0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d450bd20665fb504113b777ec9ee693d", "guid": "bfdfe7dc352907fc980b868725387e98825598897ea9754545b21db89c5ee25a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981432ca7cac3e20eaaa808d83c5388f0b", "guid": "bfdfe7dc352907fc980b868725387e98e530778148045d85e54e4c619bb3d2b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad04da8704a5708f1e84fd72e0c1f593", "guid": "bfdfe7dc352907fc980b868725387e98f488f255e21782508909c453460a3925"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8a4f0d429005237b6112ca518ceb16", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801399d5252c9ef98aec51619cd006344", "guid": "bfdfe7dc352907fc980b868725387e98f42acbe3b51e49c72a21d6eb95580b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336bfc8ecb36c4c60d145396ecd2fdc7", "guid": "bfdfe7dc352907fc980b868725387e9828bc3567942503f361bcfebe04593267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a88305b38418f57536dac3959f7274", "guid": "bfdfe7dc352907fc980b868725387e98dbc5ab769dcdc04cef7aecce02d62f68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d0618fe517c7c23b584e122637084c", "guid": "bfdfe7dc352907fc980b868725387e9802972a934d48b0f32747cc91a27926f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c791cf56cf54be35251c6130a56f4e61", "guid": "bfdfe7dc352907fc980b868725387e98c5ab80a55c0e58d573b4132fb5250540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b879fb59f3ee7cb128c9e6380bd23362", "guid": "bfdfe7dc352907fc980b868725387e98d1483fc7b1024e41b3bc5acd52e546cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843c3bed3ccc6062efcc8db8c27399dc2", "guid": "bfdfe7dc352907fc980b868725387e9815e05a9dd612a0f26ca06e769807c98e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c18f531152540876bef02e7239d8854", "guid": "bfdfe7dc352907fc980b868725387e986dac89dba0ff32f13233f97c2503b938"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}