{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e09c14f5b7c11e3aea4c9a89e74ed5c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c4b3eabb73e88e598aad7c0748af4655", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988e4e9834d98edff8b6f378f4032dce6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b840de266938618f9554d9b3bac06544", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988e4e9834d98edff8b6f378f4032dce6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988fc7ad8df71beb931f07269f5f745e33", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98297bb79839b1df5efd5892ef4e10d86b", "guid": "bfdfe7dc352907fc980b868725387e980e2ad97d96fdfdf5f70c7bcbe8751d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4cf95cbd69f71910de5a485676bd0e7", "guid": "bfdfe7dc352907fc980b868725387e983c05a2e994011cf8d30216e608f29240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e9efdf42f1436fb27f1147765d5de0e", "guid": "bfdfe7dc352907fc980b868725387e98de9d5c92dc8895ec6ef694aba18050b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813db8d175fac968b72805ab1ce7c5a6d", "guid": "bfdfe7dc352907fc980b868725387e98bb5729b4e00aac9c3c74eb4beb1e63af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812443d00d537a6997655df2a5132f89f", "guid": "bfdfe7dc352907fc980b868725387e985d3db2693fed1ae0d55f1c1c2e2e887f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c51fa700f93eaabe72813de74594116a", "guid": "bfdfe7dc352907fc980b868725387e98e34f0711d4f5daeed4b6054c05096efc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987637d6bcc0b3c8c5dad26ab306ac0371", "guid": "bfdfe7dc352907fc980b868725387e98895d6ef8732dfe8a093592d25f14405d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486c646aa6dad7a0e0838e30aa6ef95e", "guid": "bfdfe7dc352907fc980b868725387e9857e57368c6b14871da98005fe3431416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50265feeabd923e79ae751eb7f316da", "guid": "bfdfe7dc352907fc980b868725387e98efb17d15ba98188185c4b6fbe4fb10f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2dfc8674a26e937a1ac3eadecf77187", "guid": "bfdfe7dc352907fc980b868725387e98ff0123e17cfad80813d63e8bc9d9503a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886f9245e3e43615268306a97fe543451", "guid": "bfdfe7dc352907fc980b868725387e98bce3fee215344d1401ae6d886024d332"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984460b5c1ae604d31ed74d153680a4738", "guid": "bfdfe7dc352907fc980b868725387e987322e134f87cf227c8c66175486194cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d792c06ab3d9f71e911dd426a3254e", "guid": "bfdfe7dc352907fc980b868725387e9804a7467dfec05a21b17a0f48388f5067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bcc46a1362c502a6a53082fc7b7d3a", "guid": "bfdfe7dc352907fc980b868725387e98d486c6dfb7cdaf4636fc11e4f78b5862", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b14ec5efdde4b6265b559e08bb058d3", "guid": "bfdfe7dc352907fc980b868725387e988d48fd52d63cfc12ef6477e10d15c3e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878563671fa8cdb67cc6d2fce95713608", "guid": "bfdfe7dc352907fc980b868725387e987f9cf61b7233c08543fd0d8f37f6f435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c3f058eb7609b9ae9e14e68fdcfa0a", "guid": "bfdfe7dc352907fc980b868725387e9864e3f4b73409d3b3fe68648c6003928b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3e9db34b542c8b97fb646e5134cd18", "guid": "bfdfe7dc352907fc980b868725387e98e20aa19b2f7ff00e89d861e231a4ae2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b790a7fa861f407b899143e84d39ec0", "guid": "bfdfe7dc352907fc980b868725387e987ff25cf5a535e597624729b82926d57b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a601715c5295efd60c925d23736018", "guid": "bfdfe7dc352907fc980b868725387e98c3e07bca2bf8b762be866706f87be569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e205c6d59fe90641ca08c73efcf65911", "guid": "bfdfe7dc352907fc980b868725387e988bc12d92050ac89daf40d0ed571f9d97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9750b6e8125267b2f19ded500ef637a", "guid": "bfdfe7dc352907fc980b868725387e9808b16dee72efab2e6b482ec78b4a2e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f5a062b40b3b3a10dafdd2fcce4935a", "guid": "bfdfe7dc352907fc980b868725387e987ab170356c8c28b7b7ebe7c89741cc4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857865e9884191a9ffd72fa639917e9e6", "guid": "bfdfe7dc352907fc980b868725387e98361f164fa61a5d29d26757a1108f3b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1581535bbd65034dc90da367d2b1bb", "guid": "bfdfe7dc352907fc980b868725387e9826d1e125b8d69bc349ea488b4eae91d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836949209d307f3ae411245db65cd9ba0", "guid": "bfdfe7dc352907fc980b868725387e98f405ed58b76abc98fe8fac054b14b160"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57925326e27bc7c9657aae39c866322", "guid": "bfdfe7dc352907fc980b868725387e980002e900a80b524443ded06ffc1c7a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007e9a9c5f92d40792329ee60a4d2194", "guid": "bfdfe7dc352907fc980b868725387e98b113ef869101c2856c57813eeb7d76c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9cc9806630d802f034434b83c627e2f", "guid": "bfdfe7dc352907fc980b868725387e982e08d38df82ed39b55261b346d62d278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a55cb4366e18227850e7d8549d99891", "guid": "bfdfe7dc352907fc980b868725387e9886a86020dc24d1d7f4f0a7537d5f2099"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a17dcba959ab3b7441a343ad0dcfbd8c", "guid": "bfdfe7dc352907fc980b868725387e98e04f34d281e74c470a66ee103724932e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7226b76b9fe8bd38284c4725bda7fa0", "guid": "bfdfe7dc352907fc980b868725387e982b6745b0cc00e3c3b2fdbbdab8be7327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb539f87bfc22093fa710eee9a445c6", "guid": "bfdfe7dc352907fc980b868725387e98e2169902a37c669721c7203e10553188"}], "guid": "bfdfe7dc352907fc980b868725387e98c9e8c2e01816706711ba9d1b34733b7b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98081d0df44194b7fe21d7e325c7eef353", "guid": "bfdfe7dc352907fc980b868725387e98be0612568f80909e3004b772fcbbc7e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abaeaf1c911f8b055f5520cfcd35aa0b", "guid": "bfdfe7dc352907fc980b868725387e98e05842847b78f6be27cd3fa527530263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98144ba62e856a7d092a6d19ae13917835", "guid": "bfdfe7dc352907fc980b868725387e98da7ae273e16d5f86c7a3fe3c1ce86b06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b17ba72100beb2e81d60c8cbc973b8b", "guid": "bfdfe7dc352907fc980b868725387e9853046792ecf035aabd0df9c8f22e20b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823862a455dea4d44559f131e6d1ad12", "guid": "bfdfe7dc352907fc980b868725387e9879ad2604cf5d8e1f5259a7a851ac8951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852322c65e1f74598cb91b32b5c53aebe", "guid": "bfdfe7dc352907fc980b868725387e9818416332284870e237011fb1063f031b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c6efb5f20e23bce54a97011c014c632", "guid": "bfdfe7dc352907fc980b868725387e9852e01b8c18ed77267fad038dcd40118c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b606ab6eb1e1c9ac6c1beb722842e47", "guid": "bfdfe7dc352907fc980b868725387e98b8740497912ad1d88e658ffec61d4610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983986f3a50e8a5b923e7997a932b8cb19", "guid": "bfdfe7dc352907fc980b868725387e986626389e8ebb2b1a5e51513780bbdf8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803ebecb65983a3d25b0451aa2f6d670a", "guid": "bfdfe7dc352907fc980b868725387e9829c4f936d906137f7ecdd61889be6782"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e24ef4a0d062133b16c4523fe58ffd4", "guid": "bfdfe7dc352907fc980b868725387e98f9b6818bd3bbc2535f2e5ade551b2b07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7f29780d300ac8e6715fc1d2c0d787", "guid": "bfdfe7dc352907fc980b868725387e9807768e5a616ff3996f9dbe61dc9ffb75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9202103ed356a83c9ce71dfdb74020", "guid": "bfdfe7dc352907fc980b868725387e9852df52fd74b05635a19474eb53df256b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b0ae423bfbef52c124a3ddc06db6a3", "guid": "bfdfe7dc352907fc980b868725387e9899da39a0a2ba2a0f129e97249ceb74f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06694a4c5d8a46033d234e992914bf8", "guid": "bfdfe7dc352907fc980b868725387e985fec2cbac399d38ce04f573aa8a82525"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e628b98ae64374e72fd1391d8ba9d7", "guid": "bfdfe7dc352907fc980b868725387e98b5c74b8d94b91261e546f5e1165dccb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893f43622a7b52e012cb4135cd565eb16", "guid": "bfdfe7dc352907fc980b868725387e983a6225449325cce817f5c86defc490e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d19104c6ab5aacaf099a12c989a14a", "guid": "bfdfe7dc352907fc980b868725387e98450244169a3bc25d241b9f4d7c533a5a"}], "guid": "bfdfe7dc352907fc980b868725387e98545a25bed0b50342bde4d4e714457dfa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98a2fc11ee9a060ed17580b83b7ceae2e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e98d8dc3da3d5a19daeb84c71a9ebc57e65"}], "guid": "bfdfe7dc352907fc980b868725387e98da68165e96261b5134740fff35bd66c1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bcbc3ed589e3cba8ae2a904b22b4f9c0", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98f8f2a15a5253aea716222934e47a9c13", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}