{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d40fc7f2c395b7be3b801b616840a883", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bea0c46adb4a4b2d9635548dc8b2d95", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bea0c46adb4a4b2d9635548dc8b2d95", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b980ba9979d96af97a18da1efc79050", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813477a813186442305c8ae3e8e88eb15", "guid": "bfdfe7dc352907fc980b868725387e9857b9be4490be6f042feef2177171132f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8539b918ad7c10eef643f1c70eb8b0f", "guid": "bfdfe7dc352907fc980b868725387e98773f9db85c53cf45934a2dec7f4d8305", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e66d3d5fc3c7884a7836b81e1e57e54", "guid": "bfdfe7dc352907fc980b868725387e986d6f837413a5bc6d9ce033aa161d4e1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986248f1af5665bb42406ba660b625f0a8", "guid": "bfdfe7dc352907fc980b868725387e988be93303bc1d73b379e8ac5e527f358f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1fe5378c35ad5797597e6b81aba592b", "guid": "bfdfe7dc352907fc980b868725387e9811e61da5efdc46c49ea9e9347024cdc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f2dfb465a5e29ccf6b9c3ec3c6e6047", "guid": "bfdfe7dc352907fc980b868725387e983b615f05b4d7227cc5ab8bee00617b30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d87dc3402d7120a6c31d13c67997b11", "guid": "bfdfe7dc352907fc980b868725387e98d267c00bdb03910aff17969c8dc9509b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816bb35e5db912f4cc14014b2132adfe7", "guid": "bfdfe7dc352907fc980b868725387e988153fb10e7827d910d4e9d51409bd3f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998bb21ad7ed500ef3eaf3daedd39476", "guid": "bfdfe7dc352907fc980b868725387e986c63d42787468b59d034edb8d8187220", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982521b488415bc2f39a7733b8cf824cda", "guid": "bfdfe7dc352907fc980b868725387e98732a576c62a0702582d548a9c6991ceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e6dea3bd8747da97c27612bb2b254a8", "guid": "bfdfe7dc352907fc980b868725387e986c0667d117d942af0aa4ab9a3ab1b694", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc897931e6977d66e8118eb4cc9ca65f", "guid": "bfdfe7dc352907fc980b868725387e987943766325ce8a5108dcf589e9f18de5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2eb1aafa33606582a833e7959981bcd", "guid": "bfdfe7dc352907fc980b868725387e98a36660d829a9569acb1789e68546f9d5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c5dd98d8249860f3541b045434b43654", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b27d4651f5ed6134a9f682b9675ce9e", "guid": "bfdfe7dc352907fc980b868725387e9880fe23f1ef3f3fbeaf6971ffdca2bb03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be96c81684584cb901bbe0575e68ac2c", "guid": "bfdfe7dc352907fc980b868725387e988f958238505193406a0fbe430b71c4c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85183d5b607e9af16eb7c23067a473c", "guid": "bfdfe7dc352907fc980b868725387e9882c8ba8e51bd7a612bdc5b3bfd3f55bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892beec61e705b6c6d88fcde5f43f4960", "guid": "bfdfe7dc352907fc980b868725387e98ad0d77200164c8cffb3105bab32ab5f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d9b524cd99f34b7dfa628dabaa2a3c", "guid": "bfdfe7dc352907fc980b868725387e985cd904b997f940df0ed18f6f500aa13d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982061833133eaf952a1c62c517c4d0353", "guid": "bfdfe7dc352907fc980b868725387e9845c81c7bb26602fa282cfe822315c4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98612ac6fa04e48bec12555a68c22385da", "guid": "bfdfe7dc352907fc980b868725387e98c512421a9fc09eb498ab1ff56423ae47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98018b6bca73bbbf8a2db238350702c447", "guid": "bfdfe7dc352907fc980b868725387e9852c43a24fdf86126c6e7fc8b1942319b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f63d1305c0842a342661ad23f13893", "guid": "bfdfe7dc352907fc980b868725387e98d261942bd7034f65cf79384bbb8ae11d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e92916cef73e6b09ff8b26a35a65fa6", "guid": "bfdfe7dc352907fc980b868725387e98ce978135d3591807824d42dafb975f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820384fabed2feb49dcf1e1200bff2ba3", "guid": "bfdfe7dc352907fc980b868725387e98310c9524b14bdb4f80b9f8297c6e521f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e5e215e87ab651531b3e3dc2526e50", "guid": "bfdfe7dc352907fc980b868725387e98be9ca2949f5445df03c872d69e5cf67c"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}