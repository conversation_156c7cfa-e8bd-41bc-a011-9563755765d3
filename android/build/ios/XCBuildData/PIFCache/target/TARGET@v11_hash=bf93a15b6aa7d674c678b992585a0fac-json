{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f14fbd1b3b8342beb37f73a09c99c376", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e86f437cf06fba5d62c606ffe87ea28c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820d20eae25a7ad06807b92c9ba4687d6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824a0f8641933c63790c7c44b36e64cf3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820d20eae25a7ad06807b92c9ba4687d6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98be6d25086323fc9cdd3ab830a7f72908", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ae99fca389cff086d00e0ee86a7d12e9", "guid": "bfdfe7dc352907fc980b868725387e988f9be0a24fb2e681475d4a0e1fb8afd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b6eaf6b87847f8e6705d981deaec673", "guid": "bfdfe7dc352907fc980b868725387e98905135e91bf72ed63bcbbb76a1d1b095", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2da80590a491267eed3f0de2325a7bb", "guid": "bfdfe7dc352907fc980b868725387e98ed0735292aff733ed5dd9ad81dd2f4f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988193403beb42add7ad4455d6541fb091", "guid": "bfdfe7dc352907fc980b868725387e98ac2a4342009d672a51b94acf7947a14b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988657a922a61ef304a3c26433dd91048f", "guid": "bfdfe7dc352907fc980b868725387e980d39a7cbdff22c46fa1ffc580ff53476", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac28b20614f427428bdce853ef967b9d", "guid": "bfdfe7dc352907fc980b868725387e98c6edd415bfdb74c1f54e333b33d7ddc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ad50759fd357eaf507f728399ece91", "guid": "bfdfe7dc352907fc980b868725387e989a7f744ec609747b1bd4e7134377bedb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e4d00760aa6555e2339200e6072afa", "guid": "bfdfe7dc352907fc980b868725387e9813aa55708f993458b96c3c77955afd3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ce9b3fa16bb5aea1393fd84105b5b4", "guid": "bfdfe7dc352907fc980b868725387e9888ff58427c2f4c42865648c3601bc8f4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b094aac2d05d66dc601eeb162ddb551d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dcf1b434a688ba824c74244e80eb99a5", "guid": "bfdfe7dc352907fc980b868725387e987f66d5762be1694730b932d630d31d42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be697ee0c99abdbefac3e8d8f078fce", "guid": "bfdfe7dc352907fc980b868725387e9861dff9d76bea833803323e774c1f57d8"}], "guid": "bfdfe7dc352907fc980b868725387e9883c2707afaf104dbec3b3bbb117a22b3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98016b05cc209391d477d642428cea49ed"}], "guid": "bfdfe7dc352907fc980b868725387e98a59f185c2391f801fae06c21e1c267af", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989ea9fda4a7cacbe04281b854c4acb96e", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98ea58fd249da3868201eb3a500cdc21f7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}