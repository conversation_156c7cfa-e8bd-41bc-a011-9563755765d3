{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ca20fe1f0d898315a2d0b597e717945", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98513b478a1351e34abfc56e96f2a87889", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5a247e7c483dab624fe72c1428b8f1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae1f7128a46c0fb6188e49f028fffa2a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5a247e7c483dab624fe72c1428b8f1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9852190d1bd980eecfc098218680da7354", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9806ff861965a0d853f2bb0a36b2b752f1", "guid": "bfdfe7dc352907fc980b868725387e98d6bf559f1e51788ef6d3fc8045c24b7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b8d9d8bf66e5ca30bd2710e1c7b276", "guid": "bfdfe7dc352907fc980b868725387e987d886bec5e6be7065f9be4b6f0a6c687", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c21dd5bb4dccbe56bbe80c02004abf", "guid": "bfdfe7dc352907fc980b868725387e9882ac41a2ff71a56e99daf8eddc3edb82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a96647cc1e4951187c6dc43cf91500e", "guid": "bfdfe7dc352907fc980b868725387e982bf4994c583964b3f1ccc39d98aa5b6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbcffa350845ff1f8baac2ba6a33322c", "guid": "bfdfe7dc352907fc980b868725387e98e64e225df1aa1db953647a9a8816fe14", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9855ae51d3940078f45dc2cef3f4592b74", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9887764dbcf21b46ac862ccb0dc253471e", "guid": "bfdfe7dc352907fc980b868725387e983bee7cc1cbd127908c8bbb9dd0452161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893663a7f67bfcc46ba71bcabc5949824", "guid": "bfdfe7dc352907fc980b868725387e988565c06942b1dffc8704b3c691e5abed"}], "guid": "bfdfe7dc352907fc980b868725387e98f0d5a39e6fcf0499d653658253939a8f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f6b6abfc4a2f9e152e183e9d9b4669c3"}], "guid": "bfdfe7dc352907fc980b868725387e98c3869c95fa925f412abbefacf84d3f3c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f3bab9c0d7de9fc76338749399ba2033", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988c2a56cd963a48a63ab689fe60f47236", "name": "RecaptchaInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}