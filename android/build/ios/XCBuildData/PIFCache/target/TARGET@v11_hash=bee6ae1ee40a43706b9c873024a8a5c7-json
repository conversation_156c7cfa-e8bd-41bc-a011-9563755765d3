{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b71db237e2e8b2467f44d48ba598d470", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6e32ca0bbf16185b2a704cff00612ba", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980bf3da8a736bc8174e283a8aaddc4830", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845be11279e6e7413d47eefd3da982a61", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980bf3da8a736bc8174e283a8aaddc4830", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2d3d4e86126a2aa7216c38d4190e7d4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98befbfa09cb40f8fccdc30f511b1d1645", "guid": "bfdfe7dc352907fc980b868725387e9822c53581cd2f86ce4a5e48944b7b2c8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822551bc9b5b20c96f18eebd49501b368", "guid": "bfdfe7dc352907fc980b868725387e981257e8681ca8fa188d3efb92dfdf4fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4ef37c67930c74a9bb6e85b82a803a", "guid": "bfdfe7dc352907fc980b868725387e98d90ef9cd17c492815ebec0005f58e867", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2e1976c67ec2bd8d6f87e25f3e26a0", "guid": "bfdfe7dc352907fc980b868725387e980d3ba0d83a96b766f275512a78b54165", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dffd3d1b15532c6adfff371c35b21af0", "guid": "bfdfe7dc352907fc980b868725387e9899d0118365139f346598f2a591d543ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb4a5d201c42a94951288341ae20490", "guid": "bfdfe7dc352907fc980b868725387e982d95d430f4af005771c0064fa859e71a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef96c89c6d49e4ae89b1f8e70c769f88", "guid": "bfdfe7dc352907fc980b868725387e98372335732e30bd0f0f102c21c32f2d0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf027c027233e3cf07c4c1fb1b3e3a09", "guid": "bfdfe7dc352907fc980b868725387e989ca331a44450f264de2627ad5fc29d21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131ce14dea4d19352d676f83934b713d", "guid": "bfdfe7dc352907fc980b868725387e98c9d2d6edb07be1b38b03d03644414985", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98711e3fc912b708d207c84527f95091e4", "guid": "bfdfe7dc352907fc980b868725387e98c6a7922df9d53335d6e163bda2e0906b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ce2a106fe4652a85b65006cba8e099", "guid": "bfdfe7dc352907fc980b868725387e983734d440125ab8dae6e1d4253786257f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98151e525fa718bf654f4af28368d5f7e1", "guid": "bfdfe7dc352907fc980b868725387e9896eca3ff026f89c691073b68ebd49383", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fc46900db7d94f3fdb544a52103a8e", "guid": "bfdfe7dc352907fc980b868725387e98c4646e13ab0abcfb1b1cb2e3c5ccf5fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384019ca0a13ede465e17b789ecf64ad", "guid": "bfdfe7dc352907fc980b868725387e98dead08048936fa0f5c318aabc47de566", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839f7e4d61f0cde7601f2d548054e5c85", "guid": "bfdfe7dc352907fc980b868725387e98e7c343dcd6c59ce6cfb2ba51de6aa738", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a0caee426f34e805cd679916825f7e2", "guid": "bfdfe7dc352907fc980b868725387e986204f415ad006026fc12bcc5ff67c4f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fe274a91611c034e38b3267e371c882", "guid": "bfdfe7dc352907fc980b868725387e98ed9b6b4729f9e3184e2c5ff0d4a0bcbd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98edb76bd49dd6b780ca8ba3ce19fb82e4", "guid": "bfdfe7dc352907fc980b868725387e98809709857ab34cf15f6b7a8c9ce080bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88c61b8c0cd3ac91a448097099f7e90", "guid": "bfdfe7dc352907fc980b868725387e98338e706c0533439af5b90f467d70206e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998aad6644203f3189c70f5a271ecddc", "guid": "bfdfe7dc352907fc980b868725387e9863928bbd0a863223d707d638d5aee468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a1bf09f873313b5fb3769a94669bd2", "guid": "bfdfe7dc352907fc980b868725387e98819f627a3b269c8320458fceb0e9234e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac7035b98c237c36a065d355e379da38", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae11ef40ea024ae418eb98a7fe59c5c1", "guid": "bfdfe7dc352907fc980b868725387e98a1666b26c922fbaa4699df2fc40390d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983e691aa48f4bc562b1c0a16533217c", "guid": "bfdfe7dc352907fc980b868725387e98597bb641f77861d4a927e4527ff0cb82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd0c4cf5e45028157ca4ed733b03ff0", "guid": "bfdfe7dc352907fc980b868725387e983d1af881bbbedeb43540ee039ff3dbaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d60c024d80caf2541c91d14205b23562", "guid": "bfdfe7dc352907fc980b868725387e988c05d3b07e4c424c30ca359f970ac24c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82522655072da9234f600992deea6ae", "guid": "bfdfe7dc352907fc980b868725387e98997b40f593ff93ca1340c34fe06f9396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be3fcd3077717934579dc775ea80cfda", "guid": "bfdfe7dc352907fc980b868725387e9827864d9fb24f97c9cfe631c33a29eae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e4a6f47297934e1c8ebf0cad52c6bc", "guid": "bfdfe7dc352907fc980b868725387e986a4d12b2ef0bbd53570b75517c915699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987db3bfd254f6fd326c0fed757ab34e09", "guid": "bfdfe7dc352907fc980b868725387e98839db72b794a3208e9e646aef373a07a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c11c28b7cffffa88c0d82903ae10f23", "guid": "bfdfe7dc352907fc980b868725387e98972d22b9c2e6d2af3a37c6b9d2f92a1b"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}