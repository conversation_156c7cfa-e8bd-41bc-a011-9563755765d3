{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855dbf531e37f33ed137a186e689bfae1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0318c16d32a236eb7d93fb6cf007a52", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b316f65bf9b411e2926fcd630119306", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820077d4e895caf3436a59cb3fd56fc1e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b316f65bf9b411e2926fcd630119306", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987981c95b7133e0dfef56d3f8098bf3d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e4230447edeaaa2afa8ca335198b26b", "guid": "bfdfe7dc352907fc980b868725387e98cab3f5cf5f2d9dcc84124cba2ffbae66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840485315d344883ad71dccb5e8aa6cb7", "guid": "bfdfe7dc352907fc980b868725387e987dddb2c317acfd424694fc38905eeac5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983417cf7fd73b8e0771f14fe016888aac", "guid": "bfdfe7dc352907fc980b868725387e981e9a520e9a7ad082a988fbb54df53513", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8ef3ff7bcc3853a6b66ebabec8a011c", "guid": "bfdfe7dc352907fc980b868725387e985a7219e36cc5c899f4a338a13e64c9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982638c3c1a458f9cc5cd224757301b7ca", "guid": "bfdfe7dc352907fc980b868725387e9865c2231203d7101afb4c323f6aab124c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899135c7f4731e4253827dbb3e8c21cd6", "guid": "bfdfe7dc352907fc980b868725387e9852ee594d2b7e4a46b7b9ff41759df914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c8c0a20c36552883519335ca985528", "guid": "bfdfe7dc352907fc980b868725387e986d360e8674b318a37ebfb998ac4c43c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984451d946284c075cfcfe01c0ad08bc89", "guid": "bfdfe7dc352907fc980b868725387e98a8076c84c01f2bc5c8a0929cbd613e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50350e8162d6dd01e72c4bb86751754", "guid": "bfdfe7dc352907fc980b868725387e98d33fdd1e808bcc3797422d162a9f549c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989b53ff48e4935e92b94575fb4db0caf9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad2f6f6f4a188f1a78136b1c9bd023d0", "guid": "bfdfe7dc352907fc980b868725387e98564d8172a7b4886b325c40990ca7e647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bedfa7d2d4be9d83d3df26b4a8a5f18", "guid": "bfdfe7dc352907fc980b868725387e9807b861daef48c47f159592c6c9f5b97d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b0720f2ebe39055873ad8fd9f5e406", "guid": "bfdfe7dc352907fc980b868725387e98f5b1c5e8a3f37ec3f2d4ce23067477e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729a415b63a1088a6f818fa0aa611ff3", "guid": "bfdfe7dc352907fc980b868725387e98b2b3d952af522f8b743cb5cdef6b5f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98389f8bcdb29ed5ffedbba46c7581fc6d", "guid": "bfdfe7dc352907fc980b868725387e984f1fbfff00377728f029b03fd2fdaddc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ff926288ba6012f841330c3c0952d7", "guid": "bfdfe7dc352907fc980b868725387e98667293eecd08995fcc01513a8f366092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98999c4a1310c6f2e131d7073b4fbca204", "guid": "bfdfe7dc352907fc980b868725387e98b7c9e442af9339783bcdb4df2d888458"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2e26ec49dd869041d65c45123ee6b9b", "guid": "bfdfe7dc352907fc980b868725387e98147447f309ba2394e866d58f012fd600"}], "guid": "bfdfe7dc352907fc980b868725387e98d8ecd8a7cd05e1bd81d3f2e38cfa1f29", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98ebe8e86edfa3513f5e0fac22b9af76b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98a30f5b2759ab504f157e4ff86362d82b"}], "guid": "bfdfe7dc352907fc980b868725387e9823eee7e0de5c35c7571081e6eb179b43", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983631f3715c28d8200dfb132ed95c3880", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e982286cd5b2a5a6a283b6054704bd9757a", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98f528aa5c22a98ccef2c2d7eff5da9abe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}