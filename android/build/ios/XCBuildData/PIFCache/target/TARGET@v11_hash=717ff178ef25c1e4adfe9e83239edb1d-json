{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860e26594bc2144cc36e6ac10bbfcfb38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981ec4233b1675201404b5c7ea8fa4f364", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981ec4233b1675201404b5c7ea8fa4f364", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e77e58dfcd3f1eae768908651a5386f4", "guid": "bfdfe7dc352907fc980b868725387e986c642d959ab0b3a74cf2dc9af7ca6575", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815221d96820ecba7179dd54f75fe0db3", "guid": "bfdfe7dc352907fc980b868725387e98589145a49a93692586adf7e483809213", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803fa8ae2974442e15f788bc632eadc33", "guid": "bfdfe7dc352907fc980b868725387e98a3ab02bc76ad21854a394106652b714b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98468cd3014ca4eb6df46c350e5c832b13", "guid": "bfdfe7dc352907fc980b868725387e98aac7aae5133e3cb198d493e2da7edb95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dabbd0ac6f85271a7cc48b5967c1edd3", "guid": "bfdfe7dc352907fc980b868725387e9833f2875a39b769b84ae89ca0c330fa89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a835d73186ecb171019169e5713265", "guid": "bfdfe7dc352907fc980b868725387e98095355b5d46bdead464a8ab3405237eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981696cb1e038d2df9ae7320126fc050f4", "guid": "bfdfe7dc352907fc980b868725387e9895642136ab6b437c709e2eec70dfc1bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ccb9e6fe40e910aefecc8d0a0b215a9", "guid": "bfdfe7dc352907fc980b868725387e98bb70de040d11f52e08c72407a0b7f2ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f4febab3a2a4b05f00ce1e194b8570", "guid": "bfdfe7dc352907fc980b868725387e98ac2b3ccfaec700b91880d00d1aeef55e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870bc86a7f74b917fdbd0b75d7f6eb63c", "guid": "bfdfe7dc352907fc980b868725387e98d3467234214a5fb1717e5658ac600e74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800543cea5db166fbc54c7ee682d46700", "guid": "bfdfe7dc352907fc980b868725387e98dedffa98a09362a0bedd451061872db5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857003ca440bd118d0549de51eb5699e5", "guid": "bfdfe7dc352907fc980b868725387e9849ab0df8cb3c38b56b0161041e5306d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98403a62d28e4b2babb76869121de45f67", "guid": "bfdfe7dc352907fc980b868725387e983a81ca54b456f8a909088d5b9f6a5cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0dd5ecaa17b990a8b2ec6898f49cf2", "guid": "bfdfe7dc352907fc980b868725387e985d891ce763262ae8564bf80524257b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93c5e5e73ecf5148e5ad14072e6ae6f", "guid": "bfdfe7dc352907fc980b868725387e98cbccbcf2a5cb8519cb01836a36580c4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827ff48cc46de55cf5f25e148613d55ee", "guid": "bfdfe7dc352907fc980b868725387e985ef2dd14f9336c47ee0574cf2ceb0545", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51a516b752f437ba66b7385a5a6cdb7", "guid": "bfdfe7dc352907fc980b868725387e982fbf638d2a2d7134f5810cfbc8f87426", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e2556e8e34e4038b5a50a88d92b13cd", "guid": "bfdfe7dc352907fc980b868725387e98047d9c17e150a48a150ff6780ab58cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db99f81f94934f6cafdc14cf12d0cf6", "guid": "bfdfe7dc352907fc980b868725387e9871f01842cd1e75aeaa24ca58c60abb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e05bd3fea1036ec46b4eee9a8d5d4e06", "guid": "bfdfe7dc352907fc980b868725387e981f7455bdb1fbc4da3a82d543726f3d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830ca0292da937ed4316ba2e835a5f601", "guid": "bfdfe7dc352907fc980b868725387e9804fe3192a5f30d2e79b6aedb3464d216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb7c7decdea811179320ed6ca3de36b7", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6e9da34800a04738100c36e2590e2b", "guid": "bfdfe7dc352907fc980b868725387e984028cc6d365683630f975e4546c33dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497bb1d6457c774dff6c324df2d3c960", "guid": "bfdfe7dc352907fc980b868725387e981975ca6e74bac51e5bc3ea7aafa790b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a00724120c6812591068199c2120ee", "guid": "bfdfe7dc352907fc980b868725387e981c62e3d0329d35ceb953521f987e0874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929852c78a59b167360de31ad7fb354d", "guid": "bfdfe7dc352907fc980b868725387e98cce31e680bb7749b81cdf0cf8fd13cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823b4bbf48b70ab547fd07d65b29af57", "guid": "bfdfe7dc352907fc980b868725387e986b03888db3bf0f6e1886d46387481e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc52f4fee52d79c1b291217d913e828", "guid": "bfdfe7dc352907fc980b868725387e98fd23e7a517c3bad0fe25bd525f9f575f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b128722c3b2911e2e40c47d93e3d8c", "guid": "bfdfe7dc352907fc980b868725387e980463d32e76f26f0fea0b1e1523e26914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98082e7248cc3f50c30bfcac458b5c6de9", "guid": "bfdfe7dc352907fc980b868725387e989871f188dd1e4bee67360343550cc279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f56684deb3f4f387e5471196b629cb", "guid": "bfdfe7dc352907fc980b868725387e98f61dc4e84c1074c6c51b018070465690"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}