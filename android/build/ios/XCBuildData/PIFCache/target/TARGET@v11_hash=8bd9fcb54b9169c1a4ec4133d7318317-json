{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a7c43962a132e0251ef9aa963ebf31b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850651dc3f53a2f5eda1ff556ff5a3d95", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f75ec35ec7792f062781ae6d2cbd470", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b814888028160a740e09e334f1224ba", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f75ec35ec7792f062781ae6d2cbd470", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1c771ae1bee59f551d137d122c40284", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98637bc8c31b787b2369a9b5f16393e71a", "guid": "bfdfe7dc352907fc980b868725387e9880a85f7cf5f207b6926c75a9e1c3f04f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f516e9aeca4b1da636907817e2cc62cc", "guid": "bfdfe7dc352907fc980b868725387e981a973db894d1766aab0eef1908ccc47f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a536ea44dc067c4db01cba0081bc50a6", "guid": "bfdfe7dc352907fc980b868725387e981ac963cbe3fa7c05ba7907e986c03781", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ccb3d0fe3d28aefbbb9387f961d466", "guid": "bfdfe7dc352907fc980b868725387e98915195aa45a4c6f1e624e706ca98c045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb9a6d136aaf9050287aea0fb8be62a", "guid": "bfdfe7dc352907fc980b868725387e98e41f860a215d18319a1d21408c9fdc5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806b86e4ccc098ba384d69e49aec83ea1", "guid": "bfdfe7dc352907fc980b868725387e98b54bf55593d65be7922a1921e0d051f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f4156dd77cc601fca2fc87586f7435", "guid": "bfdfe7dc352907fc980b868725387e98ce170b1dcee52864abb2896e70e802c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9b82d8c471d6b311e6fb91666cf2bb", "guid": "bfdfe7dc352907fc980b868725387e98b4fbebbe349cf6899634b2bbb10a6923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c3cce0d2ec5eb7555d5bb463aef06b", "guid": "bfdfe7dc352907fc980b868725387e98e30b530a5acf9e21a6af1daaacb3a803", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ecb56de30ea6f54f66b93dda2e9946", "guid": "bfdfe7dc352907fc980b868725387e98e2267fcec40ca2a01a8f052a90ccc17d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c490313926d24b12e2c7313d6b9bae", "guid": "bfdfe7dc352907fc980b868725387e98c3a7ba088805416f9a158ea61757df6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5427848d572b9b95b032830f38367ca", "guid": "bfdfe7dc352907fc980b868725387e9875f9d5579d1680e7bd260ec265fecda8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea881ed2b535c05e6f70090c5eea800", "guid": "bfdfe7dc352907fc980b868725387e980880a14339ed109e9f8ebaa28942642b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302173bea1d75cefed50ddea9b35136b", "guid": "bfdfe7dc352907fc980b868725387e98202ed4c2855b55d539956df803392ff5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf466c70cf99aea23798cbb9ac772189", "guid": "bfdfe7dc352907fc980b868725387e98d3ff7eaa289c485074f1f6ac856e617b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb7d1005125e7b94cbae95de617d34f", "guid": "bfdfe7dc352907fc980b868725387e98084976180713c5e46cc709d7b1aeeb2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac780d7be420469e164ada1c011f4750", "guid": "bfdfe7dc352907fc980b868725387e982ef0e1ebda24d55ed043b99eed09e73e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981508b4547067f354bc6094efbbefad0b", "guid": "bfdfe7dc352907fc980b868725387e989b972471d72481c21af946b914e024d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2d97f860dbcb37ae004506da6f506c", "guid": "bfdfe7dc352907fc980b868725387e98d7f18ee877521a3038aeefb4c2e4eb9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc47784f96bf3f7acd7bd9adb91790f7", "guid": "bfdfe7dc352907fc980b868725387e9822a8a0f9742783fb4da392d82bfe404a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b900f64aa469b5c0286bf137006c26fa", "guid": "bfdfe7dc352907fc980b868725387e9864a7ede477819072705d4310aa4b59b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb268f92a9cd808d248854ddc686bb12", "guid": "bfdfe7dc352907fc980b868725387e98029e40d222be733bf66ce055dcefcca6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9868e4d66946cbee1a2804c09d3683fc2f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850b25eab51ed3ccfc420d551b0e96b64", "guid": "bfdfe7dc352907fc980b868725387e9862029cc007f2150b334a3b712e9ad89c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef84e005a1da4ae1700e9a7e1a75211", "guid": "bfdfe7dc352907fc980b868725387e980e461e7c6032e85b91b0c7091cec108b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a78c971407d24243e82a3c58eea4344", "guid": "bfdfe7dc352907fc980b868725387e98f4f2627c1ebc3e57c4da9fed43d5ab1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46781a8e01fc706b626f6a051d3975b", "guid": "bfdfe7dc352907fc980b868725387e98667018856ac44463d9b6c40c4315552c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862966cc3dfe76a902ffcbd254703768a", "guid": "bfdfe7dc352907fc980b868725387e9823f3f43b6e1889a66456db8f1751b33e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64d327698f0c28c3654203a05446335", "guid": "bfdfe7dc352907fc980b868725387e9831ba06bcf7a96db854e4a34bcf57357c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818edec3f91047dcd04243f22130b64ea", "guid": "bfdfe7dc352907fc980b868725387e9842a67163580db945fea64c90f5b9b6c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a9755c27d9b48bf9ddcaa90eda9361", "guid": "bfdfe7dc352907fc980b868725387e98d903240fb6c5f4b1b08af8ef8b5781f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816afe72f7fa64976ef187296c5a0e210", "guid": "bfdfe7dc352907fc980b868725387e9823fc8ccdca0f84f4bde834dc0a8c54fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987531075dd37a7d35a3fe1872209c74e3", "guid": "bfdfe7dc352907fc980b868725387e985aab05e8277e1d9e6bb55ab642466384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8b214748c0505d702f07d1d43ab8ab", "guid": "bfdfe7dc352907fc980b868725387e98755531d1f5d41a0f18298b3feaa78daf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d05b6f5006b30f00b3393838a58809", "guid": "bfdfe7dc352907fc980b868725387e98c549f1058253b2457b7288eea1e50e2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a505ae8bf7863a5fd6c45fc956eddf7", "guid": "bfdfe7dc352907fc980b868725387e984cd71003a2d25cbd323ac79ae1172c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b78f5c821ebef804f5f6058116c515", "guid": "bfdfe7dc352907fc980b868725387e98fed931dd8596a144001e78c62db0bc1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379f99cbbc71c294565546dc13469bda", "guid": "bfdfe7dc352907fc980b868725387e98abd5a330290206265e825477a26715cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875eee71a6749eddf9e17fb90adddc963", "guid": "bfdfe7dc352907fc980b868725387e98daba90f054b5299b6ac2d74fcf1a6406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98019ca1a0b2dc206183e9dc758a161ceb", "guid": "bfdfe7dc352907fc980b868725387e98a6431ff4fd40d4fdb331acda334bae91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea2817df21ba7bfd8b083a1493119a1", "guid": "bfdfe7dc352907fc980b868725387e98aaee3f9ba5b4ccd573737615470bd4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983480cbf4eb1ea31c230ec55b52c21131", "guid": "bfdfe7dc352907fc980b868725387e98e44465b546bbdc0fc6fb4dae5ea38027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45455caa98c7509a6417e2e356f5e7d", "guid": "bfdfe7dc352907fc980b868725387e980ef171701ae21f29865b1becbece2ee7"}], "guid": "bfdfe7dc352907fc980b868725387e9850a5fd8215df6d046585ed3496baec0b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e985cf8f37599728b3e226d7b95802016a2"}], "guid": "bfdfe7dc352907fc980b868725387e98b7934015281bfe85d11be5c68b4fe6fa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f616ebbc9ca06dbf0abc00fcec712470", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e988b001230cc98301f23dbd3e7284e514f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}