{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4f2ec7f7d3c067c7013130975e459d4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e4d2263ba24f164bad5feaf7c74fed21", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98487e055d2ddecc5e68c237447045af76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823c410be56b727c19aee440698e0b1ed", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98487e055d2ddecc5e68c237447045af76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b8bcaf465bef7a3fe78d23bdd7d98079", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98db6f582c38377f120093c94017c1e83e", "guid": "bfdfe7dc352907fc980b868725387e9864a9c5e2e31ff8aae93a507780d74e2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854ee14390dcfb6afc2fb94832178ec6a", "guid": "bfdfe7dc352907fc980b868725387e98bad76ae1ff38aa815da7ed44c7faf00e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c39eaa082715a82180777eccaaa10a28", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f67e5c48a49b1d1e30794e602a7cbf3", "guid": "bfdfe7dc352907fc980b868725387e9850e57c0cdac3559806e9f5292f8ac44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7541e0255010a6a6641c31b4737ae53", "guid": "bfdfe7dc352907fc980b868725387e98cc5e2cb576db5e2ac1c9eaa1ffd5c3c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7a9150a1d154caaf7655264228d2dd", "guid": "bfdfe7dc352907fc980b868725387e981821fffc04449b63ede222e6704e5dd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e75bdbbe3d5d6e6ee9522534e24efc2e", "guid": "bfdfe7dc352907fc980b868725387e981caaeb316f14da2ac5c962bf0b0cab8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a809990d3645881f98e46f7b5fb22706", "guid": "bfdfe7dc352907fc980b868725387e9827d9961f3eaaafd64a661933db1d4471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be7c953c0a7d9532c2d29d103bcf4f1", "guid": "bfdfe7dc352907fc980b868725387e982568b0c559d5af2461c16ade41bdafc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f3e59b703db3bd89ad4902d0011af1", "guid": "bfdfe7dc352907fc980b868725387e98ce5c6834b74299496c02c059506c5223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982574eeee3954fe045b58ff46f715fc", "guid": "bfdfe7dc352907fc980b868725387e98347514eb5e186b8359696a4eb52778e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98548e4325bedf76842449fb5a3a914a7e", "guid": "bfdfe7dc352907fc980b868725387e9846ac694054577333f13bf4484c5f9950"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b68d5f555d343b75c1b0257cb97ba86", "guid": "bfdfe7dc352907fc980b868725387e98be05067dcaa50ff3d9d4aacc305680ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6ea9ade92d60157fb32a4370813c730", "guid": "bfdfe7dc352907fc980b868725387e983940c7928fad3304767b4d79dec3f12d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f05f9234f3d0546fcc7da6ebd04e1f3", "guid": "bfdfe7dc352907fc980b868725387e989479ed410b51b8afceb81c5863aee477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a14795d71e0b5a62d21b545ddd91d08", "guid": "bfdfe7dc352907fc980b868725387e989dc3d6ebd9b67b032e8d4cfd987b1a1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488d933f8e73dfb2f56359c90a9d7188", "guid": "bfdfe7dc352907fc980b868725387e98dc34bdc13e3c46b4070d1a46140cdbdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a8f365a60d3325b63b469c0e4e08f83", "guid": "bfdfe7dc352907fc980b868725387e98b0b41aed6f0aca5d1a09cf9a75b219ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a764f67958bacd0f352c41f14e1e39c", "guid": "bfdfe7dc352907fc980b868725387e98d1d9400c1f47312ac4cfb59e60a58479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f6195d48497429aaedaefcf9e2e3b8", "guid": "bfdfe7dc352907fc980b868725387e980a947166cdccecdcf1f041254c638729"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865a828312af6c4a92f6eb79a9c4e573", "guid": "bfdfe7dc352907fc980b868725387e9889da45363967cf470b56c9fd1126011f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2bedcad84055d42f180f8c27152a48e", "guid": "bfdfe7dc352907fc980b868725387e980beed71a5708c9036a264c451e37c84f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c8a241c7465df5bb58b54941aff451", "guid": "bfdfe7dc352907fc980b868725387e989a6896df279a8b42477977f3803b5cc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98080064bea613fc1cd24c077d12b4e618", "guid": "bfdfe7dc352907fc980b868725387e98e1e928a216b8dad4875f9672ef59f48e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981379fd2ba9e6abb3f0ec2fb92397c486", "guid": "bfdfe7dc352907fc980b868725387e9804789ff64ae4de7f28ebdc48a88b5337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c72ca14ebf688121703c6c71e10ce1d", "guid": "bfdfe7dc352907fc980b868725387e983245465df4d586784d053cea807488e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3733a4cc1032fd598cc2946d824b786", "guid": "bfdfe7dc352907fc980b868725387e987944cb68b6812955a01a6cb6283b16d9"}], "guid": "bfdfe7dc352907fc980b868725387e9812b5618a6ac0b755a53bdc572227caaf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98b93862c690bea7ef7e60c1683f09798d"}], "guid": "bfdfe7dc352907fc980b868725387e98e37b3f67c6da59af14acaae64004e0d1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9833b2cfe315aeef979335bb03485e0dd3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}