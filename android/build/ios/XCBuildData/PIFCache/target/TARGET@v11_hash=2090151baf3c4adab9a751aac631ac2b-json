{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870f0a542362759ae6dacc0912e864903", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f84fc04c318456ffbbf4d9f56962b494", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f883b231c0ead9089005cc48befd36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9876b76e7f2403d1795e64220e7999894b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f883b231c0ead9089005cc48befd36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9804bd78c712b0e8a20c3b6ce9faed7a96", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893fa3e281ecb4116938a4cb53c2b145f", "guid": "bfdfe7dc352907fc980b868725387e984e145e0375281f6a0cbc582be8618412", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805e1c47e7cb8ed264fb37a34cd58ae2e", "guid": "bfdfe7dc352907fc980b868725387e98330dc2ffba43aed1c2c454b1b34025f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2687dd4f5f78111bc493ac7b22a2e2", "guid": "bfdfe7dc352907fc980b868725387e9827de1157ab1e4809ce5e588ee9bc473e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d6d179def54c522177f826e550896a0", "guid": "bfdfe7dc352907fc980b868725387e98e52815f92d5fde46d9c4bb9813ded62c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98793328f4d6fc41ee56de8b6bf51f4957", "guid": "bfdfe7dc352907fc980b868725387e983a3ba160dbb9eae418422764b65e06b6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986c15cebe60e0e9a8d3f9247c985a7d29", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989eea46f81e9bf7d371e17c20e16e0643", "guid": "bfdfe7dc352907fc980b868725387e9847690611d290369f6db5f1aab8f40b11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eb23f04cae4e3a4f1b9adb5a0f561d9", "guid": "bfdfe7dc352907fc980b868725387e98a841370a156060d2c7bc32dc6d6fbb8f"}], "guid": "bfdfe7dc352907fc980b868725387e984a6164df855e54e9aed3bae2f23cad84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e983f7b7c8351c9cdfd24d80665557a0dba"}], "guid": "bfdfe7dc352907fc980b868725387e98e936cef1ab0e366370410a0b2ec64e5d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98653ab29741790cfb0029a431515b2aa7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988c2a56cd963a48a63ab689fe60f47236", "name": "RecaptchaInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}