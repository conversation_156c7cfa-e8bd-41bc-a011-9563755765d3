{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f73c15e069a2f21d47b4ac4d6cf5abc7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98926f810d85ff21eaa7e9e1cc96d49279", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987026e7c1693c9763dd91c59678dcb308", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9829a97d13a777c896f1631cda25b04784", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987026e7c1693c9763dd91c59678dcb308", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5adf0eb6c4c0549df0a40de4539f218", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985528199fbe1b5b0786f7af3d67ad38b4", "guid": "bfdfe7dc352907fc980b868725387e980deb7b410bcb30b6c8c153fa2e754f86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ff1c7abe1874c28cef4b7089c2f62d", "guid": "bfdfe7dc352907fc980b868725387e98046411fdf79e0d5823b22900509fb512", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106b63a74b06c724f76c803d474d12df", "guid": "bfdfe7dc352907fc980b868725387e98b69cb1b4e30723deee17085506327e42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d093a9fa1cc1dbb00f7cace087d3811", "guid": "bfdfe7dc352907fc980b868725387e988bc17f7c45453fd4d986e7f4c655195d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845722ffd2e2f54bfb0280c280883e36c", "guid": "bfdfe7dc352907fc980b868725387e98f2a78a9e962f4d72f8299b46eccafda6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f32447b33cd14cca28cffc138ab1a9e", "guid": "bfdfe7dc352907fc980b868725387e985241f4c02bf04c4e4443895e19f8e6d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d5425116cd1945ecf1040b8b0d260f", "guid": "bfdfe7dc352907fc980b868725387e985d50fc82f886190ca165cb8135e9b504", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cbd37de550d84b675f9a2013da2886", "guid": "bfdfe7dc352907fc980b868725387e98b29106005b920de83acdc9d94ddab60f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988830be388a52514c72f09bef02121d2e", "guid": "bfdfe7dc352907fc980b868725387e983ee34b6da5be9248173a77915faae8a1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98002238efa1959e43dbcbeb7d719a6b52", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f619eff56541828cb0628f11a99723eb", "guid": "bfdfe7dc352907fc980b868725387e9891ab8da6d9b9df173c25d9ef4312a63d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e32cdcdebf2dd3794a61b23a84b325", "guid": "bfdfe7dc352907fc980b868725387e987518ab3dfa8470120e34f0864efd08a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989459bf7337cabe463f363f033be266c4", "guid": "bfdfe7dc352907fc980b868725387e98b3344db76b5c10c963f0d3793ff72a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3df6639f9ebc4430eb804ddea26ad2a", "guid": "bfdfe7dc352907fc980b868725387e9871adbed871392907e1a9d06bfed6ea35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364f249b0840c5ca00b96def57600085", "guid": "bfdfe7dc352907fc980b868725387e986152527fee65bd445c12862be816c245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70f325bdddaf606618b686925c23acc", "guid": "bfdfe7dc352907fc980b868725387e98b3c1aaf5694abf6b681b1799623874f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982903554429cdbb23f8d1642dd09e6a4d", "guid": "bfdfe7dc352907fc980b868725387e98749b982902d3d395a755af0a12b76e08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40b7e6e64fe5a4fa04bd4e3ee98a53f", "guid": "bfdfe7dc352907fc980b868725387e98a3c50381c0598b2f5eac226e96f6dbe8"}], "guid": "bfdfe7dc352907fc980b868725387e9884120bea363cf7e01c4eedb340bdd432", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9879e26dd2f17f8eb3c3e5ee3b3be0fd23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e9851e5803852d4d94969e03312ad1da582"}], "guid": "bfdfe7dc352907fc980b868725387e982fdb02267f242db665a789fd14ed6524", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981e5e245606a53c36458ae8d75a7b53a5", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98477a4b0f3901ff80676842a1b15a21ed", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98207abf14877358127af4657781bf5555", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}