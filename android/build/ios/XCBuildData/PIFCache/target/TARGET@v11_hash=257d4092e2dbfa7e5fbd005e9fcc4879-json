{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98870973c453d2092c715119f00b7f981b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cbe9c2b27d5360ba9a0cb1cc9d2fb130", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b569e6270624280cf4e18b2a801ff816", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f9d4fc4b06528bf8b6f70fb689633e8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b569e6270624280cf4e18b2a801ff816", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc96e15ca48666a08f375a0f184de4cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98830aca19f08d8ff9ac5a43e23110263e", "guid": "bfdfe7dc352907fc980b868725387e98ba52ebff4d2c96d31810b1e3d3e844ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2b6455beaacb4ff901abdbb60cee06", "guid": "bfdfe7dc352907fc980b868725387e9894f52225a785bc88276c8740fdb0da8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd3d38a2159c64d06847c116f8d09cfd", "guid": "bfdfe7dc352907fc980b868725387e984508a5365ba181bf64045df6bd88e806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eba874a171f434515c48624cce459bb", "guid": "bfdfe7dc352907fc980b868725387e98174e16339d88de171e4bbbf21d3a2e4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800225c9c589c119af7597e1b37552c9f", "guid": "bfdfe7dc352907fc980b868725387e981437f83aa070a152914446f33397e461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdbf67188310599191d7ab5f792d9ac", "guid": "bfdfe7dc352907fc980b868725387e98b8dcd61507ad5026fcd6f8ef8b66b0cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2109ac51326fcc88efdd587122acad", "guid": "bfdfe7dc352907fc980b868725387e98dbb7026972deabe8747c311b5c8d8ada", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f628a09b8a49739cb7fdafe7919eee", "guid": "bfdfe7dc352907fc980b868725387e98098cc30ab985dbdbd3d2b11957bd0364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba813d2110b38ee1a9daf343d3e278f3", "guid": "bfdfe7dc352907fc980b868725387e98c32ff8e04e846b04a46ed7a88eaec1c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f2cdfd998c6fb733251a6d57ca43e2", "guid": "bfdfe7dc352907fc980b868725387e98000862aa1456e524c2a62652400d86f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e1a904a3a377ff331147709681ab73", "guid": "bfdfe7dc352907fc980b868725387e9837428d5c05c997f7868a1d607cfedb56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de84bd808c7cd0a1fea19ad238f9a59", "guid": "bfdfe7dc352907fc980b868725387e981ba71e08bc762bb6d1fa7cade1f548b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca4265f5bfa99571a874b0abb8df200", "guid": "bfdfe7dc352907fc980b868725387e98d33611e0db059a5b257df42155c3618c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988376c58993434d8d7139f688428045aa", "guid": "bfdfe7dc352907fc980b868725387e98ab51f68c9ff81f1df52e72eff93e3d11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a0b72d4d6cb901c31795167550d4a2", "guid": "bfdfe7dc352907fc980b868725387e98f9d01477a8c8161692b9a1a4ce42ef82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1c03cde7c43be1f0b4cc8a62711a67", "guid": "bfdfe7dc352907fc980b868725387e985e9307d31c43b5f0b6ed5b724cac8426", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c803ec43af839ec4611bc36e3a917502", "guid": "bfdfe7dc352907fc980b868725387e9856defcfef0e99b1e500c56b60e2d13a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b2703ab0903fa30aac23bfdeeb01ed", "guid": "bfdfe7dc352907fc980b868725387e98bf353cff98d1509e15a17f68e37ea208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3406402e3681a003b987428629ab8e3", "guid": "bfdfe7dc352907fc980b868725387e98049b88869eea89aa2acdac9e93aa7746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50f813a9a3ccd6ee338939b44f82219", "guid": "bfdfe7dc352907fc980b868725387e98de0b12a4ef51789752e5da3ba6e2f112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bc89cdceaa8c9617d45600e8d54d61", "guid": "bfdfe7dc352907fc980b868725387e98f836af6c3d411492cb4ecd467e9902bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe0badefd813df1d43db8ecf9f48143", "guid": "bfdfe7dc352907fc980b868725387e98464aad2ecb8bd6beed2ffa5fc158a3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986634d3b7bf0bd56e1185bc139399b444", "guid": "bfdfe7dc352907fc980b868725387e988254ff2e72350a82bb3ce178cf099f3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec7692d4470ea2f89b0c77d42056612", "guid": "bfdfe7dc352907fc980b868725387e98aa7f1bace1677dde8f27db0fca6c18d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b87f3fb40a2c775f89c2cb4233b79f", "guid": "bfdfe7dc352907fc980b868725387e98d0896144beaa2402b39805630c3c8d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8d6b3ae61a736f0bb3cae880545cf76", "guid": "bfdfe7dc352907fc980b868725387e9898bec1db3c704d0c2011e302a9c4e3de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a77dab3ae85ff234b911cc034cc0c7", "guid": "bfdfe7dc352907fc980b868725387e989df501311a8c03424392fdaebaff639c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4e4e2f10573a0358047f09b8185b79", "guid": "bfdfe7dc352907fc980b868725387e9846940380aa60f9127df855ab093bcccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830582034aed2eb684854aed28b813d57", "guid": "bfdfe7dc352907fc980b868725387e986a03b8c2f07a6032f7e0f52f5ff7b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a3f0badd2deb3aabf163cc11e20c5a", "guid": "bfdfe7dc352907fc980b868725387e98265ea0fe2d4bb356c07c5c64a4b1af21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e25598acb4377cc90533b9d7d2a87c97", "guid": "bfdfe7dc352907fc980b868725387e98060382c808943c9934a1e320c02b42fe"}], "guid": "bfdfe7dc352907fc980b868725387e98f83ed34a5f053e693862c676ac64b208", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee0fa730b6530993a726a60380e2cc09", "guid": "bfdfe7dc352907fc980b868725387e987cabd289ecda702d5ef9880874c7d5b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ea0c9a7ed813746253ed85a45882de", "guid": "bfdfe7dc352907fc980b868725387e98509698d7dc205f2df006dbf055425757"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808ff0175278beb350af0bb2f9ab047a5", "guid": "bfdfe7dc352907fc980b868725387e98a7781b02c5cec6c5fcae0aa3d97b498f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881574430493a247e9c23582808bb4fb3", "guid": "bfdfe7dc352907fc980b868725387e98ffd17f85b676c68a62f8debca4414ac2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987449767c7b8aa2188fe39949b933c63e", "guid": "bfdfe7dc352907fc980b868725387e9893c897193117823827fec5399dcfd237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cdb5eff3c40f1e986bcd551c10edf22", "guid": "bfdfe7dc352907fc980b868725387e981a5144617d7448d65439f12f3359a779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689597427ef5f90865300217fea0509e", "guid": "bfdfe7dc352907fc980b868725387e9807140819c6858ba77de79bbffc904111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebe01360173b85164434fa11d79d64d", "guid": "bfdfe7dc352907fc980b868725387e98a1a454ed243b6e689119b12edaf01c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f126020b8a232ea6c6ea470f9bd00df0", "guid": "bfdfe7dc352907fc980b868725387e98bde9beebd3739294776feee2a927eea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8a12b79d08c2b7178dae7c76ffea28", "guid": "bfdfe7dc352907fc980b868725387e9848c9dfea0be73f3a230955c35057a3a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c504cd582afa7f7268d245a7150957", "guid": "bfdfe7dc352907fc980b868725387e98caa76c2274a9b7af123b0f68b5fe8598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f7d9fb5a6da4ff225b88027f9a2245", "guid": "bfdfe7dc352907fc980b868725387e98a0bcbc424ba3f0dc66484eb13a49bc34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3d660b45fbc20ed251b7e2e0a1960f", "guid": "bfdfe7dc352907fc980b868725387e9864c46b5a2e8199e0ef2e234df5d8890e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5e0414a0afcc5ceb16511cc9c201dea", "guid": "bfdfe7dc352907fc980b868725387e9867b82cd5ced7091d451aaa0a2e570262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831265d1ca651fbb8fe78a889cb7eb5ef", "guid": "bfdfe7dc352907fc980b868725387e98f0d79e0e0382edbe315832f1790c3ccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91234bff92e8a4180a395ea0cdfa84f", "guid": "bfdfe7dc352907fc980b868725387e985758ff4b780406d8442a2a423f399998"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c971e690461d78156f74c01da5c666f4", "guid": "bfdfe7dc352907fc980b868725387e981a0eb61a11a49c1e87f0ceabf549c18f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a5f064e72303a2c1c3757e5ba5abb1", "guid": "bfdfe7dc352907fc980b868725387e989919c042a7891239f12c6e654c470ed6"}], "guid": "bfdfe7dc352907fc980b868725387e98a40418fd4feef0453011792bf8b67ed1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98ba62f51ca854c9093aeefad7e9ed3e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e9848771433f4fba88bfea57b2f3ce177de"}], "guid": "bfdfe7dc352907fc980b868725387e980b1a7e95ffaff336977f30aa90b4e8df", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985d9e336a61948c08e8b43ca0534b100f", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e986e32b5a913c8a8d0b141d352a06f45c0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}