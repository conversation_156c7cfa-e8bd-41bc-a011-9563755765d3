{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98213d219c0f37257242ceac97757a8bd0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e33a531d2359c86abf67ed23aab93a0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98274cbe58dea2e515450c0f3d829969fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98825f2886f1911231947bf68727e9105d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98274cbe58dea2e515450c0f3d829969fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98207edd27aa6fab4b535086d7815e12fa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc6c7c3da7844055987127a1029520c4", "guid": "bfdfe7dc352907fc980b868725387e98542e977db57066192c5215c875ff96bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873bf207514031db9087ae524a8bf5d9", "guid": "bfdfe7dc352907fc980b868725387e9873ced95797351b9b11398a3cb577d207", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9820c249dc461983a36f71d5fb8fae5949", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98063cb79cd312745756053061d5653696", "guid": "bfdfe7dc352907fc980b868725387e982417de707725d4193c2a5425373ffc11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98827d237942ee7c7699226456e1a6ad28", "guid": "bfdfe7dc352907fc980b868725387e98584eb2f2f06419ca273ea59c3f92a4ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7c773ad8d464b3a39f584f1e31d1b63", "guid": "bfdfe7dc352907fc980b868725387e989ab06fe6c2c092e83467f0f53578f2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c805c8d4ddab83beff939c138d287162", "guid": "bfdfe7dc352907fc980b868725387e9832347f9e69eaa53432a26ddfdab92935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09b3ac778c49a44c419ed57fefde25f", "guid": "bfdfe7dc352907fc980b868725387e98b81923c7ec2f2d0cff07628509dcfa83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983749900c48e148f57e52d16bf876f64d", "guid": "bfdfe7dc352907fc980b868725387e98929ffefc27afa64b2b948bb661a66b50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e783665fbcf6ec08472549c0d995be2", "guid": "bfdfe7dc352907fc980b868725387e98592198fcc2ab9c807a18a15e36febaa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a645bb7e95e615b679a9a3abe5a70774", "guid": "bfdfe7dc352907fc980b868725387e983e51d781489c8689b3fcf75394aec7d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c887a29d3c9e2af2cd4fc18fe9600c9d", "guid": "bfdfe7dc352907fc980b868725387e98bbdf84613b02e2c4ee746b43172136e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4e662f3f3055bc0e984562e6b6fa1b", "guid": "bfdfe7dc352907fc980b868725387e983966e89798460ff03463d02b6c36f38f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809704716393dbe973341657e9476fc76", "guid": "bfdfe7dc352907fc980b868725387e988b38924def19aed121d2b472303473ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873a9aea08e0d6e511e129d626e9800d4", "guid": "bfdfe7dc352907fc980b868725387e98719b18125ae11513f9fc4dc5ac2bea71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eccf4787b07a742170b87103f43dcb4", "guid": "bfdfe7dc352907fc980b868725387e980e9c11fad3b10fbf15dfefc9c2c663f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f35d21048f9ef0ed3aefde5c1a75a085", "guid": "bfdfe7dc352907fc980b868725387e98de224de5f4f05bb08683765b430cb83e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd562566569ce3b230bc0d00fa307e8b", "guid": "bfdfe7dc352907fc980b868725387e989368641c44882e0545a297b48c0f826d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfb5a4e06392f76c3030104d4ffbdba1", "guid": "bfdfe7dc352907fc980b868725387e985dcdfa34eaea4a0657f19731a19c0a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebeda015f4cd867cd2a44f53ef464c1c", "guid": "bfdfe7dc352907fc980b868725387e9888ce3b3ee0dd3a95b2faf86f6b307a8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a934c5ce0c94af2c081fad81039e01", "guid": "bfdfe7dc352907fc980b868725387e98526016e46d3d412c7592e7af081bdc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afe7a760d587e1fcdf5d4dfcbc70b3d7", "guid": "bfdfe7dc352907fc980b868725387e98299b017764f3f0933097ce9d2f8776c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dec753d79fcfc06741497e58b8eb4d3", "guid": "bfdfe7dc352907fc980b868725387e984db0374695a79d7e85ebaeb2a1d8c71b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f4697574c39c20b142584ca2ef044d", "guid": "bfdfe7dc352907fc980b868725387e9831baedf3245de732394a875b570f90a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdcae29f1e0efd599ac4a58e78e1c55e", "guid": "bfdfe7dc352907fc980b868725387e983a3239324d3328d242496f05550598fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98482a762c9e9e287f77e5642298f27467", "guid": "bfdfe7dc352907fc980b868725387e981e005ec6d098c718a7e81ff2c81b8547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0f0de5f30b6611f4d6cf36f908b2e9", "guid": "bfdfe7dc352907fc980b868725387e9820cf63bb01b9a78e7f90ce333383016b"}], "guid": "bfdfe7dc352907fc980b868725387e9886ba676db31a0a1a8ba313dcbe707a88", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98c48bd5c7f39d5c670da720aed4023a5e"}], "guid": "bfdfe7dc352907fc980b868725387e98e031ae2b2559cc7d2eb6980a2651f0b7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985cac152fd823df6df3ee0b810b4bb6b0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}