{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9880901eabc4acae8ff4f634e27bec2d44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b3b2da9f99051c47cc112b89eadcf012", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bbbe60f36cd2076b6aa59cafc248398", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9801035a9fe121476972bd319870c680e6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bbbe60f36cd2076b6aa59cafc248398", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981199b1832461060326f6451bd5328402", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829b344d2f08f51a8a15fdbf86d28a4b7", "guid": "bfdfe7dc352907fc980b868725387e98ba0850e4d0e274cacd1e42aaf411e42a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc4490cc591a2e39c61166570ef0004", "guid": "bfdfe7dc352907fc980b868725387e9844fea8019a98be94789709bd99d8cf85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd94ddc460b63e78888abb9a15c53acd", "guid": "bfdfe7dc352907fc980b868725387e98ab8cc9e036f05ddccfb920c502ae994a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987199030e4477da5bada012a326bc81e4", "guid": "bfdfe7dc352907fc980b868725387e98639bd3357d62ea1ea81362fe3aec29dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694e59498ef830346106df7a8a7cb384", "guid": "bfdfe7dc352907fc980b868725387e98467ce236c2910e856d750976356a72f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bfd709a1c93ef25202126ab0b4ce19c", "guid": "bfdfe7dc352907fc980b868725387e9850c6df6d83980a5cd555a1e5f719a995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908969c5c40baa6256935a612b4f95d1", "guid": "bfdfe7dc352907fc980b868725387e988cdc222400189ecf770fd7fd573a986e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d5c1ac92a6941c6c3be4511ecec9015", "guid": "bfdfe7dc352907fc980b868725387e98d08c06a821db4fc3f629763ff2c1ce0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5c9d9a6b6afbaf7e6f9d64d6f2fede", "guid": "bfdfe7dc352907fc980b868725387e9822ea49380ee90e20c3855a7e2e622e37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98303de8ade80028238781e63c114eb4f7", "guid": "bfdfe7dc352907fc980b868725387e9833ed850274442d19908492d8ee9e8ef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11ecdc3baa30d2272228e46ee070a9b", "guid": "bfdfe7dc352907fc980b868725387e98aa328746aa55342307046800b39550e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6853cbf527b0cb3220c90a60b0191f2", "guid": "bfdfe7dc352907fc980b868725387e9871ccfcd01c2bb16778d60543559a41e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7d3e818d039e808242a9d5686b4aa8", "guid": "bfdfe7dc352907fc980b868725387e98dcadde814ea5c07df84ab22ba08caadc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7a37e48573d2ba02a34b209d94f4f92", "guid": "bfdfe7dc352907fc980b868725387e9810b2f33e1f71d040d1c22463ebc84665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d244ceee5f384bfc4367057c8c3d3e", "guid": "bfdfe7dc352907fc980b868725387e98731efccf0d68dead1963aac5dbf83855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea0926ee533590df477e96cdab54590", "guid": "bfdfe7dc352907fc980b868725387e989ffb4f676e53b92bdb70ea0236967381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0a2747a9f8a0404e0998bcc62f6cf9", "guid": "bfdfe7dc352907fc980b868725387e98ef54cda0f6128cbd2dacb017a7278e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ede5b9c104cc710203e944fcb87415e", "guid": "bfdfe7dc352907fc980b868725387e98fc4439ea17ac071c8a293411cdfbafd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec00584c0db5c9665bf69f3562ab5d46", "guid": "bfdfe7dc352907fc980b868725387e9813020a22e05f281a6521f46b3173be80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ec495ff201bc99ec1768267f22b1d43", "guid": "bfdfe7dc352907fc980b868725387e9862e6138cf3a42a552e28117529bde631"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d61540297f6464b7c4484b609b5c9c", "guid": "bfdfe7dc352907fc980b868725387e9893518d6bf1d2d84cf09c11daf4a40cce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a31884bcaaeb83aeee9897c389dddb", "guid": "bfdfe7dc352907fc980b868725387e9876fb36e27bd040014e64fd335d75b882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989698c05e5e37236b2aac7892305c70d9", "guid": "bfdfe7dc352907fc980b868725387e98ca4d3cc0a04950bbb44ece4977f6de4b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984ab266476d0f56d12dd5ad325fb0cb55", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867fec75d5fdd0ef86203bf8c44f2192f", "guid": "bfdfe7dc352907fc980b868725387e9871900ca56ec2c91ad0665f26e866ff9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fea79b44ef19464c33868ac0bcdae5d", "guid": "bfdfe7dc352907fc980b868725387e981f9bc88139fab0b77dbecbf776bbe356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985baf68127db188ce49663329e8ac154f", "guid": "bfdfe7dc352907fc980b868725387e98ae36d032c5dcec40f0ee1b9044635e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f52d288517a00135a14b97156252d3", "guid": "bfdfe7dc352907fc980b868725387e98a1ed16fa417578f89fc6dbbd18a0a779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985598e219e8a7c488a08f772e15966309", "guid": "bfdfe7dc352907fc980b868725387e98ef43ced79ee0d7c7b4eaf727000fbfb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ead76e3925c659a7115421152bad52", "guid": "bfdfe7dc352907fc980b868725387e98bc48a71fe7ba7d0a6ea3b5dd20f3fe0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d2bdab4db047920c497f40966265e9", "guid": "bfdfe7dc352907fc980b868725387e98e12c07cc27bdd32e57462b31b2565eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981729730768f8f1bca2277e3f5aa27b03", "guid": "bfdfe7dc352907fc980b868725387e9847fa12f14105e8401cfd51e57875da04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a23f11cfd433f20539a1388bd16bdc1", "guid": "bfdfe7dc352907fc980b868725387e986103adf74f1f34896cd52894d3659bae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98655b146386c5041dbc3571a524e4407e", "guid": "bfdfe7dc352907fc980b868725387e985191fa37c7ecc7b08095c440d0f3a23c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bbad12799ceaf97bf15cfd1ef654bf8", "guid": "bfdfe7dc352907fc980b868725387e98adfc5bad069f5453bb932541683bbff5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a65e3140beff6b95498333b7509c406a", "guid": "bfdfe7dc352907fc980b868725387e98b6085b38db8a76a18e3fa0f9204aaa29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989239d58aa1eaee9dc0f5bda870c09bdd", "guid": "bfdfe7dc352907fc980b868725387e981a523a7fd8c6f3b71bd276d030492573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980787fcea81e132e7f51ede8aaed1b5b3", "guid": "bfdfe7dc352907fc980b868725387e9809326ba455ee934915423403a687cd3e"}], "guid": "bfdfe7dc352907fc980b868725387e988d81a2e2a93c82ef419a191d0066929d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98000d793878a757f6fe2284fb65dc3c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e9830fe52d098aee46ded6aecea6d0e57f5"}], "guid": "bfdfe7dc352907fc980b868725387e9863a4e59612c52b658552718fca3c918a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c0707b3dd1628428e153da3b9ce4d1fb", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e981cea6a9f8f47043cbec72f72cd6a4871", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}