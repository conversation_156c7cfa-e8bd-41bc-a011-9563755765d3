{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845e1441a5f6fc183904c2f8bed6e9cee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a9186989285bd998fb972ce277f4838", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a23f5e2b58c1a7a730f30bedb8493a3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37a1f6412452e7499975f0eaf21275e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a23f5e2b58c1a7a730f30bedb8493a3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac2174ea8e92e04cafb435524caf8e4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b411c683546f94e6cc078e0e5ae8eef", "guid": "bfdfe7dc352907fc980b868725387e98408e096957263a5dd2d57544ac81f90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f139ad4c3b3c0dbd1bc8741b2d6f21c4", "guid": "bfdfe7dc352907fc980b868725387e98689e8f5d9d65ad21e5b61ec30f1f39dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800d10cd940df091e255eadf87f3d94eb", "guid": "bfdfe7dc352907fc980b868725387e98e496c19c18612182233ceac967d55125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df08d85a78454c53679db3e9be4e9bf3", "guid": "bfdfe7dc352907fc980b868725387e98e88385533b65402ab909295f8d8bf483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555eb4810aadf54e314ff1bad6f580b8", "guid": "bfdfe7dc352907fc980b868725387e9804394cb5e1d579817d12669031158fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7b6c500e0d73ea4aa0bed00d3f3496", "guid": "bfdfe7dc352907fc980b868725387e98534ce7ae30363a6aeb511bc43f68b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98898e8ebfb2abdc38fc036a8b86fc1e77", "guid": "bfdfe7dc352907fc980b868725387e98e193f510114a0ed25adbdb086812d328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2312674da34a72565d290626e7fa12e", "guid": "bfdfe7dc352907fc980b868725387e98806918b911a08c1e453bb6f8ec6b8e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc82b08e9901b8a619b98e40e56575c", "guid": "bfdfe7dc352907fc980b868725387e984a99f8973bf06fd7ecd48f47ea4d7b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0e8bea47c60f0a9d5895e19b990222", "guid": "bfdfe7dc352907fc980b868725387e9865ab1f93c3e5dd8b94e8bedd8d50d57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53cbba74c5e1e484f7687c04550bd69", "guid": "bfdfe7dc352907fc980b868725387e988b2ae869c841d48f7621a75452f333a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2e7f477dd9e14c35a663957777e2ea", "guid": "bfdfe7dc352907fc980b868725387e988f34148a1805194b0e49f7beb7274ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b88e925e12bfb28441e6a73b91b2722", "guid": "bfdfe7dc352907fc980b868725387e98ce08dc527a765043bcb0e25778b28a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d1771f098617ede86c5728ca4e3604", "guid": "bfdfe7dc352907fc980b868725387e98a842eade1e03a1eacccbb6112e601f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d117db22b5bd6acaebaa2b2302440ae", "guid": "bfdfe7dc352907fc980b868725387e98ec400b99e867ead36fb85a9ddc0dea3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10d4fe42a07058ab3d7d72e237659a8", "guid": "bfdfe7dc352907fc980b868725387e98670eb1e671f51e4c05821918eb010481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824540e897d7f1c236b54f6d47d1ed00a", "guid": "bfdfe7dc352907fc980b868725387e98381389c6c65e012bc3f84136e47d6551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901aa2529fe2488457987cfd9be11283", "guid": "bfdfe7dc352907fc980b868725387e98890af04b86b386ff1c6ed75d7a84b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b402bfbd314a9f234d142c6a985f09", "guid": "bfdfe7dc352907fc980b868725387e98eafe250e158a9866c042425cfe724ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60165d0cb2ed053018fde776beaec92", "guid": "bfdfe7dc352907fc980b868725387e98461b232e8ab9cb8320153ac40cf18b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d93bc005a7d0630f095186addb420f", "guid": "bfdfe7dc352907fc980b868725387e9898ff37f713170e9552f08fa30531b46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ae534f51c5dcf4a082b9594cf0c30a", "guid": "bfdfe7dc352907fc980b868725387e986835b8796a4c055252a4fffbf1d9c89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c755bc70fec967ae9d1efc6a11b6b689", "guid": "bfdfe7dc352907fc980b868725387e988cfb0d6febc566948737750f5dd9d5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83e22a937d9791dc895bf0f098e70fb", "guid": "bfdfe7dc352907fc980b868725387e98d0ed8d0f8eb8a337aaf65c9f90d461a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882070a38eacd0cd6c3037029765770ed", "guid": "bfdfe7dc352907fc980b868725387e98138d4f541ae5519074e993023016073f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9cb214bb8c60b729e170b3adaaf79a", "guid": "bfdfe7dc352907fc980b868725387e98776136c9ef87dff6a0c1e949f8c38ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852215a121b16cd05ebfcba2da708c449", "guid": "bfdfe7dc352907fc980b868725387e98f8eb522b67fbb3d7943718519bbd4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01a50378939836501245533ec18503f", "guid": "bfdfe7dc352907fc980b868725387e98d13bb27cd7ed169779d20b8c5f0b74bc"}], "guid": "bfdfe7dc352907fc980b868725387e98808e660503e89e55ed3cf0726d416c64", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e64644d466b2bc6b118a17184946b8e6", "guid": "bfdfe7dc352907fc980b868725387e9813289d7416f058b8bc83a87b0c859a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd0d055db2a37deefe51f0a926eaa20", "guid": "bfdfe7dc352907fc980b868725387e98a9ad811653e6da9c8d58d9707852e492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d8d0d531674cd78ac6331c6a460f061", "guid": "bfdfe7dc352907fc980b868725387e98b9d8f09917866faa3437e3f849511ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933f7f04aad2afdaac4b268e017e7917", "guid": "bfdfe7dc352907fc980b868725387e988a4806d5c4d243010b2a2ca456edc3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cbd9e569899a2a77d67888740527c24", "guid": "bfdfe7dc352907fc980b868725387e9875c77e2f0223b6a6cb60562c80152dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8399460d30fe2cda13e07118deeec4", "guid": "bfdfe7dc352907fc980b868725387e98f41dec7891271341fc51ca9cd9b3de7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d1bb2a918cc154cff2669991ff145a", "guid": "bfdfe7dc352907fc980b868725387e981158f7a402e3cf0e924b5ad2ccf21cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea378a8c2154ab29fd1a5dc35cb90d48", "guid": "bfdfe7dc352907fc980b868725387e988b25285695893c413044e3634fd52702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ff7c8bb3f60dbe214cc1546b28f6e2", "guid": "bfdfe7dc352907fc980b868725387e98d4824ec9d4112954acb01a6b3f2608f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bfc4ad0798c9db2ac98994e0981b3c5", "guid": "bfdfe7dc352907fc980b868725387e98eeb59f21666a8287935a608091e475ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a6be939dcbb3c5251d51dbaa8a5007", "guid": "bfdfe7dc352907fc980b868725387e98dd7346778ce40a9d9eff6785a27f08c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b1232cc292e0f974af130c0db1ed9e", "guid": "bfdfe7dc352907fc980b868725387e980de7382e22de2367c8a8ff9283195972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8503774e75be32c25530ed74c06d8c", "guid": "bfdfe7dc352907fc980b868725387e98eb9aab8515725e70bf47b290f2ffbf9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfbd73416128e7dfcfff7874c37aff9d", "guid": "bfdfe7dc352907fc980b868725387e9887170e2e2f0e1231e8f66fc27d5d75a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d77c38755558606f7e79a9c184897186", "guid": "bfdfe7dc352907fc980b868725387e9895f19af6f6285e65ba1a6fc39d8d33fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408a752290da167fe489318446cdd9f9", "guid": "bfdfe7dc352907fc980b868725387e98151e8f2dcd937577d9c166ff24e38103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980451bdc1930bf16e0f016899efdf8d7c", "guid": "bfdfe7dc352907fc980b868725387e98adfcc487798a61542fe811b996202b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98424011662f9c45e2e2693d23140f578a", "guid": "bfdfe7dc352907fc980b868725387e98bfc667348db266d8236ddf09fe3f687c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cafa1476566ed1f37948698c3a28f328", "guid": "bfdfe7dc352907fc980b868725387e989bf3642b2c1c77a0defaabcbb23f9bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d141948fb70a129969e38e389c323c", "guid": "bfdfe7dc352907fc980b868725387e9814ca939d7ef14a75c492b565d9ff81d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342b71ef551a07495f4f9676e183e189", "guid": "bfdfe7dc352907fc980b868725387e98bcea5a5e9b0795d87ad828228aae02c4"}], "guid": "bfdfe7dc352907fc980b868725387e98af7c298017aa361478f3e0825042a7ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98850b05f3204449f19f358f0f3dc05b66", "guid": "bfdfe7dc352907fc980b868725387e98eaea5eaf3300cd8b30cd03886bb61628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884bfbca5c8afd67274918042f4f7fc50", "guid": "bfdfe7dc352907fc980b868725387e989f4403d826ead7e7553d2be609e28404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9490ec00f3e8ef3a037a4803e656a1", "guid": "bfdfe7dc352907fc980b868725387e98e4ac94164d6b5fc457d5f175a5efda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328c5b66ab8a17803370a7c58c5aabc6", "guid": "bfdfe7dc352907fc980b868725387e98342fc37c026beffd967da85b92328ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac29209c798a6bfa74c9fb107ea654d", "guid": "bfdfe7dc352907fc980b868725387e9877f340a3cd66ec724d38f2560ed29b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec82dece765d451054cf9546cb2f0d4", "guid": "bfdfe7dc352907fc980b868725387e98d6420868497ac30ec74b165bada2ea66"}], "guid": "bfdfe7dc352907fc980b868725387e98fef37a92c516d72d02a173ef676fb740", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbb22bb0f5869f65ac700b94ebefa304", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98a8d455f06110aff21c29e94460d34072", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}