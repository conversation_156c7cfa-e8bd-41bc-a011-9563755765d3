{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0ec12d70a6d284d219b523fcc133b3c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c846f957f8cdf1863d82d4e7656aa828", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824da7f8d62057a6527ab6cab1401e1f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e736c77b95d18f4cc0bb4510866f9e23", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824da7f8d62057a6527ab6cab1401e1f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b64292fb4f64215e823374c66e4b7ef8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882410c7bb298730130fafa4883435f60", "guid": "bfdfe7dc352907fc980b868725387e98aa30ec20d4709054f3d802cb4d1b2a08", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a5f03272055ae4eae90eb920c8cff2a7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98da536d6efee7da5e4e4e35ddcbb76bd6", "guid": "bfdfe7dc352907fc980b868725387e98968aa8761f77e671e4ac9569fede58c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d000dfeedd03201796aae0e442c4e86f", "guid": "bfdfe7dc352907fc980b868725387e98233ce6d3744e4c256d2799416e5278b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec2c9b7714fb6a129af94e17e3323c4", "guid": "bfdfe7dc352907fc980b868725387e989efafa635e6f1a75d1b5034b874cf526"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebe3edc5414adf853bafeda9b19d23d", "guid": "bfdfe7dc352907fc980b868725387e9885598d0dd2f3c281d86a7f95cec6cf0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811aa1ebd61efdb54a4f8a66c8855e54f", "guid": "bfdfe7dc352907fc980b868725387e984c10a219f50c61c92edfef5d3207aa3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988655ef5fae827ee2e853ab9f46b269ca", "guid": "bfdfe7dc352907fc980b868725387e982bcb968d0289cc8ea0a5a1018650ca55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db60dd7fc01f30932f78939a78cd6f3f", "guid": "bfdfe7dc352907fc980b868725387e9855bce57d4520803968669c40649f52e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b80522c9dcfce674ffe87eaccb428107", "guid": "bfdfe7dc352907fc980b868725387e98247628004723487f25ae23e8fb898441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1fcfaf045b86210dd1b478cc14542cd", "guid": "bfdfe7dc352907fc980b868725387e98bdc7faa614da525a962faf51da8acc29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982473e4c7c3b01f79d8257a72ffd34611", "guid": "bfdfe7dc352907fc980b868725387e98331b2e9237c72069e35ed1d07e8657e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6a692461e8baf75c6cc584151fb97b", "guid": "bfdfe7dc352907fc980b868725387e988bf96574f738338ffac7471e51076bba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f757f0003a66c8fb23538702ba4abb8d", "guid": "bfdfe7dc352907fc980b868725387e987730fdfa37903b0e225d0dfc0504a079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e5f7625901dc21fb0e7e1eb5adf328b", "guid": "bfdfe7dc352907fc980b868725387e986f12774af8d6e44bf27641b2e75d4369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dedf70a58b154dc2b6367b771601b1a4", "guid": "bfdfe7dc352907fc980b868725387e98736d71ddf5fb39e79c44216129ae3e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68eff946907f560ed99639c5da7edba", "guid": "bfdfe7dc352907fc980b868725387e982aff83edfb10a2e30ffb9bfcdd748016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98550fd5656368da68cd4a9493aa4aee09", "guid": "bfdfe7dc352907fc980b868725387e981bea325db6062ccec6329bd99508bcc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5517a9d00a5ce8e003a5d1733d24b79", "guid": "bfdfe7dc352907fc980b868725387e98ceafc3284b369410d30a4993c5d993b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf718f54646157de12e0cfa6a952d75b", "guid": "bfdfe7dc352907fc980b868725387e989edf4f600a82adc947a2f5c2c0bb58f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c5fe4e992fac9a8d5f2fdaa1cf02ec1", "guid": "bfdfe7dc352907fc980b868725387e98755bf3ce034be751f7450109604a0642"}], "guid": "bfdfe7dc352907fc980b868725387e98801fd21c59ea54f986de614e4ee23666", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98aefe86c768c2ffcdb7ef77441bfb9299"}], "guid": "bfdfe7dc352907fc980b868725387e98948b719e4fec60a1b279f86768bbb1ba", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b0530875beee3c37dc40c7480a69b5f3", "targetReference": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871a5c175c43308b453cbf34176ba8e8c", "guid": "bfdfe7dc352907fc980b868725387e989b3c20c53458434dd884c1dc5f44d3c1"}], "guid": "bfdfe7dc352907fc980b868725387e98718e5421351eba62122ec356018414fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d", "name": "PhoneNumberKit-PhoneNumberKitPrivacy"}], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}