{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4a6340650432554e93fed7304f0dcae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897f3ecdbecf7009b0a34e20f6a090bc5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc3b35e3e9fab78dea538820bfad539e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cda11128bb241ff2053eb0f4eff62fc2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc3b35e3e9fab78dea538820bfad539e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff009be39c01c2e24ce7bbd6eb331f2e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c08767796e5384ba1c24fa6212cfbc5", "guid": "bfdfe7dc352907fc980b868725387e98a7ba57aa50e411fcf5d83bd5b07ab633", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987970ce3ee40bf1168b6369f6ee18d4f7", "guid": "bfdfe7dc352907fc980b868725387e981fd22f8953fa8f37875aeddae8fd9ae8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984582a671aab51995d9c61e7273dcd353", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98756b9b799723fe4ef945f4dc103259d4", "guid": "bfdfe7dc352907fc980b868725387e98ea4df940ebe20fbfb4dfd13c55865b1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8575ad928c754f12c3a426849419b75", "guid": "bfdfe7dc352907fc980b868725387e98ef11c0911ca7678debe6be312deb08d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d5a45af995a3fe57c231221ff3d0d5", "guid": "bfdfe7dc352907fc980b868725387e9843b0d1f38348e24be58ae7a6bc045af8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c779bcec85c61f767772644bb37e086a", "guid": "bfdfe7dc352907fc980b868725387e983e98dd3b7c4606ab71585c51a17f94ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbf951ccf3b5762a42d3605941a60e8b", "guid": "bfdfe7dc352907fc980b868725387e98e01996639b0ab61e0664417e7eb8a93a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd28209efff8b9c5201c53a5d1e9016f", "guid": "bfdfe7dc352907fc980b868725387e982f56fa3d19c237a8e7537f8149b88ef0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e593af2d765e615ebd49d75ed51b1ed8", "guid": "bfdfe7dc352907fc980b868725387e983e7bcc6cc52c669d6fa2f901d66a541b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db886ebd2af8b2463995ca9f1de501d9", "guid": "bfdfe7dc352907fc980b868725387e98c30df776023697d1be9fcebe125c1149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f388c91a16e48ba3ee19ffee6a667b8b", "guid": "bfdfe7dc352907fc980b868725387e98ea43c2d2e3272b77333334938e7ff548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c40cb48c0b4647f79d9b28187de948f", "guid": "bfdfe7dc352907fc980b868725387e982a4b2e33152dbbfebdb78182b9866765"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367167873077023521051f8ab478b33e", "guid": "bfdfe7dc352907fc980b868725387e988f39525b61439763cc4f4b887a52b36c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e1b9b17bcb31515469f95a06154b32", "guid": "bfdfe7dc352907fc980b868725387e985205067a62e6894b87443f58af9d6748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc89c972405c519e55e59623b52bfb6d", "guid": "bfdfe7dc352907fc980b868725387e9851d2a59f527820db3ede38d8c981beeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0603bfc4d8f468c62a8941dad85ba99", "guid": "bfdfe7dc352907fc980b868725387e98fa001d92041380d7793961c95842299f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0d9e0e22d5cb95a016cf1879cbd7a6", "guid": "bfdfe7dc352907fc980b868725387e98e32a0065944159c07504195616dec969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162bc4d2df36ab2884b862c166018d8e", "guid": "bfdfe7dc352907fc980b868725387e98491b3c255ca07544608f5263ca2f708a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8fc895e530945f0e3464b711110938", "guid": "bfdfe7dc352907fc980b868725387e98b3e5b67a94ddf487a0466cf38dcc3e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98593604f9c8f1d23a121f37a5346be73e", "guid": "bfdfe7dc352907fc980b868725387e98dc94e1de1ab0a80f8965ed985d87e232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94d96bc97c3a55fe616ea2bc7a2cdd3", "guid": "bfdfe7dc352907fc980b868725387e9808bab4c827b048d281fc5123a149d522"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f0bb4b18b23ea298eb43e80e68e42f", "guid": "bfdfe7dc352907fc980b868725387e98d34bb8bd676dcbe27c523cd9c9daa6ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98204017fb1f124a6e8c96769173cf8ac5", "guid": "bfdfe7dc352907fc980b868725387e98a8336682eb21537ce7f5bd6829ef3c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad512e3f405875930710f70058c3372", "guid": "bfdfe7dc352907fc980b868725387e989222e00199a07ab78206884f0cf8e21d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5a83503aa35ecc855296dc8281632c", "guid": "bfdfe7dc352907fc980b868725387e988282e57aee4408f0c549717760ffe4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d498b04593efff55bff873208ea8ed", "guid": "bfdfe7dc352907fc980b868725387e982abd84a14a82535d5d37441cca813554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef0caa394bf3e521129f5e378b13617", "guid": "bfdfe7dc352907fc980b868725387e988387860438e3bd3f55625c850117fb34"}], "guid": "bfdfe7dc352907fc980b868725387e9838f396aecd5542d54d0ecc729ce1bb84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f9f7e9ce858fe8e3bd5e0bf1fe09ffd2"}], "guid": "bfdfe7dc352907fc980b868725387e9804a72fdb008bb4dc60995648a0da940b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983791f84e6df97f16ea369b6340a03c5e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}