<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Yemek Kapımda Sipariş Uygulaması</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>yemekkapimda</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>NSLocationWhenInUseUsageDescription</key>
    <string>Bu uygulama adres seçimi için konumunuza ihtiyaç duyar</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>Bu uygulama adres seçimi için konumunuza ihtiyaç duyar</string>
	<!-- reCAPTCHA site key for Firebase Auth -->
	<key>GoogleRecaptchaSiteKey</key>
	<string>6LcnNMwrAAAAAOeTiS4uFO7aQum10lOowY9n9J1d</string>

	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.googleusercontent.apps.201585951998-dlurke0jga7r007j2gqr0o8g5ses7ni5</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.201585951998-dlurke0jga7r007j2gqr0o8g5ses7ni5</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>app-1-201585951998-ios-e381846a1c8bdfb31211af</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-201585951998-ios-e381846a1c8bdfb31211af</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library to allow users to upload photos.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app requires access to your location to provide location-based services.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>processing</string>
	</array>
	<key>NSAppleMusicUsageDescription</key>
	<string>Bildirimler için izin gerekiyor</string>
	<key>NSCameraUsageDescription</key>
	<string>Kamera kullanımı için izin gerekiyor</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Mikrofon kullanımı için izin gerekiyor</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Fotoğraf galerisi kullanımı için izin gerekiyor</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>dev.flutter.background.refresh</string>
		<string>com.yemekkapimda.user.background</string>
	</array>
</dict>
</plist>