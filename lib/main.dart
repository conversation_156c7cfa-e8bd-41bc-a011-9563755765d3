import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:yemekkapimda/views/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'controller/storage_controller.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:get/get.dart';
import 'services/event_bus_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    name: 'yemekkapimda',
    options: DefaultFirebaseOptions.currentPlatform,
  );

  String? countryCode = await StorageController.instance.getCountryCode();
  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

  Get.put(EventBusService());
  runApp(YemekKapimdaApp(
    countryCode: countryCode,
  ));

}

Future<void> checkForUpdate() async {
  final updateInfo = await InAppUpdate.checkForUpdate();
  if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
    await InAppUpdate.performImmediateUpdate();
  }
}
