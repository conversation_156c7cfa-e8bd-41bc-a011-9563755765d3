// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDPkqccLCgI9uwbLod0vQjR99hmIn2aKWI',
    appId: '1:201585951998:web:ddd2de9927fb628b1211af',
    messagingSenderId: '201585951998',
    projectId: 'yemekkapimda-46ecc',
    authDomain: 'yemekkapimda-46ecc.firebaseapp.com',
    storageBucket: 'yemekkapimda-46ecc.appspot.com',
    measurementId: 'G-H8SR5F9XGF',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCu9gwwuVQCKs_luvd3XzDFJzfUdaqvdW0',
    appId: '1:201585951998:android:bd9a11ce2766bcf61211af',
    messagingSenderId: '201585951998',
    projectId: 'yemekkapimda-46ecc',
    storageBucket: 'yemekkapimda-46ecc.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDrcE0xXYSomruNwxmKUSQJpV9nf3KXoXg',
    appId: '1:201585951998:ios:e381846a1c8bdfb31211af',
    messagingSenderId: '201585951998',
    projectId: 'yemekkapimda-46ecc',
    storageBucket: 'yemekkapimda-46ecc.appspot.com',
    iosBundleId: 'com.yemekkapimda.user',
  );

}