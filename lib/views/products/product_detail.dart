import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'widgets/features_list.dart';

class ProductDetailScreen extends StatefulWidget {
  final String productId;

  const ProductDetailScreen({Key? key, required this.productId})
      : super(key: key);

  @override
  State<ProductDetailScreen> createState() => ProductDetailScreenState();
}

class ProductDetailScreenState extends State<ProductDetailScreen> {
  DarkModeController darkModeController = Get.put(DarkModeController());
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final ValueNotifier<List<String>> selectedFeaturesNotifier =
      ValueNotifier<List<String>>([]);
  final ValueNotifier<double> totalPriceNotifier = ValueNotifier<double>(0.0);
  String userId = '';
  String? previousRoute;

  @override
  void initState() {
    super.initState();
    final FirebaseAuth auth = FirebaseAuth.instance;
    User? currentUser = auth.currentUser;
    userId = currentUser?.uid ?? 'unknownUserId';
    previousRoute = Get.previousRoute;
  }

  @override
  void dispose() {
    selectedFeaturesNotifier.dispose();
    totalPriceNotifier.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // parsePrice metodunu geri ekleyelim
  double parsePrice(dynamic price) {
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    } else if (price is int) {
      return price.toDouble();
    } else if (price is double) {
      return price;
    }
    return 0.0;
  }

  // Firestore'dan ürün bilgilerini çekmek için fonksiyon
  Future<Map<String, dynamic>?> getProductData() async {
    DocumentSnapshot doc = await FirebaseFirestore.instance
        .collection('products')
        .doc(widget.productId)
        .get();
    if (doc.exists) {
      var data = doc.data() as Map<String, dynamic>?;
      data?['id'] = doc.id;
      return data;
    }
    return null;
  }

  // Firma bilgilerini getiren fonksiyon
  Future<DocumentSnapshot?> getFirmData(String firmId) async {
    DocumentSnapshot doc =
        await FirebaseFirestore.instance.collection('firms').doc(firmId).get();
    return doc.exists ? doc : null;
  }

  // Kategori bilgilerini getiren fonksiyon
  Future<DocumentSnapshot?> getCategoryData(String categoryId) async {
    DocumentSnapshot doc = await FirebaseFirestore.instance
        .collection('categories')
        .doc(categoryId)
        .get();
    return doc.exists ? doc : null;
  }

  // Sepete ekleme fonksiyonu
  void addToCart(
      String productId,
      String productName,
      double basePrice,
      int quantity,
      List<String> selectedFeatures,
      List<dynamic> features,
      String imageURL,
      String categoryId,
      String firmId) async {
    if (userId == 'unknownUserId') {
      Get.toNamed(AppRoutes.signInScreen);
      return;
    }

    try {
      DocumentSnapshot productDoc = await FirebaseFirestore.instance
          .collection('products')
          .doc(productId)
          .get();

      if (!productDoc.exists) {
        throw 'Ürün bulunamadı';
      }

      // Seçili özellikleri ve fiyatları hazırla
      List<Map<String, dynamic>> selectedFeaturesWithPrices = [];
      double extrasPrice = 0.0;

      for (String featureName in selectedFeatures) {
        var feature = features.firstWhere(
          (f) => f['name'] == featureName,
          orElse: () => null,
        );

        if (feature != null) {
          double featurePrice = parsePrice(feature['price']);
          extrasPrice += featurePrice;

          selectedFeaturesWithPrices.add({
            'name': featureName,
            'price': feature['price'].toString(),
          });
        }
      }

      double totalPrice = basePrice + extrasPrice;

      Map<String, dynamic> cartItem = {
        "productId": productId,
        "id": productId,
        "docId": productId,
        "productName": productName,
        "quantity": quantity,
        "price": totalPrice,
        "imageURL": imageURL,
        "categoryId": categoryId,
        "firmId": firmId,
        "addedAt": FieldValue.serverTimestamp(),
        "status": "sipariş alındı",
        "selectedFeatures":
            selectedFeaturesWithPrices, // Seçili özellikleri direkt ekle
      };

      await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('activeCartWeb')
          .doc('cart')
          .collection('items')
          .add(cartItem);

      Get.snackbar(
        'Başarılı',
        'Ürün sepete eklendi.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorConfig.kPrimaryColor,
        colorText: ColorConfig.kWhiteColor,
        margin: const EdgeInsets.all(SizeConfig.kHeight20),
        borderRadius: SizeConfig.borderRadius12,
      );
    } catch (error) {
      Get.snackbar(
        'Hata',
        'Sepete ekleme başarısız: $error',
        snackPosition: SnackPosition.TOP,
        backgroundColor: ColorConfig.kErrorColor,
        colorText: ColorConfig.kWhiteColor,
        margin: const EdgeInsets.all(SizeConfig.kHeight20),
        borderRadius: SizeConfig.borderRadius12,
      );
    }
  }

  // UI Bileşenleri
  Widget buildBreadcrumbs(String firmName, String categoryName, String firmId) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SizeConfig.kHeight20,
        vertical: SizeConfig.kHeight10,
      ),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        boxShadow: [
          BoxShadow(
            color: ColorConfig.kBlackColor.withAlpha(15),
            blurRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Get.back(),
            child: Text(
              firmName,
              style: TextStyle(
                color: ColorConfig.kPrimaryColor,
                fontSize: FontConfig.kFontSize14,
                fontFamily: FontFamilyConfig.urbanistMedium,
              ),
            ),
          ),
          const Icon(Icons.chevron_right, size: 16),
          Expanded(
            child: Text(
              categoryName,
              style: TextStyle(
                color: ColorConfig.kHintColor,
                fontSize: FontConfig.kFontSize14,
                fontFamily: FontFamilyConfig.urbanistMedium,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildImageSlider(List<String> images) {
    return Stack(
      children: [
        SizedBox(
          height: SizeConfig.kHeight400,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemCount: images.length,
            itemBuilder: (context, index) {
              return Image.network(
                images[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: ColorConfig.kFillColor,
                  child: Icon(
                    Icons.image_not_supported,
                    size: 50,
                    color: ColorConfig.kHintColor,
                  ),
                ),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: ColorConfig.kPrimaryColor,
                    ),
                  );
                },
              );
            },
          ),
        ),
        // Sayfa göstergesi (dots)
        Positioned(
          bottom: SizeConfig.kHeight20,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              images.length,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentPage == index
                      ? ColorConfig.kPrimaryColor
                      : ColorConfig.kWhiteColor.withAlpha(125),
                ),
              ),
            ),
          ),
        ),
        // Geri butonu
        Positioned(
          top: SizeConfig.kHeight20,
          left: SizeConfig.kHeight20,
          child: GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorConfig.kWhiteColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: ColorConfig.kBlackColor.withAlpha(25),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: ColorConfig.kPrimaryColor,
                size: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildFirmCard(Map<String, dynamic> firmData, String firmId) {
    // firmId parametresi eklendi
    return GestureDetector(
      onTap: () => Get.toNamed(AppRoutes.firmDetail,
          parameters: {'firmId': firmId}), // Firma detay sayfasına yönlendirme
      child: Container(
        padding: const EdgeInsets.all(SizeConfig.kHeight15),
        decoration: BoxDecoration(
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kFillColor
              : ColorConfig.kDarkModeColor,
          borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
              child: Image.network(
                firmData['logo'] ?? '',
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 50,
                  height: 50,
                  color: ColorConfig.kPrimaryColor,
                  child: const Icon(Icons.store, color: Colors.white),
                ),
              ),
            ),
            const SizedBox(width: SizeConfig.kHeight15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    firmData['name'] ?? 'Unknown Firm',
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistSemiBold,
                      fontSize: FontConfig.kFontSize16,
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kBlackColor
                          : ColorConfig.kWhiteColor,
                    ),
                  ),
                ],
              ),
            ),
            // Sağ ok ikonu eklendi
            Icon(
              Icons.chevron_right,
              color: ColorConfig.kPrimaryColor,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => SafeArea(
          child: Scaffold(
            backgroundColor: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kBlackColor,
            body: FutureBuilder(
              future: Future.wait([
                getProductData(),
                // Diğer future'lar productData'ya bağlı olduğu için
                // onları ikinci FutureBuilder'da çağıracağız
              ]),
              builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError || !snapshot.hasData) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: ColorConfig.kPrimaryColor,
                        ),
                        const SizedBox(height: SizeConfig.kHeight10),
                        Text(
                          "Ürün bulunamadı.",
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistMedium,
                            fontSize: FontConfig.kFontSize16,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                var productData = snapshot.data![0] as Map<String, dynamic>?;
                if (productData == null) {
                  return const Center(child: Text("Ürün bulunamadı."));
                }

                double basePrice = parsePrice(productData['price']);
                if (totalPriceNotifier.value == 0) {
                  totalPriceNotifier.value = basePrice; // İlk yükleme için
                }

                return FutureBuilder(
                  future: Future.wait([
                    getFirmData(productData['firmId']),
                    getCategoryData(productData['category']),
                  ]),
                  builder: (context,
                      AsyncSnapshot<List<dynamic>> additionalDataSnapshot) {
                    if (additionalDataSnapshot.connectionState ==
                        ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (additionalDataSnapshot.hasError ||
                        !additionalDataSnapshot.hasData) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: ColorConfig.kPrimaryColor,
                            ),
                            const SizedBox(height: SizeConfig.kHeight10),
                            Text(
                              "Data could not be loaded.",
                              style: TextStyle(
                                fontFamily: FontFamilyConfig.urbanistMedium,
                                fontSize: FontConfig.kFontSize16,
                                color: darkModeController.isLightTheme.value
                                    ? ColorConfig.kBlackColor
                                    : ColorConfig.kWhiteColor,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    var firmData = additionalDataSnapshot.data?[0]?.data()
                        as Map<String, dynamic>?;
                    var categoryData = additionalDataSnapshot.data?[1]?.data()
                        as Map<String, dynamic>?;

                    return Column(
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (firmData != null && categoryData != null)
                                  buildBreadcrumbs(
                                    firmData['name'],
                                    categoryData['name'],
                                    productData['firmId'],
                                  ),
                                buildImageSlider(
                                    List<String>.from(productData['images'])),
                                Padding(
                                  padding: const EdgeInsets.all(
                                      SizeConfig.kHeight20),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (firmData != null)
                                        buildFirmCard(
                                            firmData, productData['firmId']),
                                      const SizedBox(
                                          height: SizeConfig.kHeight20),
                                      Text(
                                        productData['name'],
                                        style: TextStyle(
                                          fontFamily:
                                              FontFamilyConfig.urbanistSemiBold,
                                          fontSize: FontConfig.kFontSize22,
                                          color: darkModeController
                                                  .isLightTheme.value
                                              ? ColorConfig.kBlackColor
                                              : ColorConfig.kWhiteColor,
                                        ),
                                      ),
                                      const SizedBox(
                                          height: SizeConfig.kHeight15),
                                      Container(
                                        padding: const EdgeInsets.all(
                                            SizeConfig.kHeight15),
                                        decoration: BoxDecoration(
                                          color: darkModeController
                                                  .isLightTheme.value
                                              ? ColorConfig.kFillColor
                                              : ColorConfig.kDarkModeColor,
                                          borderRadius: BorderRadius.circular(
                                              SizeConfig.borderRadius12),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              productData['description'],
                                              style: TextStyle(
                                                fontFamily: FontFamilyConfig
                                                    .urbanistRegular,
                                                fontSize:
                                                    FontConfig.kFontSize14,
                                                color: darkModeController
                                                        .isLightTheme.value
                                                    ? ColorConfig.kBlackColor
                                                    : ColorConfig.kWhiteColor,
                                                height: 1.5,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(
                                          height: SizeConfig.kHeight20),
                                      if (productData['features'] != null &&
                                          (productData['features'] as List)
                                              .isNotEmpty) ...[
                                        Text(
                                          'Ekstra Seçenekler',
                                          style: TextStyle(
                                            fontFamily: FontFamilyConfig
                                                .urbanistSemiBold,
                                            fontSize: FontConfig.kFontSize18,
                                            color: darkModeController
                                                    .isLightTheme.value
                                                ? ColorConfig.kBlackColor
                                                : ColorConfig.kWhiteColor,
                                          ),
                                        ),
                                        const SizedBox(
                                            height: SizeConfig.kHeight15),
                                        ValueListenableBuilder<List<String>>(
                                          valueListenable:
                                              selectedFeaturesNotifier,
                                          builder: (context, selectedFeatures,
                                              child) {
                                            return FeaturesList(
                                              features: productData['features'],
                                              isDarkMode: !darkModeController
                                                  .isLightTheme.value,
                                              selectedFeatures:
                                                  selectedFeatures,
                                              basePrice: basePrice,
                                              onFeaturesChanged: (features) {
                                                selectedFeaturesNotifier.value =
                                                    List.from(features);
                                              },
                                              onTotalPriceChanged: (newTotal) {
                                                totalPriceNotifier.value =
                                                    newTotal;
                                              },
                                            );
                                          },
                                        ),
                                      ],
                                      const SizedBox(
                                          height: SizeConfig.kHeight20),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(SizeConfig.kHeight20),
                          decoration: BoxDecoration(
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kWhiteColor
                                : ColorConfig.kDarkModeColor,
                            boxShadow: [
                              BoxShadow(
                                color: ColorConfig.kBlackColor.withAlpha(15),
                                blurRadius: 10,
                                offset: const Offset(0, -5),
                              ),
                            ],
                          ),
                          child: SafeArea(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Toplam Tutar:',
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistMedium,
                                        fontSize: FontConfig.kFontSize16,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kBlackColor
                                            : ColorConfig.kWhiteColor,
                                      ),
                                    ),
                                    ValueListenableBuilder<double>(
                                      valueListenable: totalPriceNotifier,
                                      builder: (context, totalPrice, child) {
                                        return Text(
                                          '${totalPrice.toStringAsFixed(2)} TL',
                                          style: TextStyle(
                                            fontFamily: FontFamilyConfig
                                                .urbanistSemiBold,
                                            fontSize: FontConfig.kFontSize20,
                                            color: ColorConfig.kPrimaryColor,
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                const SizedBox(height: SizeConfig.kHeight15),
                                ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ColorConfig.kPrimaryColor,
                                    minimumSize: const Size(
                                        double.infinity, SizeConfig.kHeight50),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          SizeConfig.borderRadius12),
                                    ),
                                    elevation: 0,
                                  ),
                                  onPressed: () {
                                    addToCart(
                                      productData['id'],
                                      productData['name'],
                                      basePrice,
                                      1,
                                      selectedFeaturesNotifier.value,
                                      productData['features'] ?? [],
                                      productData['images'][0],
                                      productData['category'],
                                      productData['firmId'],
                                    );
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.shopping_cart_outlined,
                                        color: ColorConfig.kWhiteColor,
                                      ),
                                      const SizedBox(
                                          width: SizeConfig.kHeight10),
                                      Text(
                                        'Sepete Ekle',
                                        style: TextStyle(
                                          fontFamily:
                                              FontFamilyConfig.urbanistSemiBold,
                                          fontSize: FontConfig.kFontSize16,
                                          color: ColorConfig.kWhiteColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ));
  }
}
