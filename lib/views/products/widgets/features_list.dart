import 'package:flutter/material.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';

class FeaturesList extends StatefulWidget {
  final List<dynamic> features;
  final bool isDarkMode;
  final List<String> selectedFeatures;
  final Function(List<String>) onFeaturesChanged;
  final Function(double) onTotalPriceChanged;
  final double basePrice;

  const FeaturesList({
    Key? key,
    required this.features,
    required this.isDarkMode,
    required this.selectedFeatures,
    required this.onFeaturesChanged,
    required this.onTotalPriceChanged,
    required this.basePrice,
  }) : super(key: key);

  @override
  State<FeaturesList> createState() => _FeaturesListState();
}

class _FeaturesListState extends State<FeaturesList> {
  double parsePrice(dynamic price) {
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    } else if (price is int) {
      return price.toDouble();
    } else if (price is double) {
      return price;
    }
    return 0.0;
  }

  void _handleFeatureChange(String featureName, bool? value) {
    List<String> newSelectedFeatures = List.from(widget.selectedFeatures);

    if (value == true) {
      newSelectedFeatures.add(featureName);
    } else {
      newSelectedFeatures.remove(featureName);
    }

    double extrasPrice = 0.0;
    for (var feature in widget.features) {
      if (newSelectedFeatures.contains(feature['name'])) {
        extrasPrice += parsePrice(feature['price']);
      }
    }

    widget.onFeaturesChanged(newSelectedFeatures);
    widget.onTotalPriceChanged(widget.basePrice + extrasPrice);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.isDarkMode
            ? ColorConfig.kDarkModeColor
            : ColorConfig.kFillColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.features.length,
        separatorBuilder: (context, index) => Divider(
          color: ColorConfig.kDividerColor.withAlpha(75),
          height: 1,
        ),
        itemBuilder: (context, index) {
          var feature = widget.features[index];
          return CheckboxListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: SizeConfig.kHeight15,
              vertical: SizeConfig.kHeight5,
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorConfig.kPrimaryColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '+${parsePrice(feature['price'])} TL',
                    style: TextStyle(
                      fontSize: FontConfig.kFontSize12,
                      color: ColorConfig.kPrimaryColor,
                      fontFamily: FontFamilyConfig.urbanistMedium,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    feature['name'],
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistRegular,
                      fontSize: FontConfig.kFontSize14,
                      color: widget.isDarkMode
                          ? ColorConfig.kWhiteColor
                          : ColorConfig.kBlackColor,
                    ),
                  ),
                ),
              ],
            ),
            value: widget.selectedFeatures.contains(feature['name']),
            onChanged: (value) {
              if (value == true) {
                final newFeatures = List<String>.from(widget.selectedFeatures)
                  ..add(feature['name']);
                widget.onFeaturesChanged(newFeatures);
              } else {
                final newFeatures = List<String>.from(widget.selectedFeatures)
                  ..remove(feature['name']);
                widget.onFeaturesChanged(newFeatures);
              }
              _handleFeatureChange(feature['name'], value);
            },
            activeColor: ColorConfig.kPrimaryColor,
            checkColor: ColorConfig.kWhiteColor,
          );
        },
      ),
    );
  }
}
