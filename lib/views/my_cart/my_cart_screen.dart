import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../utils/common_material_button.dart';
import '../../config/image_path.dart';
import '../../utils/appbar_common.dart';
import 'package:firebase_auth/firebase_auth.dart';

class MyCartScreen extends StatefulWidget {
  const MyCartScreen({Key? key}) : super(key: key);

  @override
  State<MyCartScreen> createState() => _MyCartScreenState();
}

class _MyCartScreenState extends State<MyCartScreen> {
  DarkModeController darkModeController = Get.put(DarkModeController());
  FirebaseFirestore firestore = FirebaseFirestore.instance;
  FirebaseAuth auth = FirebaseAuth.instance;
  Map<String, bool> firmVisibilityStatus = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (auth.currentUser == null) {
        Get.toNamed(AppRoutes.signInScreen);
      }
    });
  }

  void incrementCounter(DocumentSnapshot cartItem) {
    firestore
        .collection('users')
        .doc(auth.currentUser!.uid)
        .collection('activeCartWeb')
        .doc('cart')
        .collection('items')
        .doc(cartItem.id)
        .update({
      'quantity': cartItem['quantity'] + 1,
      'timestamp': FieldValue.serverTimestamp()
    });
  }

  void decrementCounter(DocumentSnapshot cartItem) {
    if (cartItem['quantity'] > 1) {
      firestore
          .collection('users')
          .doc(auth.currentUser!.uid)
          .collection('activeCartWeb')
          .doc('cart')
          .collection('items')
          .doc(cartItem.id)
          .update({
        'quantity': cartItem['quantity'] - 1,
        'timestamp': FieldValue.serverTimestamp()
      });
    } else {
      firestore
          .collection('users')
          .doc(auth.currentUser!.uid)
          .collection('activeCartWeb')
          .doc('cart')
          .collection('items')
          .doc(cartItem.id)
          .delete();
    }
  }

  double calculateTotalPrice(List<DocumentSnapshot> cartItems) {
    double total = 0.0;
    for (var item in cartItems) {
      total += (item['price'] as num).toDouble() * item['quantity'];
    }
    return total;
  }

  void navigateToCompany(String companyId) {
    Get.toNamed(AppRoutes.firmDetail, parameters: {'firmId': companyId});
  }

  Widget buildCompanyHeader(String firmId, String firmName) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SizeConfig.kHeight16,
        vertical: SizeConfig.kHeight12,
      ),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kButtonLightColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.store,
            color: ColorConfig.kPrimaryColor,
            size: 20,
          ),
          const SizedBox(width: SizeConfig.kHeight8),
          Expanded(
            child: InkWell(
              onTap: () => navigateToCompany(firmId),
              child: Text(
                firmName,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontFamily: FontFamilyConfig.urbanistSemiBold,
                  color: ColorConfig.kPrimaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCartItem(DocumentSnapshot cartItem) {
    double itemTotalPrice =
        (cartItem['price'] as num).toDouble() * cartItem['quantity'];

    List<Map<String, dynamic>> selectedFeatures =
        List<Map<String, dynamic>>.from(cartItem['selectedFeatures'] ?? []);

    return Container(
      margin: const EdgeInsets.only(left: SizeConfig.kHeight12),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        boxShadow: [
          BoxShadow(
            spreadRadius: 2,
            color: ColorConfig.kShadowColor.withAlpha(25),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
          onTap: () {
            Get.toNamed(AppRoutes.productDetail,
                parameters: {'productId': cartItem['productId']});
          },
          child: Padding(
            padding: const EdgeInsets.all(SizeConfig.kHeight12),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Hero(
                      tag: 'product-${cartItem['productId']}',
                      child: ClipRRect(
                        borderRadius:
                            BorderRadius.circular(SizeConfig.borderRadius8),
                        child: Image.network(
                          cartItem['imageURL'],
                          width: SizeConfig.kHeight100,
                          height: SizeConfig.kHeight100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: SizeConfig.kHeight100,
                              height: SizeConfig.kHeight100,
                              color: ColorConfig.kBgColor,
                              child: Icon(Icons.image_not_supported,
                                  color: ColorConfig.kHintColor),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: SizeConfig.kHeight12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            cartItem['productName'],
                            style: TextStyle(
                              fontSize: FontConfig.kFontSize16,
                              fontFamily: FontFamilyConfig.urbanistSemiBold,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kTextColor
                                  : ColorConfig.kWhiteColor,
                            ),
                          ),
                          const SizedBox(height: SizeConfig.kHeight8),
                          if (selectedFeatures.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                selectedFeatures
                                    .map((feature) =>
                                        "${feature['name']} (+${feature['price']} TL)")
                                    .join(', '),
                                style: TextStyle(
                                  fontSize: FontConfig.kFontSize13,
                                  fontFamily: FontFamilyConfig.urbanistRegular,
                                  color: darkModeController.isLightTheme.value
                                      ? ColorConfig.kTextfieldTextColor
                                      : ColorConfig.kWhiteColor.withAlpha(165),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: SizeConfig.kHeight12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${itemTotalPrice.toStringAsFixed(2)} TL",
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize16,
                        fontFamily: FontFamilyConfig.urbanistBold,
                        color: ColorConfig.kPrimaryColor,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConfig.kButtonLightColor,
                        borderRadius:
                            BorderRadius.circular(SizeConfig.borderRadius20),
                      ),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.remove),
                            color: ColorConfig.kPrimaryColor,
                            onPressed: () => decrementCounter(cartItem),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: SizeConfig.kHeight12,
                            ),
                            child: Text(
                              "${cartItem['quantity']}",
                              style: TextStyle(
                                fontSize: FontConfig.kFontSize16,
                                fontFamily: FontFamilyConfig.urbanistSemiBold,
                                color: darkModeController.isLightTheme.value
                                    ? ColorConfig.kTextColor
                                    : ColorConfig.kWhiteColor,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.add),
                            color: ColorConfig.kPrimaryColor,
                            onPressed: () => incrementCounter(cartItem),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> checkFirmVisibility(
      Map<String, List<DocumentSnapshot>> groupedItems) async {
    bool allFirmsVisible = true;
    List<String> closedFirmNames = [];

    for (String firmId in groupedItems.keys) {
      var firmDoc = await firestore.collection('firms').doc(firmId).get();
      bool isVisible = firmDoc.data()?['visible'] ?? true;
      String firmName = firmDoc.data()?['name'] ?? 'Bilinmeyen Firma';

      if (!isVisible) {
        closedFirmNames.add(firmName);
        allFirmsVisible = false;
      }
    }

    if (!allFirmsVisible) {
      Get.snackbar(
        'Sipariş Tamamlanamıyor',
        'Şu anda kapalı olan firmalar var: ${closedFirmNames.join(", ")}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
        snackPosition: SnackPosition.TOP,
      );
    }

    return allFirmsVisible;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: "Sepetim",
          leadingImage: ImagePath.arrow,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: SizeConfig.kHeight20,
          vertical: SizeConfig.kHeight10,
        ),
        child: StreamBuilder(
          stream: firestore
              .collection('users')
              .doc(auth.currentUser!.uid)
              .collection('activeCartWeb')
              .doc('cart')
              .collection('items')
              .snapshots(),
          builder: (context, AsyncSnapshot<QuerySnapshot> snapshot) {
            if (!snapshot.hasData) {
              return Center(
                child: CircularProgressIndicator(
                  color: ColorConfig.kPrimaryColor,
                ),
              );
            }

            var cartItems = snapshot.data!.docs;

            if (cartItems.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: 64,
                      color: ColorConfig.kHintColor.withAlpha(125),
                    ),
                    const SizedBox(height: SizeConfig.kHeight20),
                    Text(
                      "Sepetiniz boş",
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize18,
                        fontFamily: FontFamilyConfig.urbanistMedium,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kTextColor
                            : ColorConfig.kWhiteColor,
                      ),
                    ),
                    const SizedBox(height: SizeConfig.kHeight10),
                    TextButton(
                      onPressed: () => Get.back(),
                      child: Text(
                        "Alışverişe Başla",
                        style: TextStyle(
                          color: ColorConfig.kPrimaryColor,
                          fontSize: FontConfig.kFontSize16,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            // Group items by company
            Map<String, List<DocumentSnapshot>> groupedItems = {};
            for (var item in cartItems) {
              String firmId = item['firmId'];
              if (!groupedItems.containsKey(firmId)) {
                groupedItems[firmId] = [];
              }
              groupedItems[firmId]!.add(item);
            }

            double totalPrice = calculateTotalPrice(cartItems);

            return Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: groupedItems.length,
                    itemBuilder: (context, index) {
                      String firmId = groupedItems.keys.elementAt(index);
                      List<DocumentSnapshot> items = groupedItems[firmId]!;

                      return FutureBuilder<DocumentSnapshot>(
                        future: FirebaseFirestore.instance
                            .collection('firms')
                            .doc(firmId)
                            .get(),
                        builder: (context,
                            AsyncSnapshot<DocumentSnapshot> firmSnapshot) {
                          String firmName = 'Firma';
                          if (firmSnapshot.hasData &&
                              firmSnapshot.data!.exists) {
                            final firmData = firmSnapshot.data!.data()
                                as Map<String, dynamic>;
                            firmName = firmData['name'] ?? 'Firma';
                          }

                          return Padding(
                            padding: const EdgeInsets.only(
                                bottom: SizeConfig.kHeight20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                buildCompanyHeader(firmId, firmName),
                                const SizedBox(height: SizeConfig.kHeight12),
                                ...items.map((item) => Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: SizeConfig.kHeight12),
                                      child: buildCartItem(item),
                                    )),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: SizeConfig.kHeight16,
                    horizontal: SizeConfig.kHeight20,
                  ),
                  decoration: BoxDecoration(
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kWhiteColor
                        : ColorConfig.kDarkModeColor,
                    boxShadow: [
                      BoxShadow(
                        color: ColorConfig.kShadowColor.withAlpha(25),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Toplam Tutar",
                            style: TextStyle(
                              fontSize: FontConfig.kFontSize16,
                              fontFamily: FontFamilyConfig.urbanistMedium,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kTextColor
                                  : ColorConfig.kWhiteColor,
                            ),
                          ),
                          Text(
                            "${totalPrice.toStringAsFixed(2)} TL",
                            style: TextStyle(
                              fontSize: FontConfig.kFontSize20,
                              fontFamily: FontFamilyConfig.urbanistBold,
                              color: ColorConfig.kPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: SizeConfig.kHeight16),
                      CommonMaterialButton(
                        buttonColor: ColorConfig.kPrimaryColor,
                        height: SizeConfig.kHeight52,
                        txtColor: ColorConfig.kWhiteColor,
                        buttonText: "Siparişi Tamamla",
                        onButtonClick: () async {
                          if (auth.currentUser == null) {
                            Get.toNamed(AppRoutes.signInScreen);
                          } else {
                            bool canProceed =
                                await checkFirmVisibility(groupedItems);
                            if (canProceed) {
                              Get.toNamed(AppRoutes.checkoutOrder);
                            }
                          }
                        },
                        width: double.infinity,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
