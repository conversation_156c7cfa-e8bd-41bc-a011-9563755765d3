import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart' as flutter_map;
import 'package:latlong2/latlong.dart' as lat_lng;

// ignore: must_be_immutable
class MapScreen extends StatefulWidget {
  String latitude = '0.0';
  String longitude = '0.0';
  final double? padding;

  MapScreen({
    super.key,
    required this.latitude,
    required this.longitude,
    this.padding,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final flutter_map.MapController mapController = flutter_map.MapController();
  late lat_lng.LatLng initialLocation;
  List<flutter_map.Marker> markers = [];

  @override
  void initState() {
    super.initState();
    initialLocation = lat_lng.LatLng(
      double.parse(widget.latitude),
      double.parse(widget.longitude),
    );
    _updateMarkers();
  }

  void _updateMarkers() {
    markers = [
      flutter_map.Marker(
        point: initialLocation,
        child: const Icon(
          Icons.location_pin,
          color: Colors.red,
          size: 40,
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return flutter_map.FlutterMap(
      mapController: mapController,
      options: flutter_map.MapOptions(
        initialCenter: initialLocation,
        initialZoom: 14.4746,
      ),
      children: [
        flutter_map.TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.yemekkapimda.newapp',
        ),
        flutter_map.MarkerLayer(markers: markers),
      ],
    );
  }

  @override
  void dispose() {
    mapController.dispose();
    super.dispose();
  }
}
