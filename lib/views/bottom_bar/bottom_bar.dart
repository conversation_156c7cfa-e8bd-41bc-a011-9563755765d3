// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:yemekkapimda/app_routes/app_routes.dart';
import 'package:yemekkapimda/config/color.dart';
import 'package:yemekkapimda/config/image_path.dart';
import 'package:yemekkapimda/config/size_config.dart';
import 'package:yemekkapimda/controller/bottom_controller.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:yemekkapimda/views/home/<USER>';
import 'package:get/get.dart';
import '../favorite_screen/favorite_screen.dart';
import '../order_screen/order_screen.dart';
import '../profile/profile_screen.dart';
import 'common_bottombar.dart';

class BottomBarScreen extends StatefulWidget {
  const BottomBarScreen({Key? key}) : super(key: key);

  @override
  State<BottomBarScreen> createState() => _BottomBarScreenState();
}

class _BottomBarScreenState extends State<BottomBarScreen> {
  final BottomNavigationController bottomController = Get.put(BottomNavigationController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Sayfa yüklenirken kısa bir gecikme ekleyelim
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  void onTabTapped(int index) {
    bottomController.selected.value = index;
    bottomController.controller.jumpToPage(index);
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Obx(() => Scaffold(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          resizeToAvoidBottomInset: false,
          bottomNavigationBar:
              CommonBottomBar(bottomController: bottomController),
          floatingActionButton: FloatingActionButton(
              onPressed: () {
                Get.toNamed(AppRoutes.myCartScreen);
              },
              backgroundColor: ColorConfig.kPrimaryColor,
              child: Image.asset(
                ImagePath.cart,
                height: SizeConfig.kHeight25,
                width: SizeConfig.kHeight25,
              )),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          body: PageView(
            controller: bottomController.controller,
            physics: const NeverScrollableScrollPhysics(),
            children: const [
              HomePage(),
              FavoriteScreen(),
              OrderScreen(),
              ProfileScreen(),
            ],
          ),
        ));
  }
}
