import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart' as flutter_map;
import 'package:latlong2/latlong.dart' as lat_lng;

class MapView extends StatefulWidget {
  const MapView({Key? key}) : super(key: key);

  @override
  State<MapView> createState() => _MapViewState();
}

class _MapViewState extends State<MapView> {
  final flutter_map.MapController mapController = flutter_map.MapController();
  
  // Seydişehir'in koordinatları
  static const lat_lng.LatLng seydisehirLocation = lat_lng.LatLng(37.4187, 31.8489);

  @override
  Widget build(BuildContext context) {
    return flutter_map.FlutterMap(
      mapController: mapController,
      options: const flutter_map.MapOptions(
        initialCenter: seydisehirLocation,
        initialZoom: 15,
      ),
      children: [
        flutter_map.TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.yemekkapimda.newapp',
        ),
        const flutter_map.MarkerLayer(
          markers: [
            flutter_map.Marker(
              point: seydisehirLocation,
              child: Icon(
                Icons.location_pin,
                color: Colors.red,
                size: 40,
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    mapController.dispose();
    super.dispose();
  }
} 