import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yemekkapimda/views/restaurant/widgets/banner_section.dart';
import 'package:yemekkapimda/views/restaurant/widgets/categories_section.dart';
import 'package:yemekkapimda/views/restaurant/widgets/firm_info_section.dart';
import 'package:yemekkapimda/views/restaurant/widgets/products_section.dart';
import 'package:yemekkapimda/views/restaurant/widgets/rating_section.dart';
import '../../config/color.dart';

import '../../controller/dark_mode_controller.dart';

class FirmDetail extends StatelessWidget {
  final String firmId;
  final ScrollController _scrollController = ScrollController();
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final Map<String, String> _categoriesMap = {};
  final Map<String, GlobalKey> _categoryKeys = {};

  FirmDetail({
    super.key,
    required this.firmId,
  });

  Map<String, String> get categoriesMap => _categoriesMap;

  Future<Map<String, dynamic>?> getFirmData() async {
    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('firms')
          .doc(firmId)
          .get();
      return doc.exists ? doc.data() as Map<String, dynamic>? : null;
    } catch (e) {
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> getFirmProducts() async {
    QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('products')
        .where('firmId', isEqualTo: firmId)
        .get();

    return snapshot.docs.map((doc) {
      var data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return data;
    }).toList();
  }

  Future<void> getCategories() async {
    QuerySnapshot snapshot =
        await FirebaseFirestore.instance.collection('categories').get();

    _categoriesMap.clear();
    _categoriesMap.addAll(Map.fromEntries(snapshot.docs.map((doc) {
      var data = doc.data() as Map<String, dynamic>;
      return MapEntry(doc.id, data['name'] ?? 'Kategori Bilinmiyor');
    })));
  }

  void _scrollToCategory(String categoryName) {
    if (_categoryKeys.containsKey(categoryName)) {
      final RenderBox renderBox = _categoryKeys[categoryName]!
          .currentContext
          ?.findRenderObject() as RenderBox;
      final position = renderBox.localToGlobal(Offset.zero);

      _scrollController.animateTo(
        _scrollController.offset + position.dy - 150,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  List<String> sortCategories(List<String> categories) {
    var sortedList = categories.toList();

    // SOĞUK İÇECEKLER'i listeden çıkar
    sortedList.remove("SOĞUK İÇECEKLER");

    // SOĞUK İÇECEKLER varsa en sona ekle
    if (categories.contains("SOĞUK İÇECEKLER")) {
      sortedList.add("SOĞUK İÇECEKLER");
    }

    return sortedList;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => SafeArea(
          child: Scaffold(
            backgroundColor: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kBlackColor,
            body: FutureBuilder(
              future: Future.wait(
                  [getFirmData(), getCategories(), getFirmProducts()]),
              builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError || !snapshot.hasData) {
                  return const Center(child: Text("Veriler alınamadı."));
                }

                final firmData = snapshot.data![0] as Map<String, dynamic>?;
                final products =
                    snapshot.data![2] as List<Map<String, dynamic>>;
                final categorizedProducts = _categorizeProducts(products);
                final sortedCategories =
                    sortCategories(categorizedProducts.keys.toList());

                return RefreshIndicator(
                  color: ColorConfig.kPrimaryColor,
                  onRefresh: () async {
                    // Tüm verileri yeniden yükle
                    await Future.wait([
                      getFirmData(),
                      getCategories(),
                      getFirmProducts(),
                    ]);
                  },
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(), // RefreshIndicator için gerekli
                    controller: _scrollController,
                    child: Column(
                      children: [
                        BannerSection(
                          firmBanner: firmData?['banner'] ??
                              'assets/images/placeholder.png',
                        ),
                        FirmInfoSection(
                          firmName: firmData?['name'] ?? "Firma Adı Bulunamadı",
                          firmId: firmId,
                          darkModeController: darkModeController,
                        ),
                        RatingSection(
                          firmId: firmId,
                          darkModeController: darkModeController,
                        ),
                        CategoriesSection(
                          categories: sortedCategories,
                          onCategoryTap: _scrollToCategory,
                        ),
                        ProductsSection(
                          sortedCategories: sortedCategories,
                          categorizedProducts: categorizedProducts,
                          categoryKeys: _categoryKeys,
                          darkModeController: darkModeController,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ));
  }

  Map<String, List<Map<String, dynamic>>> _categorizeProducts(
      List<Map<String, dynamic>> products) {
    Map<String, List<Map<String, dynamic>>> categorizedProducts = {};

    // Sadece stockStatus'ü "Stokta Var" olan ürünleri filtrele
    var availableProducts = products
        .where((product) => product['stockStatus'] == 'Stokta Var')
        .toList();

    for (var product in availableProducts) {
      String categoryId = product['category'] ?? 'unknown';
      String categoryName = categoriesMap[categoryId] ?? 'Kategori Bilinmiyor';

      if (!categorizedProducts.containsKey(categoryName)) {
        categorizedProducts[categoryName] = [];
        _categoryKeys[categoryName] = GlobalKey();
      }
      categorizedProducts[categoryName]!.add(product);
    }

    return categorizedProducts;
  }
}
