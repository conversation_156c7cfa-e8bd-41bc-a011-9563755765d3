import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:yemekkapimda/views/restaurant/widgets/product_image.dart';
import 'package:yemekkapimda/views/restaurant/widgets/product_info.dart';

import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/size_config.dart';
import '../../../controller/dark_mode_controller.dart';

class ProductCard extends StatelessWidget {
  final Map<String, dynamic> product;
  final DarkModeController darkModeController;

  const ProductCard({
    Key? key,
    required this.product,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: SizeConfig.kHeight20,
        vertical: SizeConfig.kHeight10,
      ),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        boxShadow: [
          BoxShadow(
            color: ColorConfig.kBlackColor.withAlpha(15),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
          onTap: () {
            if (product['id'] != null && product['id']!.isNotEmpty) {
              Get.toNamed(
                AppRoutes.productDetail,
                parameters: {'productId': product['id']},
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(SizeConfig.kHeight12),
            child: Row(
              children: [
                ProductImage(imageUrl: product['images'][0]),
                const SizedBox(width: SizeConfig.kHeight16),
                Expanded(
                  child: ProductInfo(
                    name: product['name'],
                    price: product['price'],
                    darkModeController: darkModeController,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_outlined,
                  color: ColorConfig.kPrimaryColor,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
