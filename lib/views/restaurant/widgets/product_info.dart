import 'package:flutter/material.dart';

import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../controller/dark_mode_controller.dart';

class ProductInfo extends StatelessWidget {
  final String name;
  final dynamic price;
  final DarkModeController darkModeController;

  const ProductInfo({
    Key? key,
    required this.name,
    required this.price,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name,
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            fontSize: FontConfig.kFontSize16,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          "$price TL",
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            fontSize: FontConfig.kFontSize18,
            color: ColorConfig.kPrimaryColor,
          ),
        ),
      ],
    );
  }
}