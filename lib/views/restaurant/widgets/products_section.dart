import 'package:flutter/material.dart';
import 'package:yemekkapimda/views/restaurant/widgets/product_cart.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../controller/dark_mode_controller.dart';

class ProductsSection extends StatelessWidget {
  final List<String> sortedCategories;
  final Map<String, List<Map<String, dynamic>>> categorizedProducts;
  final Map<String, GlobalKey> categoryKeys;
  final DarkModeController darkModeController;

  const ProductsSection({
    Key? key,
    required this.sortedCategories,
    required this.categorizedProducts,
    required this.categoryKeys,
    required this.darkModeController,
  }) : super(key: key);

  List<Map<String, dynamic>> _sortProductsByOrder(
      List<Map<String, dynamic>> products) {
    return List<Map<String, dynamic>>.from(products)
      ..sort((a, b) {
        final orderA =
            a['order'] as int? ?? 999999; // Eğer order yoksa en sona koy
        final orderB = b['order'] as int? ?? 999999;
        return orderA.compareTo(orderB);
      });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: sortedCategories.length,
      itemBuilder: (context, index) {
        String categoryName = sortedCategories[index];
        var categoryProducts =
            _sortProductsByOrder(categorizedProducts[categoryName]!);

        return Column(
          key: categoryKeys[categoryName],
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeConfig.kHeight20,
                vertical: SizeConfig.kHeight10,
              ),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: FontFamilyConfig.urbanistSemiBold,
                  fontSize: FontConfig.kFontSize18,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: categoryProducts.length,
              itemBuilder: (context, productIndex) {
                return ProductCard(
                  product: categoryProducts[productIndex],
                  darkModeController: darkModeController,
                );
              },
            ),
          ],
        );
      },
    );
  }
}
