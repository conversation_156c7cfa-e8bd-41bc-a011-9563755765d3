import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../home/<USER>/rating_stars.dart';
import '../../../app_routes/app_routes.dart';

class RatingSection extends StatefulWidget {
  final String firmId;
  final DarkModeController darkModeController;

  const RatingSection({
    Key? key,
    required this.firmId,
    required this.darkModeController,
  }) : super(key: key);

  @override
  State<RatingSection> createState() => _RatingSectionState();
}

class _RatingSectionState extends State<RatingSection> {
  double averageRating = 0;
  int totalReviews = 0;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchReviews();
  }

  Future<void> fetchReviews() async {
    try {
      final ordersSnapshot =
          await FirebaseFirestore.instance.collection('orders').get();

      List<double> ratings = [];

      for (var orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data();
        final items = orderData['items'] as List<dynamic>? ?? [];

        for (var item in items) {
          if (item['firmId'] == widget.firmId &&
              item['review'] != null &&
              item['review']['rating'] != null) {
            ratings.add((item['review']['rating'] as num).toDouble());
          }
        }
      }

      if (ratings.isNotEmpty) {
        setState(() {
          totalReviews = ratings.length;
          averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.firmId.isNotEmpty) {
          Get.toNamed(
            AppRoutes.firmReviews, // String yerine tanımlı route'u kullanalım
            parameters: {'firmId': widget.firmId},
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight20),
        padding: const EdgeInsets.all(SizeConfig.kHeight16),
        decoration: BoxDecoration(
          color: widget.darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kDarkModeColor,
          borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
          boxShadow: [
            BoxShadow(
              color: ColorConfig.kBlackColor.withAlpha(15),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: isLoading
            ? const Center(child: CircularProgressIndicator())
            : Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: ColorConfig.kPrimaryColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        RatingStars(
                          rating: averageRating,
                          size: 18,
                          activeColor: ColorConfig.kPrimaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          averageRating.toStringAsFixed(1),
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistSemiBold,
                            fontSize: FontConfig.kFontSize16,
                            color: ColorConfig.kPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '($totalReviews değerlendirme)',
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistRegular,
                      fontSize: FontConfig.kFontSize13,
                      color: widget.darkModeController.isLightTheme.value
                          ? ColorConfig.kHintColor
                          : ColorConfig.kDividerColor.withAlpha(200),
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: ColorConfig.kPrimaryColor,
                    size: 16,
                  ),
                ],
              ),
      ),
    );
  }
}
