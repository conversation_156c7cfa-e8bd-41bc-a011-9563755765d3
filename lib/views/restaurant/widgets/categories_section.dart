import 'package:flutter/material.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';

class CategoriesSection extends StatelessWidget {
  final List<String> categories;
  final Function(String) onCategoryTap;

  const CategoriesSection({
    Key? key,
    required this.categories,
    required this.onCategoryTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: SizeConfig.kHeight20),
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 12),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => onCategoryTap(categories[index]),
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              ColorConfig.kPrimaryColor,
                              ColorConfig.kPrimaryColor.withAlpha(200),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: ColorConfig.kPrimaryColor.withAlpha(75),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          categories[index],
                          style: TextStyle(
                            color: ColorConfig.kWhiteColor,
                            fontFamily: FontFamilyConfig.urbanistMedium,
                            fontSize: FontConfig.kFontSize14,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.swipe_left,
                size: 18,
                color: ColorConfig.kHintColor.withAlpha(175),
              ),
              const SizedBox(width: 6),
              Text(
                'Kategoriler için sola kaydırın',
                style: TextStyle(
                  fontFamily: FontFamilyConfig.urbanistRegular,
                  fontSize: FontConfig.kFontSize14,
                  color: ColorConfig.kHintColor.withAlpha(175),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
