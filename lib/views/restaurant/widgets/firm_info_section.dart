import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../app_routes/app_routes.dart';
import '../../../controller/dark_mode_controller.dart';

class FirmInfoSection extends StatelessWidget {
  final String firmName;
  final String firmId;
  final DarkModeController darkModeController;

  const FirmInfoSection({
    Key? key,
    required this.firmName,
    required this.firmId,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(SizeConfig.kHeight20),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        boxShadow: [
          BoxShadow(
            color: ColorConfig.kBlackColor.withAlpha(15),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(SizeConfig.kHeight16),
        title: Text(
          firmName,
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            fontSize: FontConfig.kFontSize22,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
        trailing: Container(
          decoration: BoxDecoration(
            color: ColorConfig.kPrimaryColor.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(8),
          child: Icon(
            Icons.arrow_forward_ios_outlined,
            color: ColorConfig.kPrimaryColor,
            size: 20,
          ),
        ),
        onTap: () {
          Get.toNamed(AppRoutes.overViewScreen, parameters: {'firmId': firmId});
        },
      ),
    );
  }
}
