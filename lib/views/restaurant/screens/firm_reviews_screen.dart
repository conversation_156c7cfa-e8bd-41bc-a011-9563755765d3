import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../config/color.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../home/<USER>/rating_stars.dart';

class FirmReviewsScreen extends StatefulWidget {
  final String firmId;

  FirmReviewsScreen({
    Key? key,
    required this.firmId,
  })  : assert(firmId.isNotEmpty, 'firmId cannot be empty'),
        super(key: key);

  @override
  State<FirmReviewsScreen> createState() => _FirmReviewsScreenState();
}

class _FirmReviewsScreenState extends State<FirmReviewsScreen> {
  final DarkModeController darkModeController = Get.put(DarkModeController());
  List<Map<String, dynamic>> reviews = [];
  double averageRating = 0;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchReviews();
  }

  String _maskName(String name) {
    if (name.isEmpty || name == 'Anonim') return name;

    List<String> parts = name.split(' ');
    List<String> maskedParts = parts.map((part) {
      if (part.length <= 1) return part;
      return '${part[0]}${'*' * (part.length - 1)}';
    }).toList();

    return maskedParts.join(' ');
  }

  Future<void> fetchReviews() async {
    try {
      final ordersSnapshot =
          await FirebaseFirestore.instance.collection('orders').get();

      List<Map<String, dynamic>> fetchedReviews = [];
      List<double> ratings = [];

      for (var orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data();
        final items = orderData['items'] as List<dynamic>? ?? [];

        for (var item in items) {
          if (item['firmId'] == widget.firmId &&
              item['review'] != null &&
              item['review']['rating'] != null) {
            String maskedName = _maskName(orderData['firstName'] ?? 'Anonim');

            fetchedReviews.add({
              'rating': (item['review']['rating'] as num).toDouble(),
              'comment': item['review']['comment'] ?? '',
              'firstName': maskedName,
              'productName': item['productName'] ?? 'Ürün adı bulunamadı',
            });
            ratings.add((item['review']['rating'] as num).toDouble());
          }
        }
      }

      setState(() {
        reviews = fetchedReviews;
        if (ratings.isNotEmpty) {
          averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
        }
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorConfig.kPrimaryColor,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Müşteri Değerlendirmeleri',
          style: TextStyle(
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
            fontFamily: FontFamilyConfig.urbanistSemiBold,
          ),
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(SizeConfig.kHeight20),
                child: Column(
                  children: [
                    if (reviews.isNotEmpty) ...[
                      Container(
                        padding: const EdgeInsets.all(SizeConfig.kHeight20),
                        decoration: BoxDecoration(
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kWhiteColor
                              : ColorConfig.kDarkModeColor,
                          borderRadius:
                              BorderRadius.circular(SizeConfig.borderRadius12),
                          boxShadow: [
                            BoxShadow(
                              color: ColorConfig.kBlackColor.withAlpha(15),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Text(
                              averageRating.toStringAsFixed(1),
                              style: TextStyle(
                                fontSize: 40,
                                fontFamily: FontFamilyConfig.urbanistBold,
                                color: ColorConfig.kPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            RatingStars(
                              rating: averageRating,
                              size: 24,
                              activeColor: ColorConfig.kPrimaryColor,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${reviews.length} değerlendirme',
                              style: TextStyle(
                                color: darkModeController.isLightTheme.value
                                    ? ColorConfig.kHintColor
                                    : ColorConfig.kDividerColor,
                                fontFamily: FontFamilyConfig.urbanistMedium,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: reviews.length,
                        itemBuilder: (context, index) {
                          final review = reviews[index];
                          return Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kWhiteColor
                                  : ColorConfig.kDarkModeColor,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: ColorConfig.kBlackColor.withAlpha(15),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    RatingStars(
                                      rating: review['rating'],
                                      activeColor: ColorConfig.kPrimaryColor,
                                    ),
                                    Text(
                                      review['firstName'],
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistMedium,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kDividerColor,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  review['productName'],
                                  style: TextStyle(
                                    fontFamily:
                                        FontFamilyConfig.urbanistSemiBold,
                                    color: darkModeController.isLightTheme.value
                                        ? ColorConfig.kBlackColor
                                        : ColorConfig.kWhiteColor,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  review['comment'],
                                  style: TextStyle(
                                    color: darkModeController.isLightTheme.value
                                        ? ColorConfig.kBlackColor
                                        : ColorConfig.kWhiteColor,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ] else
                      Center(
                        child: Text(
                          'Henüz değerlendirme yapılmamış',
                          style: TextStyle(
                            color: ColorConfig.kHintColor,
                            fontFamily: FontFamilyConfig.urbanistMedium,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
    );
  }
}
