import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../controller/dark_mode_controller.dart';

class OverViewScreen extends StatelessWidget {
  OverViewScreen({Key? key, required this.firmId}) : super(key: key);

  final String firmId;
  final DarkModeController darkModeController = Get.put(DarkModeController());

  // Firmaya ait bilgileri Firebase'den çeken fonksiyon
  Future<Map<String, dynamic>?> getFirmData() async {
    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('firms')
          .doc(firmId)
          .get();
      if (doc.exists) {
        var data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id; // Document ID'yi ekliyoruz
        return data;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
          () => Scaffold(
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kBlackColor,
        appBar: AppBar(
          title: FutureBuilder(
            future: getFirmData(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              } else if (snapshot.hasError || !snapshot.hasData) {
                return const Text('Firma Adı');
              } else {
                var firmData = snapshot.data as Map<String, dynamic>;
                return Text(
                  firmData['name'] ?? 'Firma Adı Bulunamadı',
                  style: TextStyle(
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    fontSize: FontConfig.kFontSize22,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                  ),
                );
              }
            },
          ),
        ),
        body: FutureBuilder(
          future: getFirmData(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return const Center(child: Text("Veriler alınırken hata oluştu."));
            } else if (!snapshot.hasData) {
              return const Center(child: Text("Firma bilgisi bulunamadı."));
            } else {
              var firmData = snapshot.data as Map<String, dynamic>;
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Firma banner
                      SizedBox(
                        height: 200,
                        width: double.infinity,
                        child: Image.network(
                          firmData['banner'] ??
                              'assets/images/placeholder.png',
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Firma adı
                      Text(
                        firmData['name'] ?? 'Firma Adı Bulunamadı',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Firma açıklaması
                      Text(
                        firmData['description'] ?? 'Açıklama yok',
                        style: TextStyle(
                          fontSize: 16,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
