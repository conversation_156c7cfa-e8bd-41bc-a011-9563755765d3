import 'package:flutter/material.dart';
import 'package:yemekkapimda/views/order_view/widgets/review_dialog.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';

class OrderItemWidget extends StatelessWidget {
  final String orderId;
  final Map<String, dynamic> item;
  final bool isDarkMode;
  final bool showReview;

  const OrderItemWidget({
    Key? key,
    required this.orderId,
    required this.item,
    required this.isDarkMode,
    this.showReview = true,
  }) : super(key: key);

  void _showReviewDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ReviewDialog(
        orderId: orderId,
        item: item,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final review = item['review'] as Map<String, dynamic>?;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ColorConfig.kDarkModeColor.withAlpha(125)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
        ),
      ),
      child: Column(
        children: [
          // Ürün Bilgileri
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item['productName'] ?? '',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistMedium,
                              fontSize: FontConfig.kFontSize16,
                              fontWeight: FontWeight.w600,
                              color: isDarkMode
                                  ? ColorConfig.kWhiteColor
                                  : ColorConfig.kBlackColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          if (item['selectedFeatures'] != null) ...[
                            const SizedBox(height: 4),
                            ...(item['selectedFeatures'] as List<dynamic>)
                                .map((feature) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 2),
                                child: Text(
                                  '${feature['name']}: ${feature['price']} TL',
                                  style: TextStyle(
                                    fontFamily:
                                        FontFamilyConfig.urbanistRegular,
                                    fontSize: FontConfig.kFontSize12,
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                  ),
                                ),
                              );
                            }).toList(),
                          ],
                          const SizedBox(height: 4),
                          Text(
                            '${item['price']} TL',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistRegular,
                              fontSize: FontConfig.kFontSize14,
                              color: ColorConfig.kPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: ColorConfig.kPrimaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${item['quantity'] ?? 1}x',
                        style: TextStyle(
                          color: ColorConfig.kPrimaryColor,
                          fontWeight: FontWeight.bold,
                          fontSize: FontConfig.kFontSize14,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Değerlendirme Bölümü
          if (review != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[850] : Colors.grey[50],
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Değerlendirmeniz',
                        style: TextStyle(
                          fontFamily: FontFamilyConfig.urbanistMedium,
                          fontSize: FontConfig.kFontSize14,
                          color: isDarkMode
                              ? ColorConfig.kWhiteColor
                              : ColorConfig.kBlackColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: List.generate(
                      5,
                      (index) => Icon(
                        index < (review['rating'] as num).toInt()
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 24,
                      ),
                    ),
                  ),
                  if (review['comment'] != null &&
                      review['comment'].toString().isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      review['comment'].toString(),
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistRegular,
                        fontSize: FontConfig.kFontSize14,
                        color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ] else if (showReview)
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: () => _showReviewDialog(context),
                  icon: const Icon(Icons.star_outline),
                  label: const Text('Değerlendirme Yap'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConfig.kPrimaryColor,
                    foregroundColor: Colors.white,
                    textStyle: const TextStyle(
                      fontFamily: FontFamilyConfig.urbanistMedium,
                      fontSize: FontConfig.kFontSize16,
                      fontWeight: FontWeight.w600,
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
