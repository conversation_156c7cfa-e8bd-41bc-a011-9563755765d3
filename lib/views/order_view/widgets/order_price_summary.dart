import 'package:flutter/material.dart';

import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';

class OrderPriceSummary extends StatelessWidget {
  final Map<String, dynamic> orderData;
  final bool isDarkMode;

  const OrderPriceSummary({
    Key? key,
    required this.orderData,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPriceLabel('Ara Toplam:', isDarkMode),
            if (orderData['discountApplied'] != null &&
                orderData['discountApplied'] > 0)
              _buildPriceLabel('İndirim:', isDarkMode),
            _buildTotalLabel('Toplam:', isDarkMode),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildPriceAmount('${orderData['totalPrice'] ?? 0} TL', isDarkMode),
            if (orderData['discountApplied'] != null &&
                orderData['discountApplied'] > 0)
              Text(
                '-${orderData['discountApplied']} TL',
                style: const TextStyle(
                  fontFamily: FontFamilyConfig.urbanistRegular,
                  fontSize: FontConfig.kFontSize14,
                  color: Colors.red,
                ),
              ),
            Text(
              '${orderData['finalPrice'] ?? 0} TL',
              style: TextStyle(
                fontFamily: FontFamilyConfig.urbanistMedium,
                fontSize: FontConfig.kFontSize16,
                fontWeight: FontWeight.w600,
                color: ColorConfig.kPrimaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriceLabel(String text, bool isDarkMode) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: FontFamilyConfig.urbanistRegular,
        fontSize: FontConfig.kFontSize14,
        color: isDarkMode
            ? ColorConfig.kWhiteColor.withAlpha(175)
            : ColorConfig.kBlackColor.withAlpha(175),
      ),
    );
  }

  Widget _buildTotalLabel(String text, bool isDarkMode) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: FontFamilyConfig.urbanistMedium,
        fontSize: FontConfig.kFontSize16,
        fontWeight: FontWeight.w600,
        color: isDarkMode ? ColorConfig.kWhiteColor : ColorConfig.kBlackColor,
      ),
    );
  }

  Widget _buildPriceAmount(String amount, bool isDarkMode) {
    return Text(
      amount,
      style: TextStyle(
        fontFamily: FontFamilyConfig.urbanistRegular,
        fontSize: FontConfig.kFontSize14,
        color: isDarkMode
            ? ColorConfig.kWhiteColor.withAlpha(175)
            : ColorConfig.kBlackColor.withAlpha(175),
      ),
    );
  }
}
