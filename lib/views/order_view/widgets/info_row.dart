import 'package:flutter/cupertino.dart';

import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final bool isDarkMode;

  const InfoRow({
    Key? key,
    required this.label,
    required this.value,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistRegular,
              fontSize: FontConfig.kFontSize14,
              color: isDarkMode
                  ? ColorConfig.kWhiteColor.withAlpha(175)
                  : ColorConfig.kBlackColor.withAlpha(175),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistMedium,
              fontSize: FontConfig.kFontSize14,
              color: isDarkMode
                  ? ColorConfig.kWhiteColor
                  : ColorConfig.kBlackColor,
            ),
          ),
        ],
      ),
    );
  }
}