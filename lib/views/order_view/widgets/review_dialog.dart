import 'package:flutter/material.dart';
import 'package:yemekkapimda/views/order_view/widgets/review_service.dart';

class ReviewDialog extends StatefulWidget {
  final String orderId;
  final Map<String, dynamic> item;

  const ReviewDialog({
    Key? key,
    required this.orderId,
    required this.item,
  }) : super(key: key);

  @override
  State<ReviewDialog> createState() => _ReviewDialogState();
}

class _ReviewDialogState extends State<ReviewDialog> {
  final ReviewService _reviewService = ReviewService();
  final TextEditingController _commentController = TextEditingController();
  int _rating = 0;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${widget.item['productName']} iç<PERSON>',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Yıldız derecelendirme
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                5,
                (index) => IconButton(
                  onPressed: () => setState(() => _rating = index + 1),
                  icon: Icon(
                    index < _rating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 30,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Yorum alanı
            TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                hintText: 'Yorumunuzu yazın...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            // Gönder butonu
            ElevatedButton(
              onPressed: () => _handleSubmit(context),
              child: const Text('Değerlendirmeyi Gönder'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSubmit(BuildContext dialogContext) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(dialogContext);

    if (_rating == 0) {
      if (!mounted) return;
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('Lütfen yıldız değerlendirmesi yapın')),
      );
      return;
    }

    try {
      await _reviewService.addReviewToItem(
        orderId: widget.orderId,
        itemId: widget.item['id'],
        rating: _rating,
        comment: _commentController.text.trim(),
      );
      
      if (!mounted) return;
      navigator.pop();
    } catch (e) {
      if (!mounted) return;
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text('Hata: $e')),
      );
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
}