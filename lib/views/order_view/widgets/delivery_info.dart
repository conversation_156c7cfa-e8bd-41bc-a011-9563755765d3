import 'package:flutter/material.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import 'info_row.dart';

class DeliveryInfoWidget extends StatelessWidget {
  final Map<String, dynamic> orderData;
  final bool isDarkMode;

  const DeliveryInfoWidget({
    Key? key,
    required this.orderData,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final address = orderData['addresses'] != null
        ? (orderData['addresses'] as List).firstWhere(
            (addr) => addr['id'] == orderData['deliverTo'],
            orElse: () => {},
          )
        : {};

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ColorConfig.kDarkModeColor.withAlpha(75)
            : ColorConfig.kFillColor.withAlpha(75),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Teslimat Bilgileri',
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistMedium,
              fontSize: FontConfig.kFontSize14,
              fontWeight: FontWeight.w600,
              color: isDarkMode
                  ? ColorConfig.kWhiteColor
                  : ColorConfig.kBlackColor,
            ),
          ),
          const SizedBox(height: 8),
          if (address.isNotEmpty) ...[
            InfoRow(
              label: 'Adres:',
              value: address['sokak'] ?? '',
              isDarkMode: isDarkMode,
            ),
            InfoRow(
              label: 'Mahalle:',
              value: address['mahalle'] ?? '',
              isDarkMode: isDarkMode,
            ),
            if (address['bina_ve_daire'] != null)
              InfoRow(
                label: 'Bina/Daire:',
                value: address['bina_ve_daire'],
                isDarkMode: isDarkMode,
              ),
            if (address['aciklama'] != null && address['aciklama'].isNotEmpty)
              InfoRow(
                label: 'Not:',
                value: address['aciklama'],
                isDarkMode: isDarkMode,
              ),
          ],
          if (orderData['paymentMethod'] != null)
            InfoRow(
              label: 'Ödeme Yöntemi:',
              value: orderData['paymentMethod'],
              isDarkMode: isDarkMode,
            ),
          if (orderData['instructions'] != null &&
              orderData['instructions'].isNotEmpty)
            InfoRow(
              label: 'Sipariş Notu:',
              value: orderData['instructions'],
              isDarkMode: isDarkMode,
            ),
        ],
      ),
    );
  }
}
