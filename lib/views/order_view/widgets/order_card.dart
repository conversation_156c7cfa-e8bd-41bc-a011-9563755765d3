import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import '../../../config/color.dart';
import '../../../config/size_config.dart';
import 'delivery_info.dart';
import 'order_item_widget.dart';
import 'order_price_summary.dart';

class OrderCard extends StatelessWidget {
  final QueryDocumentSnapshot orderDoc;
  final Map<String, dynamic> orderData;
  final bool isDarkMode;

  const OrderCard({
    Key? key,
    required this.orderDoc,
    required this.orderData,
    required this.isDarkMode,
  }) : super(key: key);

  String _formatDate(dynamic orderDate) {
    if (orderDate is Timestamp) {
      final date = orderDate.toDate();
      return '${date.day}.${date.month}.${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    final items = (orderData['items'] as List<dynamic>?) ?? [];
    final isDelivered = orderData['status'] == 'Teslim Edildi';
    final orderDate = orderData['orderDate'];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        color:
            isDarkMode ? ColorConfig.kDarkModeColor : ColorConfig.kWhiteColor,
        boxShadow: [
          BoxShadow(
            color: ColorConfig.kBlackColor.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      margin: const EdgeInsets.only(bottom: SizeConfig.kHeight15),
      padding: const EdgeInsets.all(SizeConfig.kHeight15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.black26 : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Sipariş No: ${orderDoc.id}',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? ColorConfig.kWhiteColor
                    : ColorConfig.kBlackColor,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDate(orderDate),
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(orderData['status']),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  orderData['status'] ?? '',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (items.isNotEmpty) ...[
            const Divider(height: 20),
            ...items
                .map((item) => OrderItemWidget(
                      orderId: orderDoc.id,
                      item: item,
                      isDarkMode: isDarkMode,
                      showReview: isDelivered,
                    ))
                .toList(),
          ],
          DeliveryInfoWidget(orderData: orderData, isDarkMode: isDarkMode),
          const Divider(height: 20),
          OrderPriceSummary(orderData: orderData, isDarkMode: isDarkMode),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'Sipariş Alındı':
        return Colors.blue;
      case 'Siparişler Hazırlanıyor':
        return Colors.orange;
      case 'Paketler Hazır':
        return Colors.purple;
      case 'Yola Çıktı':
        return Colors.indigo;
      case 'Teslim Edildi':
        return Colors.green;
      case 'İptal':
        return Colors.red;
      case 'İade':
        return Colors.red[700]!;
      default:
        return Colors.grey;
    }
  }
}
