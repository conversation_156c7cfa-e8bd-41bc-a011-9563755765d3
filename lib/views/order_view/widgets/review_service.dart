import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class ReviewService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> addReviewToItem({
    required String orderId,
    required String itemId,
    required int rating,
    required String comment,
  }) async {
    WriteBatch batch = _firestore.batch();

    final reviewData = {
      'rating': rating,
      'comment': comment,
      'createdAt': Timestamp.now(),
    };

    try {
      final orderRef = _firestore.collection('orders').doc(orderId);
      final orderDoc = await orderRef.get();

      if (orderDoc.exists) {
        final orderData = orderDoc.data() as Map<String, dynamic>;
        final items = List<Map<String, dynamic>>.from(orderData['items'] ?? []);

        final itemIndex = items.indexWhere((item) => item['id'] == itemId);
        if (itemIndex != -1) {
          items[itemIndex]['review'] = reviewData;
          batch.update(orderRef, {'items': items});
        }

        if (orderData['userId'] != null) {
          final userOrderRef = _firestore
              .collection('users')
              .doc(orderData['userId'])
              .collection('orders')
              .doc(orderId);
          batch.update(userOrderRef, {'items': items});
        }

        if (orderData['firmId'] != null) {
          final firmOrderRef = _firestore
              .collection('firms')
              .doc(orderData['firmId'])
              .collection('orders')
              .doc(orderId);
          batch.update(firmOrderRef, {'items': items});
        }
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error adding review: $e');
      rethrow;
    }
  }
}