import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/home_controller.dart';
import 'widgets/order_card.dart';

class CancelTabView extends StatelessWidget {
  final HomeController cancelController = Get.put(HomeController());
  final DarkModeController darkModeController = Get.put(DarkModeController());

  CancelTabView({super.key});

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final isDarkMode = !darkModeController.isLightTheme.value;

    // Kullanıcı girişi kontrolü
    if (user == null) {
      return Center(
        child: Text(
          "Siparişlerinizi görmek için lütfen giriş yapın.",
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: isDarkMode ? ColorConfig.kWhiteColor : ColorConfig.kBlackColor,
            fontWeight: FontWeight.w600,
            fontSize: FontConfig.kFontSize16,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(
        left: SizeConfig.kHeight20,
        right: SizeConfig.kHeight20,
      ),
      child: Column(
        children: [
          const SizedBox(
            height: SizeConfig.kHeight20,
          ),
          Expanded(
            child: StreamBuilder(
              stream: FirebaseFirestore.instance
                  .collection('orders')
                  .where('userId', isEqualTo: FirebaseAuth.instance.currentUser?.uid)
                  .where('status', whereIn: ['İptal', 'İade'])
                  .snapshots(),
              builder: (context, AsyncSnapshot<QuerySnapshot> snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.data!.docs.isEmpty) {
                  return Center(
                    child: Text(
                      "İptal edilen veya iade edilen sipariş bulunmamaktadır.",
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        color: isDarkMode ? ColorConfig.kWhiteColor : ColorConfig.kBlackColor,
                        fontWeight: FontWeight.w600,
                        fontSize: FontConfig.kFontSize16,
                      ),
                    ),
                  );
                }

                final orders = snapshot.data!.docs;
                orders.sort((a, b) {
                  final aDate = (a.data() as Map<String, dynamic>)['orderDate'] as Timestamp;
                  final bDate = (b.data() as Map<String, dynamic>)['orderDate'] as Timestamp;
                  return bDate.compareTo(aDate);
                });

                return RefreshIndicator(
                  color: ColorConfig.kPrimaryColor,
                  onRefresh: () async {
                    // StreamBuilder otomatik olarak yeni verileri alacağı için
                    // sadece kısa bir bekleme süresi ekleyeceğiz
                    await Future.delayed(const Duration(milliseconds: 500));
                  },
                  child: ListView.builder(
                    physics: const AlwaysScrollableScrollPhysics(), // RefreshIndicator için gerekli
                    itemCount: orders.length,
                    itemBuilder: (context, index) {
                      var orderDoc = orders[index];
                      var orderData = orderDoc.data() as Map<String, dynamic>;
                      orderData['id'] = orderDoc.id;

                      return OrderCard(
                        orderDoc: orderDoc,
                        orderData: orderData,
                        isDarkMode: isDarkMode,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}