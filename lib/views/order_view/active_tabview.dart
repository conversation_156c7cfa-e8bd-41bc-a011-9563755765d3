import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'widgets/order_card.dart';

class ActiveTabView extends StatelessWidget {
  ActiveTabView({Key? key}) : super(key: key);

  final activeStatuses = [
    'Sipar<PERSON>ş Alındı',
    'Siparişler Hazırlanıyor',
    '<PERSON><PERSON><PERSON> Hazır',
    '<PERSON><PERSON>ı<PERSON>'
  ];

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return const Center(child: Text('Lütfen giriş yapın.'));
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return StreamBuilder(
      stream: FirebaseFirestore.instance
          .collection('orders')
          .where('userId', isEqualTo: user.uid)
          .snapshots(),
      builder: (context, AsyncSnapshot<QuerySnapshot> snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        // Client-side filtering and sorting
        final orders = snapshot.data!.docs.where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return activeStatuses.contains(data['status']);
        }).toList()
          ..sort((a, b) {
            final aDate =
                (a.data() as Map<String, dynamic>)['orderDate'] as Timestamp;
            final bDate =
                (b.data() as Map<String, dynamic>)['orderDate'] as Timestamp;
            return bDate.compareTo(aDate);
          });

        if (orders.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'Aktif siparişiniz bulunmamaktadır',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          color: Theme.of(context).primaryColor,
          onRefresh: () async {
            // StreamBuilder otomatik olarak yeni verileri alacağı için
            // sadece kısa bir bekleme süresi ekleyeceğiz
            await Future.delayed(const Duration(milliseconds: 500));
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            physics:
                const AlwaysScrollableScrollPhysics(), // RefreshIndicator için gerekli
            itemCount: orders.length,
            itemBuilder: (context, index) {
              var orderDoc = orders[index];
              var orderData = orderDoc.data() as Map<String, dynamic>;
              orderData['id'] = orderDoc.id;

              return OrderCard(
                orderDoc: orderDoc,
                orderData: orderData,
                isDarkMode: isDarkMode,
              );
            },
          ),
        );
      },
    );
  }
}
