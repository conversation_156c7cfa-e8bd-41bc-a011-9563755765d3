// ignore_for_file: must_be_immutable

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:yemekkapimda/config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/image_path.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../../utils/appbar_common.dart';
import '../../app_routes/app_routes.dart';
import '../../utils/common_material_button.dart';
import '../../services/event_bus_service.dart';

class AddressScreen extends StatefulWidget {
  const AddressScreen({Key? key}) : super(key: key);

  @override
  State<AddressScreen> createState() => AddressScreenState();
}

class AddressScreenState extends State<AddressScreen>
    with WidgetsBindingObserver {
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final RxList<Map<String, dynamic>> addresses = <Map<String, dynamic>>[].obs;

  late StreamSubscription _addressUpdateSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadAddresses();

    // Event dinleyicisini ekle
    final eventBus = Get.put(EventBusService());
    _addressUpdateSubscription = eventBus.addressesUpdated.listen((_) {
      _loadAddresses();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Sayfa geri döndüğünde adresleri tekrar yükle
      _loadAddresses();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _addressUpdateSubscription.cancel(); // Event dinleyicisini temizle
    super.dispose();
  }

  // Firestore'dan adresleri çekme ve reactive listeye ekleme
  Future<void> _loadAddresses() async {
    User? user = auth.currentUser;
    if (user != null) {
      DocumentSnapshot userDoc =
          await firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists && userDoc.data() != null) {
        var data = userDoc.data() as Map<String, dynamic>;
        List addressesData = data['addresses'] ?? [];
        addresses.assignAll(List<Map<String, dynamic>>.from(addressesData));
      }
    }
  }

  // Adresi Firestore'dan silme işlemi
  Future<void> _deleteAddress(String addressId) async {
    User? user = auth.currentUser;
    if (user != null) {
      DocumentSnapshot userDoc =
          await firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        var userData = userDoc.data() as Map<String, dynamic>;
        List<dynamic> addressesData = userData['addresses'] ?? [];

        // Silinecek adresi listeden çıkarıyoruz
        addressesData.removeWhere((address) => address['id'] == addressId);

        // Firestore'a güncellenen adres listesini kaydediyoruz
        await firestore.collection('users').doc(user.uid).update({
          'addresses': addressesData,
        });

        // Yerel listeyi güncelle
        addresses.removeWhere((address) => address['id'] == addressId);
        Get.snackbar("Başarılı", "Adres silindi.");
      }
    }
  }

  // Silme işlemi onayı için uyarı dialogu
  Future<void> _showDeleteConfirmationDialog(
      BuildContext context, String addressId) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Adresi Sil'),
          content: const Text('Bu adresi silmek istediğinize emin misiniz?'),
          actions: <Widget>[
            TextButton(
              child: const Text('İptal'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Sil'),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteAddress(
                    addressId); // Adres silme fonksiyonunu çağırıyoruz
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kBlackColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
            child: CommonAppBar(
              title: StringConfig.addressProfile,
              leadingImage: ImagePath.arrow,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
              leadingOnTap: () {
                Get.back();
              },
            )),
        body: Obx(
          () {
            if (addresses.isEmpty) {
              return const Center(child: Text("Adres bulunamadı"));
            }

            return Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeConfig.kHeight20,
                vertical: SizeConfig.kHeight10,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Adresleri listeleme
                  Expanded(
                    child: ListView.builder(
                      itemCount: addresses.length,
                      itemBuilder: (context, index) {
                        Map<String, dynamic> address = addresses[index];

                        return Padding(
                          padding: const EdgeInsets.only(
                              bottom: SizeConfig.kHeight10),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(SizeConfig.kHeight12),
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kWhiteColor
                                  : ColorConfig.kBlackColor,
                              boxShadow: [
                                BoxShadow(
                                  color: darkModeController.isLightTheme.value
                                      ? Colors.grey
                                          .withAlpha(50)
                                      : Colors.grey
                                          .withAlpha(25),
                                  blurRadius: 10,
                                  spreadRadius: SizeConfig.kHeight2,
                                  offset: const Offset(0.0, 0.0),
                                )
                              ],
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.all(SizeConfig.kHeight12),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    ImagePath.fillLocation,
                                    height: SizeConfig.kHeight25,
                                    width: SizeConfig.kHeight25,
                                  ),
                                  const SizedBox(width: SizeConfig.kHeight10),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        address['mahalle'] ??
                                            'Bilinmeyen Mahalle',
                                        style: TextStyle(
                                          fontFamily:
                                              FontFamilyConfig.urbanistSemiBold,
                                          color: darkModeController
                                                  .isLightTheme.value
                                              ? ColorConfig.kBlackColor
                                              : ColorConfig.kWhiteColor,
                                          fontWeight: FontWeight.w600,
                                          fontSize: FontConfig.kFontSize17,
                                        ),
                                      ),
                                      const SizedBox(
                                          height: SizeConfig.kHeight5),
                                      Text(
                                        address['sokak'] ?? 'Bilinmeyen Sokak',
                                        style: TextStyle(
                                          fontFamily:
                                              FontFamilyConfig.urbanistRegular,
                                          color: darkModeController
                                                  .isLightTheme.value
                                              ? ColorConfig.kHintColor
                                              : ColorConfig
                                                  .kDarkModeDividerColor,
                                          fontWeight: FontWeight.w400,
                                          fontSize: FontConfig.kFontSize14,
                                        ),
                                      ),
                                      Text(
                                        address['bina_ve_daire'] ??
                                            'Bilinmeyen Bina/Daire',
                                        style: TextStyle(
                                          fontFamily:
                                              FontFamilyConfig.urbanistRegular,
                                          color: darkModeController
                                                  .isLightTheme.value
                                              ? ColorConfig.kHintColor
                                              : ColorConfig
                                                  .kDarkModeDividerColor,
                                          fontWeight: FontWeight.w400,
                                          fontSize: FontConfig.kFontSize14,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Spacer(),
                                  // Düzenleme ve Silme İkonları
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Düzenleme İkonu
                                      IconButton(
                                        constraints: BoxConstraints.tight(
                                            const Size(40, 40)),
                                        padding: EdgeInsets.zero,
                                        onPressed: () {
                                          Get.toNamed(
                                            AppRoutes.editAddressView,
                                            arguments: address,
                                          );
                                        },
                                        icon: Image.asset(
                                          ImagePath.pen2,
                                          height: SizeConfig.kHeight20,
                                          width: SizeConfig.kHeight20,
                                        ),
                                      ),
                                      // Silme İkonu
                                      IconButton(
                                        constraints: BoxConstraints.tight(
                                            const Size(40, 40)),
                                        padding: EdgeInsets.zero,
                                        onPressed: () {
                                          _showDeleteConfirmationDialog(
                                            context,
                                            address['id'],
                                          );
                                        },
                                        icon: Image.asset(
                                          ImagePath.minusLogo,
                                          height: SizeConfig.kHeight20,
                                          width: SizeConfig.kHeight20,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kBlackColor,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha(25),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          padding: const EdgeInsets.only(
            left: SizeConfig.kHeight16,
            right: SizeConfig.kHeight16,
            bottom: SizeConfig.kHeight24,
            top: SizeConfig.kHeight16,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CommonMaterialButton(
                buttonColor: ColorConfig.kPrimaryColor,
                height: SizeConfig.kHeight52,
                txtColor: ColorConfig.kWhiteColor,
                buttonText: "Yeni Adres Ekle",
                onButtonClick: () {
                  Get.toNamed(AppRoutes.addNewAddressForm);
                },
                width: double.infinity,
              ),
              const SizedBox(height: SizeConfig.kHeight12),
              CommonMaterialButton(
                buttonColor: darkModeController.isLightTheme.value
                    ? ColorConfig.kWhiteColor
                    : ColorConfig.kDarkModeColor,
                height: SizeConfig.kHeight52,
                txtColor: ColorConfig.kPrimaryColor,
                buttonText: "Varsayılan Adres Seç",
                onButtonClick: () {
                  if (addresses.isNotEmpty) {
                    Get.toNamed(AppRoutes.deliverToScreen);
                  } else {
                    Get.snackbar(
                      "Uyarı",
                      "Önce bir adres eklemelisiniz",
                      backgroundColor: Colors.orange,
                      colorText: Colors.white,
                    );
                  }
                },
                width: double.infinity,
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(SizeConfig.borderRadius40),
                  side: BorderSide(
                    color: ColorConfig.kPrimaryColor,
                    width: 1,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
