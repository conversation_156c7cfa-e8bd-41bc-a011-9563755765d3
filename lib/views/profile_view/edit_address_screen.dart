// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:yemekkapimda/config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../../utils/appbar_common.dart';
import '../../../utils/common_material_button.dart';
import '../../config/image_path.dart';
import '../../controller/edit_address_controller.dart';
import '../../app_routes/app_routes.dart';
import 'package:flutter_map/flutter_map.dart' as flutter_map;
import 'package:latlong2/latlong.dart' as lat_lng;

class EditNewAddressFormScreen extends StatefulWidget {
  const EditNewAddressFormScreen({Key? key}) : super(key: key);

  @override
  State<EditNewAddressFormScreen> createState() =>
      _EditNewAddressFormScreenState();
}

class _EditNewAddressFormScreenState extends State<EditNewAddressFormScreen> {
  final EditAddressController editAddressController =
      Get.put(EditAddressController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  flutter_map.MapController mapController = flutter_map.MapController();
  lat_lng.LatLng? selectedLocation;
  List<flutter_map.Marker> markers = [];
  static const lat_lng.LatLng seydisehirLocation =
      lat_lng.LatLng(37.4187, 31.8489);

  @override
  void initState() {
    super.initState();
    _loadAddressData().then((_) {
      if (selectedLocation == null) {
        _requestLocationPermission();
      }
    });
    _setupAddressListener();
  }

  Future<void> _requestLocationPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        "Hata",
        "Lütfen konum servisini açın",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        Get.snackbar(
          "Hata",
          "Konum izni reddedildi",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      Get.snackbar(
        "Hata",
        "Konum izni kalıcı olarak reddedildi. Ayarlardan izin vermeniz gerekiyor.",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    try {
      _showLoadingDialog();

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 15),
        forceAndroidLocationManager: true,
      );

      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (mounted) {
        final newLocation =
            lat_lng.LatLng(position.latitude, position.longitude);
        setState(() {
          selectedLocation = newLocation;
          _updateMarkers(newLocation);
        });
        mapController.move(newLocation, 15);
      }
    } catch (e) {
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Eğer yüksek hassasiyetli konum alınamadıysa, son bilinen konumu deneyelim
      try {
        final lastKnownPosition = await Geolocator.getLastKnownPosition();
        if (lastKnownPosition != null && mounted) {
          final newLocation = lat_lng.LatLng(
              lastKnownPosition.latitude, lastKnownPosition.longitude);
          setState(() {
            selectedLocation = newLocation;
            _updateMarkers(newLocation);
          });
          mapController.move(newLocation, 15);
          return;
        }
      } catch (_) {
        // Son bilinen konum da alınamazsa, varsayılan konumu kullan
      }

      // Hiçbir konum alınamadıysa varsayılan konumu kullan
      if (mounted && selectedLocation == null) {
        setState(() {
          selectedLocation = seydisehirLocation;
          _updateMarkers(seydisehirLocation);
        });
        mapController.move(seydisehirLocation, 15);

        Get.snackbar(
          "Uyarı",
          "Konum alınamadı, haritadan manuel seçim yapabilirsiniz.",
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  Future<void> _loadAddressData() async {
    try {
      User? user = auth.currentUser;
      if (user != null) {
        DocumentSnapshot userDoc =
            await firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          var userData = userDoc.data() as Map<String, dynamic>;
          List<dynamic> addresses = userData['addresses'] ?? [];

          var address = addresses.firstWhere(
            (element) =>
                element['id'].toString() == Get.arguments['id'].toString(),
            orElse: () => null,
          );

          if (address != null) {
            editAddressController.streetController.text =
                address['mahalle'] ?? '';
            editAddressController.street2Controller.text =
                address['sokak'] ?? '';
            editAddressController.landmarkController.text =
                address['bina_ve_daire'] ?? '';
            editAddressController.descriptionController.text =
                address['aciklama'] ?? '';

            if (mounted) {
              setState(() {
                selectedLocation = lat_lng.LatLng(
                  double.parse(address['latitude'].toString()),
                  double.parse(address['longitude'].toString()),
                );

                mapController.move(selectedLocation!, 15.0);
                _updateMarkers(selectedLocation!);
              });
            }
          } else {
            Get.snackbar(
              "Hata",
              "Adres bulunamadı",
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
            Get.back();
          }
        }
      }
    } catch (e) {
      Get.snackbar(
        "Hata",
        "Adres bilgileri yüklenemedi",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    }
  }

  void _updateMarkers(lat_lng.LatLng position) {
    setState(() {
      markers = [
        flutter_map.Marker(
          point: position,
          child: Icon(
            Icons.location_pin,
            color: ColorConfig.kPrimaryColor,
            size: 40,
          ),
        ),
      ];
      selectedLocation = position;
    });
  }

  Future<void> _saveAddressData() async {
    if (_formKey.currentState!.validate()) {
      try {
        User? user = auth.currentUser;
        if (user != null) {
          DocumentSnapshot userDoc =
              await firestore.collection('users').doc(user.uid).get();
          var userData = userDoc.data() as Map<String, dynamic>;
          List<dynamic> addresses = userData['addresses'] ?? [];

          int addressIndex = addresses.indexWhere((element) =>
              element['id'].toString() == Get.arguments['id'].toString());

          if (addressIndex != -1) {
            addresses[addressIndex] = {
              'id': Get.arguments['id'],
              'mahalle': editAddressController.streetController.text.trim(),
              'sokak': editAddressController.street2Controller.text.trim(),
              'bina_ve_daire':
                  editAddressController.landmarkController.text.trim(),
              'aciklama':
                  editAddressController.descriptionController.text.trim(),
              'latitude':
                  selectedLocation?.latitude ?? seydisehirLocation.latitude,
              'longitude':
                  selectedLocation?.longitude ?? seydisehirLocation.longitude,
              'updatedAt': Timestamp.now(),
            };

            await firestore.collection('users').doc(user.uid).update({
              'addresses': addresses,
            });

            Get.snackbar(
              "Başarılı",
              "Adres bilgileri güncellendi",
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
            Get.offNamed(AppRoutes.address);
          } else {
            Get.snackbar(
              "Hata",
              "Güncellenecek adres bulunamadı",
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        }
      } catch (e) {
        Get.snackbar(
          "Hata",
          "Adres güncellenirken bir hata oluştu",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  void _setupAddressListener() {
    User? user = auth.currentUser;
    if (user != null) {
      firestore
          .collection('users')
          .doc(user.uid)
          .snapshots()
          .listen((snapshot) {
        if (snapshot.exists) {
          try {
            var userData = snapshot.data() as Map<String, dynamic>;
            List<dynamic> addresses = userData['addresses'] ?? [];

            var address = addresses.firstWhere(
              (element) =>
                  element['id'].toString() == Get.arguments['id'].toString(),
              orElse: () => null,
            );

            if (address != null) {
              var newLocation = lat_lng.LatLng(
                double.parse(address['latitude'].toString()),
                double.parse(address['longitude'].toString()),
              );

              if (selectedLocation == null ||
                  newLocation.latitude != selectedLocation!.latitude ||
                  newLocation.longitude != selectedLocation!.longitude) {
                setState(() {
                  selectedLocation = newLocation;
                  mapController.move(newLocation, 15.0);
                  _updateMarkers(newLocation);
                });
              }
            }
          } catch (e) {
            if (e is FormatException) {
              Get.snackbar(
                "Uyarı",
                "Konum bilgileri geçersiz format",
                backgroundColor: Colors.orange,
                colorText: Colors.white,
              );
            }
          }
        }
      });
    }
  }

  void _showLocationDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20),
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Konum Seçimi',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize20,
                  fontFamily: FontFamilyConfig.urbanistBold,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ColorConfig.kPrimaryColor.withAlpha(25),
                ),
                child: Icon(
                  Icons.location_on,
                  size: 40,
                  color: ColorConfig.kPrimaryColor,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Lütfen doğru teslimat için\ngerçek konumunuzu seçin',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontFamily: FontFamilyConfig.urbanistMedium,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor.withAlpha(165)
                      : ColorConfig.kWhiteColor.withAlpha(165),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                        Get.snackbar(
                          "Bilgi",
                          "Lütfen haritadan konumunuzu seçin",
                          backgroundColor: Colors.blue,
                          colorText: Colors.white,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: ColorConfig.kPrimaryColor,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Text(
                        'Haritadan Seç',
                        style: TextStyle(
                          color: ColorConfig.kPrimaryColor,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          fontSize: FontConfig.kFontSize16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                        _requestLocationPermission();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConfig.kPrimaryColor,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Konumumu Bul',
                        style: TextStyle(
                          color: ColorConfig.kWhiteColor,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          fontSize: FontConfig.kFontSize16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  void _showLoadingDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: ColorConfig.kPrimaryColor,
              ),
              const SizedBox(height: 20),
              Text(
                'Konumunuz Bulunuyor...',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontFamily: FontFamilyConfig.urbanistMedium,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: StringConfig.editAddress,
          leadingImage: ImagePath.arrow,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(SizeConfig.padding20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  controller: editAddressController.streetController,
                  validator: (value) =>
                      value?.isEmpty ?? true ? 'Bu alan boş bırakılamaz' : null,
                  decoration: _buildInputDecoration("Mahalle"),
                ),
                const SizedBox(height: SizeConfig.kHeight24),
                TextFormField(
                  controller: editAddressController.street2Controller,
                  validator: (value) =>
                      value?.isEmpty ?? true ? 'Bu alan boş bırakılamaz' : null,
                  decoration: _buildInputDecoration("Sokak"),
                ),
                const SizedBox(height: SizeConfig.kHeight24),
                TextFormField(
                  controller: editAddressController.landmarkController,
                  validator: (value) =>
                      value?.isEmpty ?? true ? 'Bu alan boş bırakılamaz' : null,
                  decoration: _buildInputDecoration("Bina ve Daire Bilgisi"),
                ),
                const SizedBox(height: SizeConfig.kHeight24),
                TextFormField(
                  controller: editAddressController.descriptionController,
                  decoration: _buildInputDecoration("Açıklama"),
                ),
                const SizedBox(height: SizeConfig.kHeight24),
                Text(
                  "Konumu Haritada İşaretleyin",
                  style: TextStyle(
                    fontSize: FontConfig.kFontSize16,
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    fontWeight: FontWeight.w600,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                  ),
                ),
                const SizedBox(height: SizeConfig.kHeight16),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(SizeConfig.borderRadius8),
                    border: Border.all(
                      color: darkModeController.isLightTheme.value
                          ? Colors.grey.withAlpha(75)
                          : ColorConfig.kDarkModeDividerColor,
                    ),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius:
                            BorderRadius.circular(SizeConfig.borderRadius8),
                        child: flutter_map.FlutterMap(
                          mapController: mapController,
                          options: flutter_map.MapOptions(
                            initialCenter:
                                selectedLocation ?? seydisehirLocation,
                            initialZoom: 15,
                            onTap: (tapPosition, point) {
                              _updateMarkers(point);
                            },
                          ),
                          children: [
                            flutter_map.TileLayer(
                              urlTemplate:
                                  'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.yemekkapimda.newapp',
                            ),
                            flutter_map.MarkerLayer(markers: markers),
                          ],
                        ),
                      ),
                      Positioned(
                        right: 10,
                        bottom: 10,
                        child: FloatingActionButton(
                          mini: true,
                          backgroundColor: ColorConfig.kPrimaryColor,
                          onPressed: () => _showLocationDialog(),
                          child: Icon(Icons.my_location,
                              color: ColorConfig.kWhiteColor),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: SizeConfig.kHeight30),
                CommonMaterialButton(
                  buttonColor: ColorConfig.kPrimaryColor,
                  height: SizeConfig.kHeight52,
                  txtColor: ColorConfig.kWhiteColor,
                  buttonText: StringConfig.saveAddress,
                  onButtonClick: _saveAddressData,
                  width: double.infinity,
                ),
                const SizedBox(height: SizeConfig.kHeight16),
                CommonMaterialButton(
                  buttonColor: darkModeController.isLightTheme.value
                      ? ColorConfig.kContainerLightColor
                      : ColorConfig.kDarkDialougeColor,
                  height: SizeConfig.kHeight52,
                  txtColor: darkModeController.isLightTheme.value
                      ? ColorConfig.kPrimaryColor
                      : ColorConfig.kWhiteColor,
                  buttonText: StringConfig.cancel,
                  onButtonClick: () {
                    editAddressController.streetController.clear();
                    editAddressController.street2Controller.clear();
                    editAddressController.landmarkController.clear();
                    editAddressController.descriptionController.clear();
                    Get.back();
                  },
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  InputDecoration _buildInputDecoration(String hintText) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        fontSize: FontConfig.kFontSize14,
        fontFamily: FontFamilyConfig.urbanistRegular,
        fontWeight: FontWeight.w400,
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kTextfieldTextColor
            : ColorConfig.kTextFieldLightTextColor,
      ),
      filled: true,
      fillColor: darkModeController.isLightTheme.value
          ? ColorConfig.kBgColor.withAlpha(50)
          : ColorConfig.kDarkModeColor,
      border: UnderlineInputBorder(
        borderSide: BorderSide.none,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
      ),
    );
  }

  @override
  void dispose() {
    mapController.dispose();
    super.dispose();
  }
}
