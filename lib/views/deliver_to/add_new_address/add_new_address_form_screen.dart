// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:yemekkapimda/app_routes/app_routes.dart';
import 'package:yemekkapimda/config/color.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_map/flutter_map.dart' as flutter_map;
import 'package:latlong2/latlong.dart' as lat_lng;
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/image_path.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../../controller/address_controller.dart';
import '../../../utils/appbar_common.dart';
import '../../../utils/common_material_button.dart';
import '../../../services/event_bus_service.dart';

class AddNewAddressFormScreen extends StatefulWidget {
  const AddNewAddressFormScreen({Key? key}) : super(key: key);

  @override
  State<AddNewAddressFormScreen> createState() =>
      _AddNewAddressFormScreenState();
}

class _AddNewAddressFormScreenState extends State<AddNewAddressFormScreen> {
  AddressController addressController = Get.put(AddressController());
  DarkModeController darkModeController = Get.put(DarkModeController());
  FirebaseAuth auth = FirebaseAuth.instance;
  FirebaseFirestore firestore = FirebaseFirestore.instance;

  flutter_map.MapController mapController = flutter_map.MapController();
  lat_lng.LatLng? selectedLocation;
  List<flutter_map.Marker> markers = [];

  // Seydişehir'in koordinatları
  static const lat_lng.LatLng seydisehirLocation =
      lat_lng.LatLng(37.4187, 31.8489);

  bool _mounted = true;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _mounted = true;
    selectedLocation = null;
    _updateMarkers(seydisehirLocation);
    // Dialog'u build tamamlandıktan sonra göster
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_mounted) {
        _showLocationDialog();
      }
    });
  }

  void _updateMarkers(lat_lng.LatLng position) {
    if (_mounted) {
      // Widget hala mount edilmiş mi kontrol et
      setState(() {
        markers = [
          flutter_map.Marker(
            point: position,
            child: Icon(
              Icons.location_pin,
              color: ColorConfig.kPrimaryColor,
              size: 40,
            ),
          ),
        ];
        selectedLocation = position;
      });
    }
  }

  Future<void> _requestLocationPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        "Hata",
        "Lütfen konum servisini açın",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        Get.snackbar(
          "Hata",
          "Konum izni reddedildi",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      Get.snackbar(
        "Hata",
        "Konum izni kalıcı olarak reddedildi. Ayarlardan izin vermeniz gerekiyor.",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    if (!_mounted) return;

    try {
      // Yükleme dialogunu göster
      _showLoadingDialog();

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ),
      );

      if (!_mounted) return;

      // Konum bulunduktan sonra dialogu kapat
      Get.back();

      final newLocation = lat_lng.LatLng(position.latitude, position.longitude);
      _updateMarkers(newLocation);
      mapController.move(newLocation, 15);
    } catch (e) {
      if (!_mounted) return;

      // Hata durumunda da dialogu kapat
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Varsayılan konum olarak Seydişehir'i kullan
      _updateMarkers(seydisehirLocation);
      mapController.move(seydisehirLocation, 15);

      // Hata mesajı göster
      Get.snackbar(
        "Hata",
        "Konum alınamadı, varsayılan konum kullanılıyor.",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _saveNewAddress() async {
    try {
      User? user = auth.currentUser;
      if (user == null) {
        Get.snackbar(
          "Hata",
          "Kullanıcı oturumu bulunamadı",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Kullanıcı referansını al
      DocumentReference userDocRef =
          firestore.collection('users').doc(user.uid);

      // Kullanıcı dokümanını kontrol et
      DocumentSnapshot userDoc = await userDocRef.get();
      if (!userDoc.exists) {
        Get.snackbar(
          "Hata",
          "Kullanıcı profili bulunamadı",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Yeni adres ID'si oluştur
      String newAddressId = DateTime.now().millisecondsSinceEpoch.toString();

      // Yeni adres verisi
      Map<String, dynamic> newAddress = {
        'id': newAddressId,
        'mahalle': addressController.streetController.text.trim(),
        'sokak': addressController.street2Controller.text.trim(),
        'bina_ve_daire': addressController.landmarkController.text.trim(),
        'aciklama': addressController.descriptionController.text.trim(),
        'latitude': selectedLocation!.latitude,
        'longitude': selectedLocation!.longitude,
        'createdAt': Timestamp.now(),
      };

      // Mevcut adresleri al
      var userData = userDoc.data() as Map<String, dynamic>;
      List<dynamic> currentAddresses = userData['addresses'] ?? [];

      // Yeni adresi ekle
      currentAddresses.add(newAddress);

      // Firestore'u güncelle
      await userDocRef.update({
        'addresses': currentAddresses,
      });

      // Event'i tetikle
      Get.find<EventBusService>().triggerAddressUpdate();

      // Form alanlarını temizle
      addressController.streetController.clear();
      addressController.street2Controller.clear();
      addressController.landmarkController.clear();
      addressController.descriptionController.clear();

      // Başarılı mesajı göster ve DeliverTo sayfasına yönlendir
      Get.snackbar(
        "Başarılı",
        "Yeni adres başarıyla eklendi",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // DeliverTo sayfasına yönlendir
      Get.offNamed(AppRoutes.deliverToScreen);
    } catch (e) {
      Get.snackbar(
        "Hata",
        "Adres eklenirken bir hata oluştu: ${e.toString()}",
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  void _showLocationDialog() {
    if (!_mounted) return;

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20),
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Konum Seçimi',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize20,
                  fontFamily: FontFamilyConfig.urbanistBold,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ColorConfig.kPrimaryColor.withAlpha(25),
                ),
                child: Icon(
                  Icons.location_on,
                  size: 40,
                  color: ColorConfig.kPrimaryColor,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Lütfen doğru teslimat için\ngerçek konumunuzu seçin',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontFamily: FontFamilyConfig.urbanistMedium,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor.withAlpha(165)
                      : ColorConfig.kWhiteColor.withAlpha(165),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                        Get.snackbar(
                          "Bilgi",
                          "Lütfen haritadan konumunuzu seçin",
                          backgroundColor: Colors.blue,
                          colorText: Colors.white,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: ColorConfig.kPrimaryColor,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Text(
                        'Haritadan Seç',
                        style: TextStyle(
                          color: ColorConfig.kPrimaryColor,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          fontSize: FontConfig.kFontSize16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                        _requestLocationPermission();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConfig.kPrimaryColor,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Konumumu Bul',
                        style: TextStyle(
                          color: ColorConfig.kWhiteColor,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          fontSize: FontConfig.kFontSize16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  void _showLoadingDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: ColorConfig.kPrimaryColor,
              ),
              const SizedBox(height: 20),
              Text(
                'Konumunuz Bulunuyor...',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontFamily: FontFamilyConfig.urbanistMedium,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: StringConfig.addNewAddress,
          leadingImage: ImagePath.arrow,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(SizeConfig.padding20),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Mahalle TextFormField
                    TextFormField(
                      controller: addressController.streetController,
                      cursorColor: ColorConfig.kTextfieldTextColor,
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize14,
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        fontWeight: FontWeight.w600,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                      decoration: InputDecoration(
                        hintText: "Mahalle",
                        hintStyle: TextStyle(
                          fontSize: FontConfig.kFontSize14,
                          fontFamily: FontFamilyConfig.urbanistRegular,
                          fontWeight: FontWeight.w400,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kTextfieldTextColor
                              : ColorConfig.kTextFieldLightTextColor,
                        ),
                        filled: true,
                        fillColor: darkModeController.isLightTheme.value
                            ? ColorConfig.kBgColor.withAlpha(50)
                            : ColorConfig.kDarkModeColor,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius:
                              BorderRadius.circular(SizeConfig.borderRadius8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Mahalle adı zorunludur';
                        }
                        return null;
                      },
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),
                    const SizedBox(height: SizeConfig.kHeight24),

                    // Sokak TextFormField
                    TextFormField(
                      controller: addressController.street2Controller,
                      cursorColor: ColorConfig.kTextfieldTextColor,
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize14,
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        fontWeight: FontWeight.w600,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                      decoration: InputDecoration(
                        hintText: "Sokak",
                        hintStyle: TextStyle(
                          fontSize: FontConfig.kFontSize14,
                          fontFamily: FontFamilyConfig.urbanistRegular,
                          fontWeight: FontWeight.w400,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kTextfieldTextColor
                              : ColorConfig.kTextFieldLightTextColor,
                        ),
                        filled: true,
                        fillColor: darkModeController.isLightTheme.value
                            ? ColorConfig.kBgColor.withAlpha(50)
                            : ColorConfig.kDarkModeColor,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius:
                              BorderRadius.circular(SizeConfig.borderRadius8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Sokak adı zorunludur';
                        }
                        return null;
                      },
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),
                    const SizedBox(height: SizeConfig.kHeight24),

                    // Bina ve Daire TextFormField
                    TextFormField(
                      controller: addressController.landmarkController,
                      cursorColor: ColorConfig.kTextfieldTextColor,
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize14,
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        fontWeight: FontWeight.w600,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                      decoration: InputDecoration(
                        hintText: "Bina ve Daire Bilgisi",
                        hintStyle: TextStyle(
                          fontSize: FontConfig.kFontSize14,
                          fontFamily: FontFamilyConfig.urbanistRegular,
                          fontWeight: FontWeight.w400,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kTextfieldTextColor
                              : ColorConfig.kTextFieldLightTextColor,
                        ),
                        filled: true,
                        fillColor: darkModeController.isLightTheme.value
                            ? ColorConfig.kBgColor.withAlpha(50)
                            : ColorConfig.kDarkModeColor,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius:
                              BorderRadius.circular(SizeConfig.borderRadius8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Bina ve daire bilgisi zorunludur';
                        }
                        return null;
                      },
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),
                    const SizedBox(height: SizeConfig.kHeight24),

                    // Açıklama TextFormField
                    TextFormField(
                      controller: addressController.descriptionController,
                      cursorColor: ColorConfig.kTextfieldTextColor,
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize14,
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        fontWeight: FontWeight.w600,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                      decoration: InputDecoration(
                        hintText:
                            "Adres Tarifi (Örn: Apartmanın arkasındaki sokak, mavi kapılı bina)",
                        hintStyle: TextStyle(
                          fontSize: FontConfig.kFontSize14,
                          fontFamily: FontFamilyConfig.urbanistRegular,
                          fontWeight: FontWeight.w400,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kTextfieldTextColor
                              : ColorConfig.kTextFieldLightTextColor,
                        ),
                        filled: true,
                        fillColor: darkModeController.isLightTheme.value
                            ? ColorConfig.kBgColor.withAlpha(50)
                            : ColorConfig.kDarkModeColor,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius:
                              BorderRadius.circular(SizeConfig.borderRadius8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Adres tarifi zorunludur';
                        }
                        return null;
                      },
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      maxLines: 2,
                    ),
                    const SizedBox(height: SizeConfig.kHeight24),

                    // Harita başlığı
                    Text(
                      "Konumu Haritada İşaretleyin",
                      style: TextStyle(
                        fontSize: FontConfig.kFontSize16,
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        fontWeight: FontWeight.w600,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                    ),
                    const SizedBox(height: SizeConfig.kHeight16),

                    // Google Maps
                    Container(
                      height: 200,
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(SizeConfig.borderRadius8),
                        border: Border.all(
                          color: darkModeController.isLightTheme.value
                              ? Colors.grey.withAlpha(75)
                              : ColorConfig.kDarkModeDividerColor,
                        ),
                      ),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius:
                                BorderRadius.circular(SizeConfig.borderRadius8),
                            child: flutter_map.FlutterMap(
                              mapController: mapController,
                              options: flutter_map.MapOptions(
                                initialCenter: seydisehirLocation,
                                initialZoom: 15,
                                onTap: (tapPosition, point) {
                                  _updateMarkers(point);
                                },
                              ),
                              children: [
                                flutter_map.TileLayer(
                                  urlTemplate:
                                      'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                  userAgentPackageName:
                                      'com.yemekkapimda.newapp',
                                ),
                                flutter_map.MarkerLayer(markers: markers),
                              ],
                            ),
                          ),
                          Positioned(
                            right: 10,
                            bottom: 10,
                            child: FloatingActionButton(
                              mini: true,
                              backgroundColor: ColorConfig.kPrimaryColor,
                              onPressed: () => _showLocationDialog(),
                              child: Icon(Icons.my_location,
                                  color: ColorConfig.kWhiteColor),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: SizeConfig.kHeight30),

                    // Kaydet Butonu
                    CommonMaterialButton(
                      buttonColor: ColorConfig.kPrimaryColor,
                      height: SizeConfig.kHeight52,
                      txtColor: ColorConfig.kWhiteColor,
                      buttonText: StringConfig.saveNewAddress,
                      onButtonClick: () async {
                        // Form validasyonunu kontrol et
                        if (!_formKey.currentState!.validate()) {
                          Get.snackbar(
                            "Hata",
                            "Lütfen tüm zorunlu alanları doldurun",
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }

                        // Konum kontrolü
                        if (selectedLocation == null) {
                          Get.snackbar(
                            "Hata",
                            "Lütfen haritadan konum seçin veya mevcut konumunuzu kullanın",
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }

                        await _saveNewAddress();
                      },
                      width: double.infinity,
                    ),
                    const SizedBox(height: SizeConfig.kHeight16),

                    // İptal Butonu
                    CommonMaterialButton(
                      buttonColor: darkModeController.isLightTheme.value
                          ? ColorConfig.kContainerLightColor
                          : ColorConfig.kDarkDialougeColor,
                      height: SizeConfig.kHeight52,
                      txtColor: darkModeController.isLightTheme.value
                          ? ColorConfig.kPrimaryColor
                          : ColorConfig.kWhiteColor,
                      buttonText: StringConfig.cancel,
                      onButtonClick: () {
                        addressController.streetController.clear();
                        addressController.street2Controller.clear();
                        addressController.landmarkController.clear();
                        addressController.descriptionController.clear();
                        Get.back();
                      },
                      width: double.infinity,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mounted = false;
    mapController.dispose();
    super.dispose();
  }
}
