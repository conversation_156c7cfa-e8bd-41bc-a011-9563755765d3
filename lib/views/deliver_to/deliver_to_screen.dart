// ignore_for_file: must_be_immutable, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../utils/appbar_common.dart';

class DeliverTo extends StatefulWidget {
  const DeliverTo({Key? key}) : super(key: key);

  @override
  State<DeliverTo> createState() => _DeliverToState();
}

class _DeliverToState extends State<DeliverTo> {
  DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  String? selectedAddressId;

  // Adreslerin ve seçili adresin geçerliliğini kontrol eden fonksiyon
  Future<bool> _hasValidSelectedAddress() async {
    User? user = _auth.currentUser;
    if (user != null) {
      DocumentSnapshot userDoc =
          await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        var userData = userDoc.data() as Map<String, dynamic>;
        List addresses = userData['addresses'] ?? [];
        String? deliverTo = userData['deliverTo'];

        // Adres listesi boşsa false
        if (addresses.isEmpty) return false;

        // Seçili adres yoksa false
        if (deliverTo == null) return false;

        // Seçili adresin hala mevcut olup olmadığını kontrol et
        bool addressExists = addresses
            .any((address) => address['id'].toString() == deliverTo.toString());
        return addressExists;
      }
    }
    return false;
  }

  // Firebase'den adresleri çekme fonksiyonu
  Future<List<Map<String, dynamic>>> _loadAddresses() async {
    User? user = _auth.currentUser;
    if (user != null) {
      DocumentSnapshot userDoc =
          await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        var userData = userDoc.data() as Map<String, dynamic>;
        List addresses = userData['addresses'] ?? [];
        String? deliverTo = userData['deliverTo'];

        // Eğer deliverTo seçili değilse veya seçili adres artık mevcut değilse
        bool addressExists = deliverTo != null &&
            addresses.any(
                (address) => address['id'].toString() == deliverTo.toString());

        if (!addressExists && addresses.isNotEmpty) {
          // İlk adresi seç
          _saveDeliverToAddress(addresses[0]['id']);
          selectedAddressId = addresses[0]['id'];
        } else {
          selectedAddressId = addressExists ? deliverTo : null;
        }

        return List<Map<String, dynamic>>.from(addresses);
      }
    }
    return [];
  }

  // Seçilen adresin id'sini Firebase'e kaydetme
  Future<void> _saveDeliverToAddress(String addressId) async {
    User? user = _auth.currentUser;
    if (user != null) {
      await _firestore.collection('users').doc(user.uid).update({
        'deliverTo': addressId,
      });

      Get.snackbar(
        "Başarılı",
        "Teslimat adresi güncellendi",
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  // Adresi seçme ve Firebase'e id'yi kaydetme
  void selectContainer(String addressId) {
    // Eğer zaten seçili olan adrese tıklandıysa işlem yapma
    if (selectedAddressId == addressId) return;

    setState(() {
      selectedAddressId = addressId;
    });
    _saveDeliverToAddress(addressId);
  }

  // Seçili adresin ikonu
  String getContainerImage(String addressId) {
    return selectedAddressId == addressId
        ? ImagePath.radioFillIcon
        : ImagePath.radioIcon;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (bool didPop) async {
        bool hasValidAddress = await _hasValidSelectedAddress();
        if (hasValidAddress && !didPop) {
          Navigator.of(context).pop();
        } else if (!hasValidAddress) {
          Get.snackbar(
            "Uyarı",
            "Lütfen bir teslimat adresi seçin",
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
      },
      child: Obx(
        () => Scaffold(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
            child: CommonAppBar(
              title: StringConfig.deliverTo,
              leadingImage: ImagePath.arrow,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
              leadingOnTap: () async {
                bool hasValidAddress = await _hasValidSelectedAddress();
                if (hasValidAddress) {
                  Navigator.of(context).pop();
                } else {
                  Get.snackbar(
                    "Uyarı",
                    "Lütfen bir teslimat adresi seçin",
                    backgroundColor: Colors.orange,
                    colorText: Colors.white,
                    duration: const Duration(seconds: 2),
                  );
                }
              },
            ),
          ),
          body: FutureBuilder<List<Map<String, dynamic>>>(
            future: _loadAddresses(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(child: Text("Hata: ${snapshot.error}"));
              } else if (snapshot.data == null || snapshot.data!.isEmpty) {
                return const Center(child: Text("Adres bulunamadı"));
              }

              List<Map<String, dynamic>> addresses = snapshot.data!;

              return Padding(
                padding: const EdgeInsets.only(top: SizeConfig.padding10),
                child: Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        itemCount: addresses.length,
                        itemBuilder: (context, index) {
                          Map<String, dynamic> address = addresses[index];
                          String addressId = address['id']; // Adresin id'si
                          String fullAddress =
                              "${address['mahalle']}, ${address['sokak']}, ${address['bina_ve_daire']}";

                          return Padding(
                            padding: const EdgeInsets.only(
                              left: SizeConfig.padding20,
                              right: SizeConfig.padding20,
                              bottom: SizeConfig.kHeight16,
                            ),
                            child: InkWell(
                              onTap: () => selectContainer(addressId),
                              child: Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: darkModeController.isLightTheme.value
                                      ? ColorConfig.kWhiteColor
                                      : ColorConfig.kDarkModeColor,
                                  borderRadius: BorderRadius.circular(
                                      SizeConfig.borderRadius12),
                                  border: Border.all(
                                    color: selectedAddressId == addressId
                                        ? ColorConfig.kPrimaryColor
                                        : Colors.transparent,
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      spreadRadius: 2,
                                      color: ColorConfig.kDarkDialougeColor
                                          .withAlpha(15),
                                      blurRadius: 10,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(
                                      SizeConfig.padding16),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: ColorConfig.kPrimaryColor
                                              .withAlpha(25),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Image.asset(
                                          ImagePath.mapLocation,
                                          width: SizeConfig.kHeight24,
                                          height: SizeConfig.kHeight24,
                                          color: ColorConfig.kPrimaryColor,
                                        ),
                                      ),
                                      const SizedBox(
                                          width: SizeConfig.kHeight16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              address['mahalle'],
                                              style: TextStyle(
                                                fontSize:
                                                    FontConfig.kFontSize16,
                                                fontFamily: FontFamilyConfig
                                                    .urbanistBold,
                                                color: darkModeController
                                                        .isLightTheme.value
                                                    ? ColorConfig.kBlackColor
                                                    : ColorConfig.kWhiteColor,
                                              ),
                                            ),
                                            const SizedBox(
                                                height: SizeConfig.kHeight8),
                                            Text(
                                              fullAddress,
                                              style: TextStyle(
                                                fontSize:
                                                    FontConfig.kFontSize14,
                                                fontFamily: FontFamilyConfig
                                                    .urbanistRegular,
                                                color: darkModeController
                                                        .isLightTheme.value
                                                    ? ColorConfig.kHintColor
                                                    : ColorConfig.kWhiteColor
                                                        .withAlpha(165),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(
                                          width: SizeConfig.kHeight12),
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: selectedAddressId ==
                                                    addressId
                                                ? ColorConfig.kPrimaryColor
                                                : Colors.grey.withAlpha(125),
                                            width: 2,
                                          ),
                                        ),
                                        child: selectedAddressId == addressId
                                            ? Center(
                                                child: Container(
                                                  width: 12,
                                                  height: 12,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: ColorConfig
                                                        .kPrimaryColor,
                                                  ),
                                                ),
                                              )
                                            : null,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
