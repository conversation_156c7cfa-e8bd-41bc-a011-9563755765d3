import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import '../../utils/common_material_button.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import '../../utils/appbar_common.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({Key? key}) : super(key: key);

  @override
  State<SignInScreen> createState() => SignInScreenState();
}

class SignInScreenState extends State<SignInScreen> {
  final DarkModeController darkModeController = Get.put(DarkModeController());

  final countryPicker = const FlCountryCodePicker();
  late FlCountryCodePicker countryPickerWithParams;
  final TextEditingController phoneController = TextEditingController();
  String selectedCountryCode = '+90'; // Default ülke kodu (TR)
  bool isLoading = false; // Yükleme durumu

  @override
  void initState() {
    super.initState();
    countryPickerWithParams = const FlCountryCodePicker(
      localize: true,
      showDialCode: true,
      showSearchBar: true,
    );

    // Check if the user is already logged in
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (FirebaseAuth.instance.currentUser != null) {
        Get.offAllNamed(AppRoutes.bottomBar);
      }
    });
  }

  void _showCountryPicker() async {
    final code = await countryPickerWithParams.showPicker(
      context: context,
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kDarkModeColor,
    );
    if (code != null) {
      setState(() {
        selectedCountryCode = code.dialCode;
      });
    }
  }

  void _signInWithPhoneNumber() async {
    if (phoneController.text.isEmpty || phoneController.text.length < 10) {
      Get.snackbar(
        'Hata',
        'Lütfen geçerli bir telefon numarası girin',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    String phoneNumber = "$selectedCountryCode${phoneController.text.trim()}";

    await FirebaseAuth.instance.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: (PhoneAuthCredential credential) {
        // Otomatik doğrulamayı devre dışı bırakalım
        setState(() {
          isLoading = false;
        });
      },
      verificationFailed: (FirebaseAuthException e) {
        Get.snackbar('Doğrulama Başarısız', e.message ?? 'Bilinmeyen hata');
        setState(() {
          isLoading = false; // Yükleme bitiyor
        });
      },
      codeSent: (String verificationId, int? resendToken) {
        setState(() {
          isLoading = false; // Yükleme bitiyor
        });
        Get.toNamed(AppRoutes.otpScreen,
            arguments: {'verificationId': verificationId});
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        setState(() {
          isLoading = false; // Yükleme bitiyor
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Obx(() => Scaffold(
            backgroundColor: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kBlackColor,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
                child: CommonAppBar(
                  leadingImage: ImagePath.restarantMenu,
                  leadingOnTap: () {},
                  actions: [
                    TextButton(
                      onPressed: () {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          Get.offAllNamed(AppRoutes
                              .bottomBar); // Navigate to the desired page
                        });
                      },
                      child: Text(
                        'Atla',
                        style: TextStyle(
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor,
                          fontSize: FontConfig.kFontSize16,
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                        ),
                      ),
                    ),
                  ],
                )),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  Center(
                    child: Text(
                      StringConfig.signIn,
                      style: TextStyle(
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          fontSize: FontConfig.kFontSize24,
                          fontWeight: FontWeight.w600,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor),
                    ),
                  ),
                  const SizedBox(height: SizeConfig.kHeight8),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: SizeConfig.kHeight30),
                    child: Text(
                      StringConfig.pleaseEnterYourNumber,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistRegular,
                        fontSize: FontConfig.kFontSize14,
                        fontWeight: FontWeight.w400,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: SizeConfig.kHeight40),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: SizeConfig.kHeight20),
                    child: Container(
                      height: SizeConfig.kHeight50,
                      decoration: BoxDecoration(
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kFillColor
                            : ColorConfig.kDarkModeColor,
                        borderRadius:
                            BorderRadius.circular(SizeConfig.borderRadius10),
                      ),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: SizeConfig.kHeight10),
                            child: GestureDetector(
                              onTap: _showCountryPicker,
                              child: Row(
                                children: [
                                  Text(
                                    selectedCountryCode,
                                    style: TextStyle(
                                      fontFamily:
                                          FontFamilyConfig.urbanistSemiBold,
                                      color:
                                          darkModeController.isLightTheme.value
                                              ? ColorConfig.kBlackColor
                                              : ColorConfig.kWhiteColor,
                                      fontWeight: FontWeight.w300,
                                      fontSize: FontConfig.kFontSize14,
                                    ),
                                  ),
                                  const SizedBox(width: SizeConfig.kHeight5),
                                  Icon(
                                    Icons.arrow_drop_down,
                                    color: darkModeController.isLightTheme.value
                                        ? ColorConfig.kBlackColor
                                        : ColorConfig.kFillColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          VerticalDivider(
                            color: Colors.grey.withAlpha(50),
                            thickness: 1,
                            width: SizeConfig.kHeight10,
                            indent: SizeConfig.kHeight12,
                            endIndent: SizeConfig.kHeight12,
                          ),
                          const SizedBox(width: SizeConfig.kHeight5),
                          Expanded(
                            child: TextField(
                              controller: phoneController,
                              keyboardType: TextInputType.phone,
                              autofillHints: const [],
                              enableIMEPersonalizedLearning: false,
                              enableInteractiveSelection: true,
                              enableSuggestions: false,
                              autocorrect: false,
                              style: TextStyle(
                                color: darkModeController.isLightTheme.value
                                    ? ColorConfig.kBlackColor
                                    : ColorConfig.kWhiteColor,
                                height: SizeConfig.kH07,
                              ),
                              decoration: InputDecoration(
                                hintText: StringConfig.enterPhoneNumber,
                                hintStyle: TextStyle(
                                  fontFamily: FontFamilyConfig.urbanistRegular,
                                  color: darkModeController.isLightTheme.value
                                      ? ColorConfig.kHintColor
                                      : ColorConfig.kWhiteColor,
                                  fontWeight: FontWeight.w300,
                                  fontSize: FontConfig.kFontSize14,
                                ),
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: SizeConfig.kHeight60),
                  isLoading
                      ? const CircularProgressIndicator() // Yükleme göstergesi
                      : CommonMaterialButton(
                          height: SizeConfig.kHeight52,
                          width: double.infinity,
                          buttonColor: ColorConfig.kPrimaryColor,
                          txtColor: ColorConfig.kWhiteColor,
                          buttonText: StringConfig.signIn,
                          onButtonClick: _signInWithPhoneNumber,
                        ),
                  const SizedBox(height: SizeConfig.kHeight32),
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(AppRoutes.signUpScreen);
                    },
                    child: RichText(
                      text: TextSpan(
                        children: <TextSpan>[
                          TextSpan(
                            text: StringConfig.dontHaveAnAccount,
                            style: TextStyle(
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kBlackColor
                                  : ColorConfig.kWhiteColor,
                              fontFamily: FontFamilyConfig.urbanistRegular,
                              fontSize: FontConfig.kFontSize14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: StringConfig.signUp,
                            style: TextStyle(
                              color: ColorConfig.kPrimaryColor,
                              fontFamily: FontFamilyConfig.urbanistBold,
                              fontSize: FontConfig.kFontSize14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
