import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'dart:async';

import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';

class SearchSection extends StatefulWidget {
  final DarkModeController darkModeController;

  const SearchSection({
    Key? key,
    required this.darkModeController,
  }) : super(key: key);

  @override
  State<SearchSection> createState() => _SearchSectionState();
}

class _SearchSectionState extends State<SearchSection> {
  final searchController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  final RxBool showResults = false.obs;
  final RxBool isLoading = false.obs;
  final RxList<Map<String, dynamic>> searchResults =
      <Map<String, dynamic>>[].obs;

  @override
  void initState() {
    super.initState();
    focusNode.addListener(() {
      if (focusNode.hasFocus && searchResults.isNotEmpty) {
        showResults.value = true;
      }
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  String normalizeString(String text) {
    // Türkçe karakterleri normalize et
    final normalized = text
        .toLowerCase()
        .replaceAll('ı', 'i')
        .replaceAll('ğ', 'g')
        .replaceAll('ü', 'u')
        .replaceAll('ş', 's')
        .replaceAll('ö', 'o')
        .replaceAll('ç', 'c')
        .replaceAll('İ', 'i')
        .replaceAll('Ğ', 'g')
        .replaceAll('Ü', 'u')
        .replaceAll('Ş', 's')
        .replaceAll('Ö', 'o')
        .replaceAll('Ç', 'c');
    return normalized.trim();
  }

  Future<void> performSearch(String query) async {
    if (query.isEmpty) {
      searchResults.clear();
      isLoading.value = false;
      showResults.value = false;
      return;
    }

    isLoading.value = true;
    showResults.value = true;

    try {
      final normalizedQuery = normalizeString(query);
      final results = <Map<String, dynamic>>[];

      // Get all visible firms first
      final firmsSnapshot = await FirebaseFirestore.instance
          .collection('firms')
          .where('visible', isEqualTo: true)
          .get();

      // Create a map of firm IDs to firm data
      Map<String, Map<String, dynamic>> visibleFirms = {};
      for (var doc in firmsSnapshot.docs) {
        visibleFirms[doc.id] = {
          ...doc.data(),
          'id': doc.id,
        };
      }

      // Search in categories
      final categoriesSnapshot =
          await FirebaseFirestore.instance.collection('categories').get();

      for (var doc in categoriesSnapshot.docs) {
        final name = normalizeString(doc.data()['name'] as String);
        if (name.contains(normalizedQuery)) {
          results.add({
            'id': doc.id,
            'name': doc.data()['name'],
            'imageUrl': doc.data()['imageUrl'],
            'type': 'category',
          });
        }
      }

      // Search in visible firms
      for (var firmData in visibleFirms.values) {
        final name = normalizeString(firmData['name'] as String);
        if (name.contains(normalizedQuery)) {
          results.add({
            'id': firmData['id'],
            'name': firmData['name'],
            'banner': firmData['banner'],
            'type': 'firm',
          });
        }
      }

      // Search in products from visible firms
      final productsSnapshot =
          await FirebaseFirestore.instance.collection('products').get();

      for (var doc in productsSnapshot.docs) {
        var productData = doc.data();
        String firmId = productData['firmId'];

        // Only include products from visible firms
        if (visibleFirms.containsKey(firmId)) {
          final productName = normalizeString(productData['name'] as String);
          if (productName.contains(normalizedQuery)) {
            results.add({
              'id': doc.id,
              'name': productData['name'],
              'price': productData['price'],
              'image': productData['images'] != null &&
                      (productData['images'] as List).isNotEmpty
                  ? productData['images'][0]
                  : 'assets/images/placeholder.png',
              'firmId': firmId,
              'firmName': visibleFirms[firmId]!['name'],
              'type': 'product',
            });
          }
        }
      }

      searchResults.value = results;
    } finally {
      isLoading.value = false;
    }
  }

  void navigateToResult(Map<String, dynamic> result) {
    switch (result['type']) {
      case 'category':
        Get.toNamed(
          AppRoutes.categoryProducts,
          parameters: {
            'categoryName': result['name'],
            'categoryId': result['id'],
          },
        );
        break;
      case 'firm':
        Get.toNamed(
          AppRoutes.firmDetail,
          parameters: {'firmId': result['id']},
        );
        break;
      case 'product':
        Get.toNamed(
          AppRoutes.productDetail,
          parameters: {'productId': result['id']},
        );
        break;
    }
  }

  Widget buildResultTile(Map<String, dynamic> result) {
    switch (result['type']) {
      case 'product':
        return Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ColorConfig.kHintColor.withAlpha(25),
                width: 1,
              ),
            ),
          ),
          child: ListTile(
            leading: ClipRRect(
              borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
              child: Image.network(
                result['image'],
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    const Icon(Icons.error),
              ),
            ),
            title: Text(
              result['name'],
              style: TextStyle(
                color: widget.darkModeController.isLightTheme.value
                    ? ColorConfig.kBlackColor
                    : ColorConfig.kWhiteColor,
                fontFamily: FontFamilyConfig.urbanistMedium,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${result['price']} TL',
                  style: TextStyle(
                    color: ColorConfig.kPrimaryColor,
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    fontSize: FontConfig.kFontSize14,
                  ),
                ),
                Text(
                  result['firmName'],
                  style: TextStyle(
                    color: widget.darkModeController.isLightTheme.value
                        ? ColorConfig.kHintColor
                        : ColorConfig.kDarkModeDividerColor,
                    fontSize: FontConfig.kFontSize12,
                  ),
                ),
              ],
            ),
            onTap: () => navigateToResult(result),
          ),
        );
      default:
        return ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
            child: Image.network(
              result['type'] == 'category'
                  ? result['imageUrl']
                  : result['banner'],
              width: 40,
              height: 40,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) =>
                  const Icon(Icons.error),
            ),
          ),
          title: Text(
            result['name'],
            style: TextStyle(
              color: widget.darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            ),
          ),
          subtitle: Text(
            result['type'] == 'category' ? 'Kategori' : 'Restaurant',
            style: TextStyle(
              color: widget.darkModeController.isLightTheme.value
                  ? ColorConfig.kHintColor
                  : ColorConfig.kDarkModeDividerColor,
            ),
          ),
          onTap: () => navigateToResult(result),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight20),
          child: TextFormField(
            controller: searchController,
            focusNode: focusNode,
            cursorColor: ColorConfig.kPrimaryColor,
            showCursor: true,
            onFieldSubmitted: (value) {
              performSearch(value);
              showResults.value = true;
            },
            style: TextStyle(
              color: widget.darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
              height: SizeConfig.kH07,
            ),
            decoration: InputDecoration(
              prefixIcon: IconButton(
                icon: Obx(() => isLoading.value
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: ColorConfig.kPrimaryColor,
                          ),
                        ),
                      )
                    : Icon(
                        CupertinoIcons.search,
                        color: widget.darkModeController.isLightTheme.value
                            ? ColorConfig.kHintColor.withAlpha(175)
                            : ColorConfig.kWhiteColor,
                      )),
                onPressed: () {
                  performSearch(searchController.text);
                  showResults.value = true;
                },
              ),
              suffixIcon: Obx(
                  () => showResults.value && searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            searchController.clear();
                            searchResults.clear();
                            FocusScope.of(context).unfocus();
                          },
                        )
                      : const SizedBox.shrink()),
              fillColor: widget.darkModeController.isLightTheme.value
                  ? ColorConfig.kFillColor
                  : ColorConfig.kDarkModeColor,
              filled: true,
              hintText: StringConfig.search,
              hintStyle: TextStyle(
                fontFamily: FontFamilyConfig.urbanistRegular,
                color: widget.darkModeController.isLightTheme.value
                    ? ColorConfig.kHintColor
                    : ColorConfig.kDarkModeDividerColor,
                fontWeight: FontWeight.w300,
                fontSize: FontConfig.kFontSize14,
              ),
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                    Radius.circular(SizeConfig.borderRadius10)),
                borderSide: BorderSide(
                    width: SizeConfig.kHeight0, style: BorderStyle.none),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
                borderSide: const BorderSide(
                    width: SizeConfig.kHeight0, style: BorderStyle.none),
              ),
            ),
          ),
        ),
        Obx(() => showResults.value
            ? Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: SizeConfig.kHeight20,
                  vertical: SizeConfig.kHeight10,
                ),
                decoration: BoxDecoration(
                  color: widget.darkModeController.isLightTheme.value
                      ? ColorConfig.kWhiteColor
                      : ColorConfig.kDarkModeColor,
                  borderRadius:
                      BorderRadius.circular(SizeConfig.borderRadius10),
                  boxShadow: [
                    BoxShadow(
                      color: ColorConfig.kBlackColor.withAlpha(25),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: searchResults.isEmpty &&
                        !isLoading.value &&
                        searchController.text.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'Sonuç bulunamadı',
                          style: TextStyle(
                            color: widget.darkModeController.isLightTheme.value
                                ? ColorConfig.kHintColor
                                : ColorConfig.kDarkModeDividerColor,
                          ),
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: searchResults.length,
                        itemBuilder: (context, index) {
                          final result = searchResults[index];
                          return buildResultTile(result);
                        },
                      ),
              )
            : const SizedBox.shrink()),
      ],
    );
  }
}
