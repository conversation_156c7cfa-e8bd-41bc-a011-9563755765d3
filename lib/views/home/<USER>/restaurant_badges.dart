import 'package:flutter/material.dart';
import '../../../config/color.dart';

class OrderStatusBadges extends StatelessWidget {
  final bool isVisible;
  final int minOrderAmount;
  final bool hasFreeDelivery;

  const OrderStatusBadges({
    Key? key,
    required this.isVisible,
    this.minOrderAmount = 100,
    this.hasFreeDelivery = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        StatusBadge(
          text: isVisible ? 'Açık' : 'Kapalı',
          color: isVisible ? Colors.green : Colors.red,
          icon: isVisible ? Icons.check_circle : Icons.cancel,
        ),
        const SizedBox(width: 8),
        StatusBadge(
          text: 'Min Tutar',
          subText: '$minOrderAmount ₺',
          color: ColorConfig.kPrimaryColor,
          icon: Icons.monetization_on,
          showSubText: true,
        ),
        if (hasFreeDelivery) ...[
          const SizedBox(width: 8),
          const StatusBadge(
            text: 'Teslimat',
            subText: 'Ücretsiz',
            color: Colors.green,
            icon: Icons.local_shipping,
            showSubText: true,
          ),
        ],
      ],
    );
  }
}

class StatusBadge extends StatelessWidget {
  final String text;
  final String? subText;
  final Color color;
  final IconData icon;
  final bool showSubText;

  const StatusBadge({
    Key? key,
    required this.text,
    this.subText,
    required this.color,
    required this.icon,
    this.showSubText = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          if (showSubText) ...[
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subText ?? '',
                  style: TextStyle(
                    color: Colors.grey[800],
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ] else ...[
            Text(
              text,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
