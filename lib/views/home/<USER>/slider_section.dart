import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';


import '../../../config/size_config.dart';

import '../../../controller/slider_controller.dart';



class SliderSection extends StatefulWidget {
  final SliderController sliderController;
  final PageController pageController;

  const SliderSection({
    Key? key,
    required this.sliderController,
    required this.pageController,
  }) : super(key: key);

  @override
  State<SliderSection> createState() => _SliderSectionState();
}

class _SliderSectionState extends State<SliderSection> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (widget.pageController.hasClients) {
        final nextPage = (widget.pageController.page?.toInt() ?? 0) + 1;
        
        if (nextPage < widget.sliderController.sliders.length) {
          widget.pageController.animateToPage(
            nextPage,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          widget.sliderController.updateIndex(nextPage);
        } else {
          widget.pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          widget.sliderController.updateIndex(0);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SliderController>(
      builder: (controller) {
        if (controller.isLoading.value) {
          return const SizedBox(
            height: SizeConfig.kHeight140,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (controller.sliders.isEmpty) {
          return SizedBox(
            height: SizeConfig.kHeight140,
            child: Center(
              child: Text(
                'Slider Görselleri Yüklenemedi',
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 16,
                ),
              ),
            ),
          );
        }

        return SizedBox(
          height: SizeConfig.kHeight140,
          child: PageView.builder(
            itemCount: controller.sliders.length,
            controller: widget.pageController,
            onPageChanged: controller.updateIndex,
            itemBuilder: (context, index) {
              final slider = controller.sliders[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
                  child: Image.network(
                    slider.imageUrl,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey[300],
                        child: const Center(child: CircularProgressIndicator()),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Center(child: Text('Görsel Yüklenemedi')),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}