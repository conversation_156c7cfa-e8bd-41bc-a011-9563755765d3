import 'package:flutter/material.dart';

import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';

class OrderHeader extends StatelessWidget {
  final Map<String, dynamic> orderData;
  final bool isDarkMode;

  const OrderHeader({
    Key? key,
    required this.orderData,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Sipariş #${orderData['id'] ?? 'null'}',
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistMedium,
            fontSize: FontConfig.kFontSize16,
            fontWeight: FontWeight.w600,
            color:
                isDarkMode ? ColorConfig.kWhiteColor : ColorConfig.kBlackColor,
          ),
        ),
        if (orderData['status'] != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.orange.withAlpha(50),
              borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
            ),
            child: Text(
              orderData['status'],
              style: const TextStyle(
                color: Colors.orange,
                fontSize: FontConfig.kFontSize12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }
}
