import 'dart:ui';

import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';

class CommonStyles {
  static TextStyle regularText(bool isDarkMode) => TextStyle(
    fontFamily: FontFamilyConfig.urbanistRegular,
    fontSize: FontConfig.kFontSize14,
    color: isDarkMode
        ? ColorConfig.kWhiteColor.withAlpha(175)
        : ColorConfig.kBlackColor.withAlpha(175),
  );

  static TextStyle mediumText(bool isDarkMode) => TextStyle(
    fontFamily: FontFamilyConfig.urbanistMedium,
    fontSize: FontConfig.kFontSize14,
    color: isDarkMode ? ColorConfig.kWhiteColor : ColorConfig.kBlackColor,
  );
}