import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';




class CategoriesSection extends StatelessWidget {
  final DarkModeController darkModeController;
  final Map<String, Map<String, dynamic>> categoriesMap;

  const CategoriesSection({
    Key? key,
    required this.darkModeController,
    required this.categoriesMap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                StringConfig.categories,
                style: TextStyle(
                  fontFamily: FontFamilyConfig.urbanistSemiBold,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                  fontWeight: FontWeight.w600,
                  fontSize: FontConfig.kFontSize18,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Get.toNamed(
                    AppRoutes.allCategories,
                    arguments: categoriesMap,
                  );
                },
                child: Text(
                  'Hepsini Gör',
                  style: TextStyle(
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    color: ColorConfig.kPrimaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: FontConfig.kFontSize13,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: SizeConfig.kHeight24),
        SizedBox(
          height: SizeConfig.kHeight100,
          child: ListView.builder(
            itemCount: categoriesMap.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              String categoryId = categoriesMap.keys.elementAt(index);
              String categoryName =
                  categoriesMap[categoryId]?['name'] ?? "Kategori Yok";
              String categoryImageUrl = categoriesMap[categoryId]?['imageUrl'] ??
                  'assets/images/category_placeholder.png';

              return CategoryItem(
                darkModeController: darkModeController,
                categoryId: categoryId,
                categoryName: categoryName,
                categoryImageUrl: categoryImageUrl,
              );
            },
          ),
        ),
      ],
    );
  }
}

class CategoryItem extends StatelessWidget {
  final DarkModeController darkModeController;
  final String categoryId;
  final String categoryName;
  final String categoryImageUrl;

  const CategoryItem({
    Key? key,
    required this.darkModeController,
    required this.categoryId,
    required this.categoryName,
    required this.categoryImageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              Get.toNamed(
                AppRoutes.categoryProducts,
                parameters: {
                  'categoryName': categoryName,
                  'categoryId': categoryId,
                },
              );
            },
            child: ClipOval(
              child: Image.network(
                categoryImageUrl,
                height: SizeConfig.kHeight60,
                width: SizeConfig.kHeight60,
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: SizeConfig.kHeight12),
          Text(
            categoryName,
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistSemiBold,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
              fontWeight: FontWeight.w600,
              fontSize: FontConfig.kFontSize14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}