import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';
import 'rating_stars.dart';

class RestaurantsSection extends StatelessWidget {
  final DarkModeController darkModeController;
  final RxList<String> favoriteList;
  final Future<void> Function(String) onToggleFavorite;
  final Future<List<Map>> Function() getFirmsData;
  final Future<bool> Function(String) hasProducts;

  const RestaurantsSection({
    Key? key,
    required this.darkModeController,
    required this.favoriteList,
    required this.onToggleFavorite,
    required this.getFirmsData,
    required this.hasProducts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight20),
          child:
              RestaurantsSectionHeader(darkModeController: darkModeController),
        ),
        const SizedBox(height: SizeConfig.kHeight10),
        RestaurantsList(
          darkModeController: darkModeController,
          favoriteList: favoriteList,
          onToggleFavorite: onToggleFavorite,
          getFirmsData: getFirmsData,
          hasProducts: hasProducts,
        ),
      ],
    );
  }
}

class RestaurantsSectionHeader extends StatelessWidget {
  final DarkModeController darkModeController;

  const RestaurantsSectionHeader({
    Key? key,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          StringConfig.allRestaurants,
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
            fontWeight: FontWeight.w600,
            fontSize: FontConfig.kFontSize18,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () {
            Get.toNamed(AppRoutes.allRestaurant);
          },
          child: Text(
            StringConfig.seeAll,
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistSemiBold,
              color: ColorConfig.kPrimaryColor,
              fontWeight: FontWeight.w600,
              fontSize: FontConfig.kFontSize13,
            ),
          ),
        ),
      ],
    );
  }
}

class RestaurantsList extends StatelessWidget {
  final DarkModeController darkModeController;
  final RxList<String> favoriteList;
  final Future<void> Function(String) onToggleFavorite;
  final Future<List<Map>> Function() getFirmsData;
  final Future<bool> Function(String) hasProducts;

  const RestaurantsList({
    Key? key,
    required this.darkModeController,
    required this.favoriteList,
    required this.onToggleFavorite,
    required this.getFirmsData,
    required this.hasProducts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: getFirmsData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return const Center(child: Text("Veriler alınamadı."));
        } else if (!snapshot.hasData || (snapshot.data as List).isEmpty) {
          return const Center(child: Text("Restoran bulunamadı."));
        }

        var firms = snapshot.data as List;
        // Önce açık firmaları göster
        firms.sort((a, b) {
          bool aVisible = a['visible'] ?? true;
          bool bVisible = b['visible'] ?? true;
          if (aVisible == bVisible) return 0;
          return aVisible ? -1 : 1;
        });

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: firms.length,
            itemBuilder: (context, index) {
              return RestaurantCard(
                firm: firms[index],
                darkModeController: darkModeController,
                favoriteList: favoriteList,
                onToggleFavorite: onToggleFavorite,
                hasProducts: hasProducts,
              );
            },
          ),
        );
      },
    );
  }
}

class RestaurantCard extends StatelessWidget {
  final Map firm;
  final DarkModeController darkModeController;
  final RxList<String> favoriteList;
  final Future<void> Function(String) onToggleFavorite;
  final Future<bool> Function(String) hasProducts;

  const RestaurantCard({
    Key? key,
    required this.firm,
    required this.darkModeController,
    required this.favoriteList,
    required this.onToggleFavorite,
    required this.hasProducts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isVisible = firm['visible'] ?? true;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
      child: GestureDetector(
        onTap: isVisible
            ? () async {
                bool hasProductsResult = await hasProducts(firm['id'] ?? '');
                if (hasProductsResult) {
                  Get.toNamed(AppRoutes.firmDetail,
                      parameters: {'firmId': firm['id'] ?? ''});
                } else {
                  Get.snackbar(
                    "Ürün Yok",
                    "Bu firmada ürün bulunmamaktadır.",
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              }
            : null,
        child: Container(
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kDarkModeColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(15),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Row(
                children: [
                  // Restaurant Image
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomLeft: Radius.circular(8),
                    ),
                    child: Image.network(
                      firm['banner'] ?? 'assets/images/placeholder.png',
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.error_outline),
                        );
                      },
                    ),
                  ),
                  // Restaurant Info
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  firm['name'] ?? "Restoran İsmi Yok",
                                  style: TextStyle(
                                    color: darkModeController.isLightTheme.value
                                        ? ColorConfig.kBlackColor
                                        : ColorConfig.kWhiteColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              // Favorite Button
                              Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: FavoriteButton(
                                  firmId: firm['id'] ?? '',
                                  favoriteList: favoriteList,
                                  onToggleFavorite: onToggleFavorite,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Row(
                            children: [
                              // Status & Min Amount Tags
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: (isVisible
                                      ? Colors.green[100]
                                      : Colors.red[100]),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  isVisible ? 'Açık' : 'Kapalı',
                                  style: TextStyle(
                                    color: isVisible
                                        ? Colors.green[700]
                                        : Colors.red[700],
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFF6600).withAlpha(25),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'Min 100₺',
                                  style: TextStyle(
                                    color: Color(0xFFFF6600),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              // Rating Stars
                              Expanded(
                                child: FutureBuilder<Map<String, dynamic>>(
                                  future:
                                      RestaurantRatingService.calculateRating(
                                          firm['id']),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.waiting) {
                                      return const SizedBox(
                                        height: 12,
                                        width: 12,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      );
                                    }

                                    final rating = (snapshot
                                                .data?['averageRating'] as num?)
                                            ?.toDouble() ??
                                        0.0;
                                    final reviews =
                                        snapshot.data?['totalReviews'] ?? 0;

                                    return Row(
                                      children: [
                                        RatingStars(
                                          rating: rating,
                                          size: 14,
                                          activeColor: const Color(0xFFFF6600),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '($reviews)',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              // Kapalı overlay
              if (!isVisible)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.black.withAlpha(165),
                    ),
                    child: const Center(
                      child: Text(
                        'Kapalı',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class RestaurantRatingService {
  static Future<Map<String, dynamic>> calculateRating(String firmId) async {
    try {
      // Firestore'dan orders collection'ını referans al
      final ordersRef = FirebaseFirestore.instance.collection('orders');
      final ordersSnapshot = await ordersRef.get();

      List<num> ratings = [];

      // Her order dokümanını kontrol et
      for (var orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data();
        final List<dynamic> items = orderData['items'] ?? [];

        // Bu firma için olan ve review'u olan itemları filtrele
        final relevantItems = items.where((item) =>
            item['firmId'] == firmId &&
            item['review'] != null &&
            item['review']['rating'] != null);

        // Bu itemların ratinglerini listeye ekle
        ratings.addAll(
            relevantItems.map((item) => item['review']['rating'] as num));
      }

      if (ratings.isEmpty) {
        return {
          'averageRating': 0.0,
          'totalReviews': 0,
        };
      }

      // Ortalama rating'i hesapla
      double averageRating = ratings.reduce((a, b) => a + b) / ratings.length;

      return {
        'averageRating': double.parse(averageRating.toStringAsFixed(1)),
        'totalReviews': ratings.length,
      };
    } catch (e) {
      return {
        'averageRating': 0.0,
        'totalReviews': 0,
      };
    }
  }
}

class RestaurantImage extends StatelessWidget {
  final String? imageUrl;

  const RestaurantImage({Key? key, this.imageUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(SizeConfig.kHeight12),
      child: Image.network(
        imageUrl ?? 'assets/images/placeholder.png',
        fit: BoxFit.cover,
        height: SizeConfig.kHeight100,
        width: SizeConfig.kHeight100,
      ),
    );
  }
}

class RestaurantInfo extends StatelessWidget {
  final Map firm;
  final DarkModeController darkModeController;

  const RestaurantInfo({
    Key? key,
    required this.firm,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(
          top: SizeConfig.kHeight25,
          left: SizeConfig.kHeight10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              firm['name'] ?? "Restoran İsmi Yok",
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                fontFamily: FontFamilyConfig.urbanistSemiBold,
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kBlackColor
                    : ColorConfig.kWhiteColor,
                fontWeight: FontWeight.w600,
                fontSize: FontConfig.kFontSize16,
              ),
            ),
            const SizedBox(height: SizeConfig.kHeight15),
            Row(
              children: [
                Text(
                  '30 DK',
                  style: TextStyle(
                    fontFamily: FontFamilyConfig.urbanistRegular,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                    fontSize: FontConfig.kFontSize12,
                  ),
                ),
                const SizedBox(width: SizeConfig.kHeight10),
                FutureBuilder<Map<String, dynamic>>(
                  future: RestaurantRatingService.calculateRating(firm['id']),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox(
                        height: SizeConfig.kHeight15,
                        width: SizeConfig.kHeight15,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      );
                    }

                    final rating =
                        (snapshot.data?['averageRating'] as num?)?.toDouble() ??
                            0.0;
                    final reviews = snapshot.data?['totalReviews'] ?? 0;

                    return Row(
                      children: [
                        RatingStars(rating: rating),
                        const SizedBox(width: SizeConfig.kHeight5),
                        Text(
                          '${rating.toStringAsFixed(1)} ($reviews)',
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistRegular,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                            fontSize: FontConfig.kFontSize12,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class FavoriteButton extends StatelessWidget {
  final String firmId;
  final RxList<String> favoriteList;
  final Future<void> Function(String) onToggleFavorite;

  const FavoriteButton({
    Key? key,
    required this.firmId,
    required this.favoriteList,
    required this.onToggleFavorite,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool isFavorite = favoriteList.contains(firmId);
      return GestureDetector(
        onTap: () async {
          await onToggleFavorite(firmId);
        },
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(225),
            shape: BoxShape.circle,
          ),
          child: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            size: 18,
            color: isFavorite ? Colors.red : ColorConfig.kPrimaryColor,
          ),
        ),
      );
    });
  }
}

class ClosedOverlay extends StatelessWidget {
  const ClosedOverlay({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: SizeConfig.kHeight130,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(125),
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
      ),
      child: const Center(
        child: Text(
          "Kapalı",
          style: TextStyle(
            color: Colors.white,
            fontSize: FontConfig.kFontSize18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
