
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/image_path.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';


class AppBarSection extends StatelessWidget {
  final DarkModeController darkModeController;

  const AppBarSection({
    Key? key,
    required this.darkModeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: SizeConfig.kHeight20,
        right: SizeConfig.kHeight12,
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: SizeConfig.kHeight2),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    ImagePath.mapPoint,
                    height: SizeConfig.kHeight16,
                    width: SizeConfig.kHeight16,
                    color: ColorConfig.kPrimaryColor,
                  ),
                  const SizedBox(width: SizeConfig.kHeight5),
                  Text(
                    StringConfig.address,
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistMedium,
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kBlackColor
                          : ColorConfig.kWhiteColor,
                      fontWeight: FontWeight.w600,
                      fontSize: FontConfig.kFontSize16,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Get.toNamed(AppRoutes.notificationHomeScreen);
            },
            child: Image.asset(
              darkModeController.isLightTheme.value
                  ? ImagePath.notification
                  : ImagePath.notificationDark,
              height: SizeConfig.kHeight50,
              width: SizeConfig.kHeight50,
            ),
          ),
        ],
      ),
    );
  }
}
