import 'package:flutter/material.dart';

class RatingStars extends StatelessWidget {
  final double rating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;

  const RatingStars({
    Key? key,
    required this.rating,
    this.size = 15,
    this.activeColor = const Color(0xFFFFB800),
    this.inactiveColor = const Color(0xFFE0E0E0),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        if (index < rating.floor()) {
          return Icon(Icons.star, size: size, color: activeColor);
        } else if (index == rating.floor() && rating % 1 > 0) {
          return Stack(
            children: [
              Icon(Icons.star, size: size, color: inactiveColor),
              ClipRect(
                clipper: _StarClipper(rating % 1),
                child: Icon(Icons.star, size: size, color: activeColor),
              ),
            ],
          );
        }
        return Icon(Icons.star, size: size, color: inactiveColor);
      }),
    );
  }
}

class _StarClipper extends CustomClipper<Rect> {
  final double part;

  _StarClipper(this.part);

  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(0, 0, size.width * part, size.height);
  }

  @override
  bool shouldReclip(_StarClipper oldClipper) {
    return part != oldClipper.part;
  }
}