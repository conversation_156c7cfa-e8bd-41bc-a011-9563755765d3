import 'package:flutter/material.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:get/get.dart';

import '../../config/color.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import '../../utils/appbar_common.dart';

class NotificationHomeScreen extends StatelessWidget {
  final DarkModeController darkModeController = Get.put(DarkModeController());

  NotificationHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kDarkModeColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: StringConfig.notifications,
          leadingImage: 'assets/images/arrow.png',
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () => Get.back(),
        ),
      ),
      body: Center(
        child: Text(
          'Bildiriminiz mevcut değil.',
          style: TextStyle(
            fontSize: 18,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
      ),
    );
  }
}