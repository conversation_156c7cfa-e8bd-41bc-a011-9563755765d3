import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../controller/dark_mode_controller.dart';

class CategoryProductsPage extends StatelessWidget {
  final String categoryName;
  final String categoryId;

   CategoryProductsPage({
    super.key,
    required this.categoryName,
    required this.categoryId,
  });

  final DarkModeController darkModeController = Get.put(DarkModeController());

  Future<List<Map<String, dynamic>>> getFirmsWithCategory() async {

    QuerySnapshot firmSnapshot = await FirebaseFirestore.instance
        .collection('firms')
        .where('categories', arrayContains: categoryId)
        .get();

    List<Map<String, dynamic>> firmsWithCategory = [];

    for (var firmDoc in firmSnapshot.docs) {
      var firmData = firmDoc.data() as Map<String, dynamic>;
      firmData['id'] = firmDoc.id;
      firmsWithCategory.add(firmData);
    }


    return firmsWithCategory;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: AppBar(
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kBlackColor,
        title: Text(
          categoryName,
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            fontSize: FontConfig.kFontSize18,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
      ),
      body: FutureBuilder(
        future: getFirmsWithCategory(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text("Firmalar alınamadı."));
          } else {
            var firms = snapshot.data as List<Map<String, dynamic>>;
            return ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: firms.length,
              itemBuilder: (context, index) {
                var firm = firms[index];
                bool isVisible = firm['visible'] ?? true;
                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: darkModeController.isLightTheme.value
                        ? Colors.white
                        : ColorConfig.kDarkModeColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      ListTile(
                        contentPadding: const EdgeInsets.all(12),
                        leading: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            firm['banner'] ?? 'assets/images/placeholder.png',
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                          ),
                        ),
                        title: Text(
                          firm['name'] ?? 'Firma Adı',
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistSemiBold,
                            fontSize: FontConfig.kFontSize18,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                          ),
                        ),
                        subtitle: Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            firm['description'] ?? "Açıklama mevcut değil.",
                            style: TextStyle(
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kHintColor
                                  : ColorConfig.kWhiteColor.withAlpha(150),
                            ),
                          ),
                        ),
                        onTap: isVisible ? () {
                          Get.toNamed(AppRoutes.firmDetail,
                              parameters: {'firmId': firm['id']});
                        } : null,
                      ),
                      if (!isVisible)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withAlpha(125),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Center(
                              child: Text(
                                "Kapalı",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: FontConfig.kFontSize18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            );
          }
        },
      ),
    ));
  }
}