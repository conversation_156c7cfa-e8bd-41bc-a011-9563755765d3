// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:yemekkapimda/controller/setting_controller.dart';
import 'package:get/get.dart';

import '../../../../config/color.dart';
import '../../../../config/font_config.dart';
import '../../../../config/font_family.dart';
import '../../../../config/image_path.dart';
import '../../../../config/size_config.dart';
import '../../../../utils/appbar_common.dart';

class TermsServiceScreen extends StatelessWidget {
  TermsServiceScreen({Key? key}) : super(key: key);
  final SettingController settingController = Get.put(SettingController());
  DarkModeController darkModeController = Get.put(DarkModeController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => SafeArea(
        child: Scaffold(
            backgroundColor: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kBlackColor,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(SizeConfig.kHeight70),
                child: CommonAppBar(
                  title: "Gizlilik ve Güvenlik Politikası",
                  leadingImage: ImagePath.arrow,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                  leadingOnTap: () {
                    Get.back();
                  },
                )),
            body: SingleChildScrollView(
                child: Obx(() => Padding(
                      padding: EdgeInsets.only(
                        right: settingController.arb.value
                            ? SizeConfig.kHeight12
                            : 0,
                        left: settingController.arb.value
                            ? 0
                            : SizeConfig.kHeight12,
                        bottom: SizeConfig.kHeight10,
                        top: SizeConfig.kHeight10,
                      ),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text("Gizlilik ve Güvenlik Politikası",
                                style: TextStyle(
                                  fontFamily: FontFamilyConfig.urbanistSemiBold,
                                  color: darkModeController.isLightTheme.value
                                      ? ColorConfig.kBlackColor
                                      : ColorConfig.kWhiteColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: FontConfig.kFontSize18,
                                )),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: SizeConfig.kHeight12,
                                  top: SizeConfig.kHeight5,
                                  bottom: SizeConfig.kHeight15),
                              child: Text(
                                  "Mağazamızda verilen tüm servisler ve Hacı Seyit Ali mahallesi 153086 sok no 20 c Seydişehir Konya adresinde kayıtlı Abdullah Ünal firmamıza aittir ve firmamız tarafından işletilir.",
                                  style: TextStyle(
                                    fontFamily:
                                        FontFamilyConfig.urbanistRegular,
                                    color: darkModeController.isLightTheme.value
                                        ? ColorConfig.kHintColor
                                        : ColorConfig.kDividerColor,
                                    fontWeight: FontWeight.w400,
                                    fontSize: FontConfig.kFontSize15,
                                  )),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: SizeConfig.kHeight10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                      "Kişisel Verilerin Toplanması ve Korunması",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistSemiBold,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kBlackColor
                                                .withAlpha(225)
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w600,
                                        fontSize: FontConfig.kFontSize16,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: SizeConfig.kHeight30),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("•",
                                            style: TextStyle(
                                              fontFamily: FontFamilyConfig
                                                  .urbanistRegular,
                                              color: darkModeController
                                                      .isLightTheme.value
                                                  ? ColorConfig.kBlackColor
                                                  : ColorConfig.kWhiteColor,
                                              fontWeight: FontWeight.w400,
                                              fontSize: FontConfig.kFontSize15,
                                            )),
                                        const SizedBox(
                                          width: SizeConfig.kHeight7,
                                        ),
                                        Expanded(
                                          child: Text(
                                              "Firmamız, çeşitli amaçlarla kişisel veriler toplayabilir. Üyelik veya mağazamız üzerindeki form ve anketlerin doldurulmasıyla kişisel bilgiler toplanır ve korunur.",
                                              style: TextStyle(
                                                fontFamily: FontFamilyConfig
                                                    .urbanistRegular,
                                                color: darkModeController
                                                        .isLightTheme.value
                                                    ? ColorConfig.kHintColor
                                                    : ColorConfig.kWhiteColor
                                                        .withAlpha(175),
                                                fontWeight: FontWeight.w400,
                                                fontSize:
                                                    FontConfig.kFontSize15,
                                              )),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: SizeConfig.kHeight30,
                                        bottom: SizeConfig.kHeight30),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("•",
                                            style: TextStyle(
                                              fontFamily: FontFamilyConfig
                                                  .urbanistRegular,
                                              color: darkModeController
                                                      .isLightTheme.value
                                                  ? ColorConfig.kBlackColor
                                                  : ColorConfig.kWhiteColor,
                                              fontWeight: FontWeight.w400,
                                              fontSize: FontConfig.kFontSize15,
                                            )),
                                        const SizedBox(
                                          width: SizeConfig.kHeight7,
                                        ),
                                        Expanded(
                                          child: Text(
                                              "Firmamız, kredi kartı bilgilerinin güvenliğini ön planda tutmaktadır. Kredi kartı bilgileri hiçbir şekilde sistemimizde saklanmamaktadır.",
                                              style: TextStyle(
                                                fontFamily: FontFamilyConfig
                                                    .urbanistRegular,
                                                color: darkModeController
                                                        .isLightTheme.value
                                                    ? ColorConfig.kHintColor
                                                    : ColorConfig.kWhiteColor
                                                        .withAlpha(175),
                                                fontWeight: FontWeight.w400,
                                                fontSize:
                                                    FontConfig.kFontSize15,
                                              )),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text("Üçüncü Taraf Web Siteleri",
                                style: TextStyle(
                                  fontFamily: FontFamilyConfig.urbanistSemiBold,
                                  color: darkModeController.isLightTheme.value
                                      ? ColorConfig.kBlackColor
                                      : ColorConfig.kWhiteColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: FontConfig.kFontSize18,
                                )),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: SizeConfig.kHeight10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text(
                                      "Mağazamız, web sitesi içinde başka sitelere link verebilir. Firmamız, bu linkler vasıtasıyla erişilen sitelerin gizlilik uygulamaları ve içeriklerine yönelik sorumluluk taşımamaktadır.",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text("E-Posta Güvenliği",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text(
                                      "Müşteri hizmetlerine gönderilen e-postalarda kredi kartı numaranızı veya şifrenizi yazmayınız. Firmamız, e-postalarınızdan aktarılan bilgilerin güvenliğini garanti edemez.",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text("Çerez Kullanımı",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text(
                                      "Mağazamızı ziyaret eden kullanıcıların web sitesi kullanımı hakkındaki bilgileri çerezler ile elde edilebilir. Çerezler, İnternet'in kullanımını kolaylaştırır.",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text("Gizlilik Politikası Değişiklikleri",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text(
                                      "Firmamız, gizlilik politikası hükümlerini dilediği zaman değiştirme hakkını saklı tutar. Gizlilik politikası hükümleri değiştiği takdirde, yayınlandığı tarihte yürürlüğe girer.",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text("İletişim",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text(
                                      "Gizlilik politikamız ile ilgili sorularınız için <EMAIL> adresine email gönderebilirsiniz.",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                  const SizedBox(
                                    height: SizeConfig.kHeight7,
                                  ),
                                  Text("<EMAIL> +90 543 582 0442",
                                      style: TextStyle(
                                        fontFamily:
                                            FontFamilyConfig.urbanistRegular,
                                        color: darkModeController
                                                .isLightTheme.value
                                            ? ColorConfig.kHintColor
                                            : ColorConfig.kWhiteColor
                                                .withAlpha(175),
                                        fontWeight: FontWeight.w400,
                                        fontSize: FontConfig.kFontSize15,
                                      )),
                                ],
                              ),
                            ),
                          ]),
                    ))))));
  }
}
