import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../app_routes/app_routes.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AllCategories extends StatelessWidget {
  final DarkModeController darkModeController = Get.find<DarkModeController>();

  AllCategories({Key? key}) : super(key: key);

  Future<Map<String, Map<String, dynamic>>> getAllCategories() async {
    Map<String, Map<String, dynamic>> allCategoriesMap = {};

    QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('categories')
        .orderBy('name') // İsimlere göre sıralama
        .get();

    for (var doc in snapshot.docs) {
      var data = doc.data() as Map<String, dynamic>?;
      if (data != null) {
        allCategoriesMap[doc.id] = {
          'name': data['name'] ?? 'Kategori Bilinmiyor',
          'imageUrl':
              data['imageUrl'] ?? 'assets/images/category_placeholder.png'
        };
      }
    }

    return allCategoriesMap;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: AppBar(
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kBlackColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          "Tüm Kategoriler",
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            fontSize: FontConfig.kFontSize18,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
      ),
      body: FutureBuilder<Map<String, Map<String, dynamic>>>(
        future: getAllCategories(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return const Center(
                child: Text('Kategoriler yüklenirken hata oluştu'));
          }

          final categoriesMap = snapshot.data ?? {};

          return GridView.builder(
            padding: const EdgeInsets.all(SizeConfig.kHeight20),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.8,
              crossAxisSpacing: SizeConfig.kHeight16,
              mainAxisSpacing: SizeConfig.kHeight16,
            ),
            itemCount: categoriesMap.length,
            itemBuilder: (context, index) {
              String categoryId = categoriesMap.keys.elementAt(index);
              String categoryName =
                  categoriesMap[categoryId]?['name'] ?? "Kategori Yok";
              String categoryImageUrl = categoriesMap[categoryId]
                      ?['imageUrl'] ??
                  'assets/images/category_placeholder.png';

              return GestureDetector(
                onTap: () {
                  Get.toNamed(
                    AppRoutes.categoryProducts,
                    parameters: {
                      'categoryName': categoryName,
                      'categoryId': categoryId,
                    },
                  );
                },
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: ColorConfig.kPrimaryColor.withAlpha(50),
                            width: 1,
                          ),
                        ),
                        child: ClipOval(
                          child: Image.network(
                            categoryImageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[300],
                                child: const Icon(Icons.error),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: SizeConfig.kHeight8),
                    Text(
                      categoryName,
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                        fontWeight: FontWeight.w600,
                        fontSize: FontConfig.kFontSize14,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
