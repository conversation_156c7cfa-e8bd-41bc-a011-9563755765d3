import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../utils/appbar_common.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:cupertino_will_pop_scope/cupertino_will_pop_scope.dart';
import 'package:yemekkapimda/app_routes/app_routes.dart';
import 'dart:async';

// Profil Menü Öğesi Widget'ı
class ProfileMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final Color iconColor;
  final VoidCallback onTap;
  final bool isDarkMode;

  const ProfileMenuItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.iconColor,
    required this.onTap,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SizeConfig.kHeight8),
        child: Padding(
          padding: const EdgeInsets.only(
            top: SizeConfig.kHeight5,
            bottom: SizeConfig.kHeight10,
            left: SizeConfig.kHeight17,
            right: SizeConfig.kHeight17,
          ),
          child: Column(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: SizeConfig.kHeight12),
                child: Row(
                  children: [
                    Icon(icon, size: SizeConfig.kHeight22, color: iconColor),
                    const SizedBox(width: SizeConfig.kHeight22),
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        color: title == "Çıkış Yap"
                            ? ColorConfig.kPrimaryColor
                            : (isDarkMode
                                ? ColorConfig.kWhiteColor
                                : ColorConfig.kBlackColor),
                        fontWeight: FontWeight.w600,
                        fontSize: FontConfig.kFontSize18,
                      ),
                    ),
                    if (title != "Çıkış Yap") ...[
                      const Spacer(),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: isDarkMode
                            ? ColorConfig.kWhiteColor
                            : ColorConfig.kBlackColor,
                        size: SizeConfig.kHeight18,
                      ),
                    ],
                  ],
                ),
              ),
              if (title != "Çıkış Yap")
                Divider(
                  color: isDarkMode
                      ? ColorConfig.kDarkModeDividerColor.withAlpha(175)
                      : ColorConfig.kHintColor.withAlpha(50)
                ),
            ],
          ),
        ),
      ),
    );
  }
}

// Profil Kartı Widget'ı
class ProfileCard extends StatelessWidget {
  final String firstName;
  final String lastName;
  final bool isDarkMode;
  final VoidCallback onEditTap;

  const ProfileCard({
    Key? key,
    required this.firstName,
    required this.lastName,
    required this.isDarkMode,
    required this.onEditTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight15),
      child: GestureDetector(
        onTap: onEditTap,
        child: Container(
          height: MediaQuery.of(context).size.height / 5,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SizeConfig.kHeight12),
            color:
                isDarkMode ? ColorConfig.kBlackColor : ColorConfig.kWhiteColor,
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.grey.withAlpha(25)
                    : Colors.grey.withAlpha(50),
                blurRadius: 10,
                spreadRadius: SizeConfig.kHeight2,
                offset: const Offset(0.0, 0.0),
              ),
            ],
          ),
          child: Stack(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  const SizedBox(width: SizeConfig.kHeight50),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "$firstName $lastName",
                        style: TextStyle(
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          color: isDarkMode
                              ? ColorConfig.kWhiteColor
                              : ColorConfig.kBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: FontConfig.kFontSize22,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Positioned(
                top: 10,
                right: 10,
                child: GestureDetector(
                  onTap: onEditTap,
                  child: Icon(
                    Icons.edit,
                    color: isDarkMode
                        ? ColorConfig.kWhiteColor
                        : ColorConfig.kBlackColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Ana Profil Ekranı
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final _refreshStream = StreamController<void>.broadcast();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (auth.currentUser == null) {
        Get.offAllNamed(AppRoutes.signInScreen);
      }
    });
  }

  @override
  void dispose() {
    _refreshStream.close();
    super.dispose();
  }

  Future<Map<String, dynamic>> getUserData() async {
    try {
      User? user = auth.currentUser;
      if (user == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAllNamed(AppRoutes.signInScreen);
        });
        return {};
      }

      DocumentSnapshot userData =
          await firestore.collection('users').doc(user.uid).get();

      if (!userData.exists) {
        return {};
      }

      Map<String, dynamic>? data = userData.data() as Map<String, dynamic>?;
      return data ?? {};
    } catch (e) {
      log("Error getting user data: $e");
      return {};
    }
  }

  void refreshProfile() {
    _refreshStream.add(null);
  }

  @override
  Widget build(BuildContext context) {
    if (auth.currentUser == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAllNamed(AppRoutes.signInScreen);
      });
      return const SizedBox();
    }

    return ConditionalWillPopScope(
      onWillPop: () {
        Get.offAllNamed(AppRoutes.homePage);
        return Future(() => false);
      },
      shouldAddCallback: false,
      child: SafeArea(
        child: StreamBuilder<void>(
          stream: _refreshStream.stream,
          builder: (context, _) {
            return FutureBuilder<Map<String, dynamic>>(
              future: getUserData(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text("Error: ${snapshot.error}"));
                } else if (!snapshot.hasData) {
                  return const Center(child: Text("No user data found"));
                }

                var userData = snapshot.data!;
                String firstName = userData['firstName'] ?? 'Unknown';
                String lastName = userData['lastName'] ?? 'User';
                bool isDarkMode = !darkModeController.isLightTheme.value;

                return Scaffold(
                  backgroundColor: isDarkMode
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                  appBar: PreferredSize(
                    preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
                    child: CommonAppBar(
                      title: "Profil",
                      leadingImage: ImagePath.restarantMenu,
                      leadingOnTap: () {},
                    ),
                  ),
                  body: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: SizeConfig.kHeight20,
                        vertical: SizeConfig.kHeight24,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ProfileCard(
                            firstName: firstName,
                            lastName: lastName,
                            isDarkMode: isDarkMode,
                            onEditTap: () async {
                              await Get.toNamed(AppRoutes.editProfileView);
                              refreshProfile(); // Profili güncelle
                            },
                          ),
                          const SizedBox(height: SizeConfig.kHeight20),
                          ProfileMenuItem(
                            icon: Icons.location_on,
                            title: "Adres",
                            iconColor: ColorConfig.kBlackColor,
                            isDarkMode: isDarkMode,
                            onTap: () => Get.toNamed(AppRoutes.address),
                          ),
                          ProfileMenuItem(
                            icon: Icons.settings,
                            title: "Ayarlar",
                            iconColor: ColorConfig.kBlackColor,
                            isDarkMode: isDarkMode,
                            onTap: () => Get.toNamed(AppRoutes.settings),
                          ),
                          ProfileMenuItem(
                            icon: Icons.logout,
                            title: "Çıkış Yap",
                            iconColor: ColorConfig.kPrimaryColor,
                            isDarkMode: isDarkMode,
                            onTap: () {
                              auth.signOut();
                              Get.offAllNamed(AppRoutes.signUpScreen);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
