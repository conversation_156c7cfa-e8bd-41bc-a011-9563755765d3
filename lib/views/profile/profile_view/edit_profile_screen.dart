import 'package:flutter/material.dart';
import 'package:yemekkapimda/controller/edit_profile_controller.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../app_routes/app_routes.dart';
import '../../../config/color.dart';
import '../../../config/font_config.dart';
import '../../../config/font_family.dart';
import '../../../config/image_path.dart';
import '../../../config/size_config.dart';
import '../../../config/string_config.dart';
import '../../../controller/dark_mode_controller.dart';
import '../../../utils/appbar_common.dart';
import '../../../utils/common_material_button.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final EditProfileController editProfileController =
      Get.put(EditProfileController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  bool _isSaving = false;

  Future<Map<String, dynamic>?> _loadUserData() async {
    try {
      User? user = auth.currentUser;
      if (user != null) {
        DocumentSnapshot userData =
            await firestore.collection('users').doc(user.uid).get();
        if (userData.exists) {
          return userData.data() as Map<String, dynamic>;
        }
      }
      return null;
    } catch (e) {
      Get.snackbar(
        'Hata',
        'Kullanıcı bilgileri yüklenirken bir hata oluştu',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  Future<void> _saveUserData() async {
    setState(() {
      _isSaving = true;
    });

    try {
      User? user = auth.currentUser;
      if (user != null) {
        await firestore.collection('users').doc(user.uid).update({
          'firstName': editProfileController.firstNameController.text,
          'lastName': editProfileController.lastNameController.text,
          'email': editProfileController.emailController.text,
        });

        Get.snackbar(
          "Başarılı",
          "Profil bilgileri güncellendi",
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        Get.back(result: true);
      }
    } catch (e) {
      Get.snackbar(
        "Hata",
        "Bilgiler güncellenirken bir hata oluştu",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _showDeleteAccountDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        title: Text('Hesabı Sil',
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistSemiBold,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            )),
        content: Text(
            'Hesabınız kalıcı olarak silinecektir. Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistRegular,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            )),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('İptal',
                style: TextStyle(
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                )),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _performAccountDeletion();
            },
            child: const Text('Sil',
                style: TextStyle(
                  color: Colors.red,
                )),
          ),
        ],
      ),
    );
  }

  Future<void> _performAccountDeletion() async {
    try {
      User? user = auth.currentUser;
      if (user != null) {
        await _reauthenticateUser(user);
      }
    } catch (e) {
      Get.snackbar(
        'Hata',
        'Beklenmeyen bir hata oluştu: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _reauthenticateUser(User user) async {
    try {
      final phoneNumber = user.phoneNumber;
      if (phoneNumber == null) {
        throw Exception('Telefon numarası bulunamadı');
      }

      await auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            await user.reauthenticateWithCredential(credential);
          } catch (e) {
            Get.snackbar(
              'Hata',
              'Kimlik doğrulama başarısız: ${e.toString()}',
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          Get.snackbar(
            'Hata',
            'Doğrulama başarısız: ${e.message}',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          Get.toNamed(AppRoutes.otpScreen, arguments: {
            'verificationId': verificationId,
            'isReauthentication': true,
          });
        },
        codeAutoRetrievalTimeout: (String verificationId) {},
      );
    } catch (e) {
      Get.snackbar(
        'Hata',
        'Doğrulama işlemi başlatılamadı: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    bool readOnly = false,
  }) {
    return TextFormField(
      controller: controller,
      readOnly: readOnly,
      cursorColor: ColorConfig.kTextfieldTextColor,
      style: TextStyle(
        fontSize: FontConfig.kFontSize14,
        fontFamily: FontFamilyConfig.urbanistSemiBold,
        fontWeight: FontWeight.w600,
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kBlackColor
            : ColorConfig.kWhiteColor,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          fontSize: FontConfig.kFontSize14,
          fontFamily: FontFamilyConfig.urbanistRegular,
          fontWeight: FontWeight.w400,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kTextfieldTextColor
              : ColorConfig.kTextFieldLightTextColor,
        ),
        filled: true,
        fillColor: darkModeController.isLightTheme.value
            ? ColorConfig.kBgColor.withAlpha(50)
            : ColorConfig.kDarkModeColor,
        border: UnderlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(SizeConfig.borderRadius8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: StringConfig.editProfile,
          leadingImage: ImagePath.arrow,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () => Get.back(),
        ),
      ),
      body: FutureBuilder<Map<String, dynamic>?>(
        future: _loadUserData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text("Hata: ${snapshot.error}"));
          }

          if (!snapshot.hasData || snapshot.data == null) {
            return const Center(child: Text("Kullanıcı verisi bulunamadı"));
          }

          var userData = snapshot.data!;
          editProfileController.firstNameController.text =
              userData['firstName'] ?? '';
          editProfileController.lastNameController.text =
              userData['lastName'] ?? '';
          editProfileController.emailController.text = userData['email'] ?? '';
          editProfileController.phoneNumberController.text =
              auth.currentUser?.phoneNumber ?? '';

          return Padding(
            padding: const EdgeInsets.only(top: SizeConfig.padding10),
            child: SingleChildScrollView(
              child: Form(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: SizeConfig.padding20,
                      ),
                      child: Column(
                        children: [
                          _buildTextField(
                            controller:
                                editProfileController.firstNameController,
                            hintText: StringConfig.firstName,
                          ),
                          const SizedBox(height: SizeConfig.kHeight24),
                          _buildTextField(
                            controller:
                                editProfileController.lastNameController,
                            hintText: StringConfig.lastName,
                          ),
                          const SizedBox(height: SizeConfig.kHeight24),
                          _buildTextField(
                            controller:
                                editProfileController.phoneNumberController,
                            hintText: StringConfig.phoneNumber,
                            readOnly: true,
                          ),
                          const SizedBox(height: SizeConfig.kHeight24),
                          _buildTextField(
                            controller: editProfileController.emailController,
                            hintText: StringConfig.email,
                          ),
                          const SizedBox(height: SizeConfig.kHeight24),
                          CommonMaterialButton(
                            buttonColor: Colors.red,
                            height: SizeConfig.kHeight52,
                            txtColor: ColorConfig.kWhiteColor,
                            buttonText: "Hesabı Sil",
                            onButtonClick: _showDeleteAccountDialog,
                            width: double.infinity,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: SizedBox(
        height: SizeConfig.kHeight80,
        child: Center(
          child: _isSaving
              ? const CircularProgressIndicator()
              : CommonMaterialButton(
                  buttonColor: ColorConfig.kPrimaryColor,
                  height: SizeConfig.kHeight52,
                  txtColor: ColorConfig.kWhiteColor,
                  buttonText: StringConfig.saveButton,
                  onButtonClick: _saveUserData,
                  width: double.infinity,
                ),
        ),
      ),
    );
  }
}
