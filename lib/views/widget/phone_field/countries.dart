// see: https://en.wikipedia.org/wiki/List_of_country_calling_codes
// for list of country/calling codes

const List<Country> countries = [
  Country(
    name: "Afghanistan",
    flag: "🇦🇫",
    code: "AF",
    dialCode: "93",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Åland Islands",
    flag: "🇦🇽",
    code: "AX",
    dialCode: "358",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Albania",
    flag: "🇦🇱",
    code: "AL",
    dialCode: "355",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Algeria",
    flag: "🇩🇿",
    code: "DZ",
    dialCode: "213",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "American Samoa",
    flag: "🇦🇸",
    code: "AS",
    dialCode: "1684",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Andorra",
    flag: "🇦🇩",
    code: "AD",
    dialCode: "376",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Angola",
    flag: "🇦🇴",
    code: "AO",
    dialCode: "244",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "<PERSON><PERSON><PERSON>",
    flag: "🇦🇮",
    code: "AI",
    dialCode: "1264",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Antarctica",
    flag: "🇦🇶",
    code: "AQ",
    dialCode: "672",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Antigua and Barbuda",
    flag: "🇦🇬",
    code: "AG",
    dialCode: "1268",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Argentina",
    flag: "🇦🇷",
    code: "AR",
    dialCode: "54",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Armenia",
    flag: "🇦🇲",
    code: "AM",
    dialCode: "374",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Aruba",
    flag: "🇦🇼",
    code: "AW",
    dialCode: "297",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Australia",
    flag: "🇦🇺",
    code: "AU",
    dialCode: "61",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Austria",
    flag: "🇦🇹",
    code: "AT",
    dialCode: "43",
    minLength: 13,
    maxLength: 13,
  ),
  Country(
    name: "Azerbaijan",
    flag: "🇦🇿",
    code: "AZ",
    dialCode: "994",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Bahamas",
    flag: "🇧🇸",
    code: "BS",
    dialCode: "1242",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bahrain",
    flag: "🇧🇭",
    code: "BH",
    dialCode: "973",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bangladesh",
    flag: "🇧🇩",
    code: "BD",
    dialCode: "880",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Barbados",
    flag: "🇧🇧",
    code: "BB",
    dialCode: "1246",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Belarus",
    flag: "🇧🇾",
    code: "BY",
    dialCode: "375",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Belgium",
    flag: "🇧🇪",
    code: "BE",
    dialCode: "32",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Belize",
    flag: "🇧🇿",
    code: "BZ",
    dialCode: "501",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Benin",
    flag: "🇧🇯",
    code: "BJ",
    dialCode: "229",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bermuda",
    flag: "🇧🇲",
    code: "BM",
    dialCode: "1441",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bhutan",
    flag: "🇧🇹",
    code: "BT",
    dialCode: "975",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bolivia",
    flag: "🇧🇴",
    code: "BO",
    dialCode: "591",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bosnia and Herzegovina",
    flag: "🇧🇦",
    code: "BA",
    dialCode: "387",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Botswana",
    flag: "🇧🇼",
    code: "BW",
    dialCode: "267",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Bouvet Island",
    flag: "🇧🇻",
    code: "BV",
    dialCode: "47",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Brazil",
    flag: "🇧🇷",
    code: "BR",
    dialCode: "55",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "British Indian Ocean Territory",
    flag: "🇮🇴",
    code: "IO",
    dialCode: "246",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Brunei Darussalam",
    flag: "🇧🇳",
    code: "BN",
    dialCode: "673",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Bulgaria",
    flag: "🇧🇬",
    code: "BG",
    dialCode: "359",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Burkina Faso",
    flag: "🇧🇫",
    code: "BF",
    dialCode: "226",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Burundi",
    flag: "🇧🇮",
    code: "BI",
    dialCode: "257",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Cambodia",
    flag: "🇰🇭",
    code: "KH",
    dialCode: "855",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Cameroon",
    flag: "🇨🇲",
    code: "CM",
    dialCode: "237",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Campione d'Italia",
    flag: "🇮🇹",
    code: "IT",
    dialCode: "41",
    regionCode: "91",
    minLength: 13,
    maxLength: 13,
  ),
  Country(
    name: "Canada",
    flag: "🇨🇦",
    code: "CA",
    dialCode: "1",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Cape Verde",
    flag: "🇨🇻",
    code: "CV",
    dialCode: "238",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Cayman Islands",
    flag: "🇰🇾",
    code: "KY",
    dialCode: "345",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Central African Republic",
    flag: "🇨🇫",
    code: "CF",
    dialCode: "236",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Chad",
    flag: "🇹🇩",
    code: "TD",
    dialCode: "235",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Chile",
    flag: "🇨🇱",
    code: "CL",
    dialCode: "56",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "China",
    flag: "🇨🇳",
    code: "CN",
    dialCode: "86",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Christmas Island",
    flag: "🇨🇽",
    code: "CX",
    dialCode: "61",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Cocos (Keeling) Islands",
    flag: "🇨🇨",
    code: "CC",
    dialCode: "61",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Colombia",
    flag: "🇨🇴",
    code: "CO",
    dialCode: "57",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Comoros",
    flag: "🇰🇲",
    code: "KM",
    dialCode: "269",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Congo",
    flag: "🇨🇬",
    code: "CG",
    dialCode: "242",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Congo",
    flag: "🇨🇩",
    code: "CD",
    dialCode: "243",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Cook Islands",
    flag: "🇨🇰",
    code: "CK",
    dialCode: "682",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Costa Rica",
    flag: "🇨🇷",
    code: "CR",
    dialCode: "506",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Côte d'Ivoire",
    flag: "🇨🇮",
    code: "CI",
    dialCode: "225",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Croatia",
    flag: "🇭🇷",
    code: "HR",
    dialCode: "385",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Cuba",
    flag: "🇨🇺",
    code: "CU",
    dialCode: "53",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Cyprus",
    flag: "🇨🇾",
    code: "CY",
    dialCode: "357",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Czech Republic",
    flag: "🇨🇿",
    code: "CZ",
    dialCode: "420",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Denmark",
    flag: "🇩🇰",
    code: "DK",
    dialCode: "45",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Djibouti",
    flag: "🇩🇯",
    code: "DJ",
    dialCode: "253",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Dominica",
    flag: "🇩🇲",
    code: "DM",
    dialCode: "1767",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Dominican Republic",
    flag: "🇩🇴",
    code: "DO",
    dialCode: "1849",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Ecuador",
    flag: "🇪🇨",
    code: "EC",
    dialCode: "593",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Egypt",
    flag: "🇪🇬",
    code: "EG",
    dialCode: "20",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "El Salvador",
    flag: "🇸🇻",
    code: "SV",
    dialCode: "503",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Equatorial Guinea",
    flag: "🇬🇶",
    code: "GQ",
    dialCode: "240",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Eritrea",
    flag: "🇪🇷",
    code: "ER",
    dialCode: "291",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Estonia",
    flag: "🇪🇪",
    code: "EE",
    dialCode: "372",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Ethiopia",
    flag: "🇪🇹",
    code: "ET",
    dialCode: "251",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Falkland Islands",
    flag: "🇫🇰",
    code: "FK",
    dialCode: "500",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Faroe Islands",
    flag: "🇫🇴",
    code: "FO",
    dialCode: "298",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Fiji",
    flag: "🇫🇯",
    code: "FJ",
    dialCode: "679",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Finland",
    flag: "🇫🇮",
    code: "FI",
    dialCode: "358",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "France",
    flag: "🇫🇷",
    code: "FR",
    dialCode: "33",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "French Guiana",
    flag: "🇬🇫",
    code: "GF",
    dialCode: "594",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "French Polynesia",
    flag: "🇵🇫",
    code: "PF",
    dialCode: "689",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "French Southern Territories",
    flag: "🇹🇫",
    code: "TF",
    dialCode: "262",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Gabon",
    flag: "🇬🇦",
    code: "GA",
    dialCode: "241",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Gambia",
    flag: "🇬🇲",
    code: "GM",
    dialCode: "220",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Georgia",
    flag: "🇬🇪",
    code: "GE",
    dialCode: "995",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Germany",
    flag: "🇩🇪",
    code: "DE",
    dialCode: "49",
    minLength: 9,
    maxLength: 13,
  ),
  Country(
    name: "Ghana",
    flag: "🇬🇭",
    code: "GH",
    dialCode: "233",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Gibraltar",
    flag: "🇬🇮",
    code: "GI",
    dialCode: "350",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Greece",
    flag: "🇬🇷",
    code: "GR",
    dialCode: "30",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Greenland",
    flag: "🇬🇱",
    code: "GL",
    dialCode: "299",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Grenada",
    flag: "🇬🇩",
    code: "GD",
    dialCode: "1473",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guadeloupe",
    flag: "🇬🇵",
    code: "GP",
    dialCode: "590",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Guam",
    flag: "🇬🇺",
    code: "GU",
    dialCode: "1671",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guatemala",
    flag: "🇬🇹",
    code: "GT",
    dialCode: "502",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Guernsey",
    flag: "🇬🇬",
    code: "GG",
    dialCode: "44",
    regionCode: "1481",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Guinea",
    flag: "🇬🇳",
    code: "GN",
    dialCode: "224",
    minLength: 8,
    maxLength: 9,
  ),
  Country(
    name: "Guinea-Bissau",
    flag: "🇬🇼",
    code: "GW",
    dialCode: "245",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Guyana",
    flag: "🇬🇾",
    code: "GY",
    dialCode: "592",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Haiti",
    flag: "🇭🇹",
    code: "HT",
    dialCode: "509",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Heard Island",
    flag: "🇭🇲",
    code: "HM",
    dialCode: "672",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Holy See",
    flag: "🇻🇦",
    code: "VA",
    dialCode: "379",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Honduras",
    flag: "🇭🇳",
    code: "HN",
    dialCode: "504",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Hong Kong",
    flag: "🇭🇰",
    code: "HK",
    dialCode: "852",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Hungary",
    flag: "🇭🇺",
    code: "HU",
    dialCode: "36",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Iceland",
    flag: "🇮🇸",
    code: "IS",
    dialCode: "354",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "India",
    flag: "🇮🇳",
    code: "IN",
    dialCode: "91",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Indonesia",
    flag: "🇮🇩",
    code: "ID",
    dialCode: "62",
    minLength: 10,
    maxLength: 13,
  ),
  Country(
    name: "Iran",
    flag: "🇮🇷",
    code: "IR",
    dialCode: "98",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Iraq",
    flag: "🇮🇶",
    code: "IQ",
    dialCode: "964",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Ireland",
    flag: "🇮🇪",
    code: "IE",
    dialCode: "353",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "Isle of Man",
    flag: "🇮🇲",
    code: "IM",
    dialCode: "44",
    regionCode: "1624",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Israel",
    flag: "🇮🇱",
    code: "IL",
    dialCode: "972",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Italy",
    flag: "🇮🇹",
    code: "IT",
    dialCode: "39",
    minLength: 13,
    maxLength: 13,
  ),
  Country(
    name: "Jamaica",
    flag: "🇯🇲",
    code: "JM",
    dialCode: "1876",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Japan",
    flag: "🇯🇵",
    code: "JP",
    dialCode: "81",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Jersey",
    flag: "🇯🇪",
    code: "JE",
    dialCode: "44",
    regionCode: "1534",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Jordan",
    flag: "🇯🇴",
    code: "JO",
    dialCode: "962",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Kazakhstan",
    flag: "🇰🇿",
    code: "KZ",
    dialCode: "7",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Kenya",
    flag: "🇰🇪",
    code: "KE",
    dialCode: "254",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Kiribati",
    flag: "🇰🇮",
    code: "KI",
    dialCode: "686",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Korea",
    flag: "🇰🇵",
    code: "KP",
    dialCode: "850",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Korea",
    flag: "🇰🇷",
    code: "KR",
    dialCode: "82",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Kosovo",
    flag: "🇽🇰",
    code: "XK",
    dialCode: "383",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Kuwait",
    flag: "🇰🇼",
    code: "KW",
    dialCode: "965",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Kyrgyzstan",
    flag: "🇰🇬",
    code: "KG",
    dialCode: "996",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Laos",
    flag: "🇱🇦",
    code: "LA",
    dialCode: "856",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Latvia",
    flag: "🇱🇻",
    code: "LV",
    dialCode: "371",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Lebanon",
    flag: "🇱🇧",
    code: "LB",
    dialCode: "961",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Lesotho",
    flag: "🇱🇸",
    code: "LS",
    dialCode: "266",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Liberia",
    flag: "🇱🇷",
    code: "LR",
    dialCode: "231",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Libyan Arab Jamahiriya",
    flag: "🇱🇾",
    code: "LY",
    dialCode: "218",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Liechtenstein",
    flag: "🇱🇮",
    code: "LI",
    dialCode: "423",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Lithuania",
    flag: "🇱🇹",
    code: "LT",
    dialCode: "370",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Luxembourg",
    flag: "🇱🇺",
    code: "LU",
    dialCode: "352",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Macao",
    flag: "🇲🇴",
    code: "MO",
    dialCode: "853",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Macedonia",
    flag: "🇲🇰",
    code: "MK",
    dialCode: "389",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Madagascar",
    flag: "🇲🇬",
    code: "MG",
    dialCode: "261",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Malawi",
    flag: "🇲🇼",
    code: "MW",
    dialCode: "265",
    minLength: 7,
    maxLength: 9,
  ),
  Country(
    name: "Malaysia",
    flag: "🇲🇾",
    code: "MY",
    dialCode: "60",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Maldives",
    flag: "🇲🇻",
    code: "MV",
    dialCode: "960",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Mali",
    flag: "🇲🇱",
    code: "ML",
    dialCode: "223",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Malta",
    flag: "🇲🇹",
    code: "MT",
    dialCode: "356",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Marshall Islands",
    flag: "🇲🇭",
    code: "MH",
    dialCode: "692",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Martinique",
    flag: "🇲🇶",
    code: "MQ",
    dialCode: "596",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Mauritania",
    flag: "🇲🇷",
    code: "MR",
    dialCode: "222",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Mauritius",
    flag: "🇲🇺",
    code: "MU",
    dialCode: "230",
    minLength: 7,
    maxLength: 8,
  ),
  Country(
    name: "Mayotte",
    flag: "🇾🇹",
    code: "YT",
    dialCode: "262",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mexico",
    flag: "🇲🇽",
    code: "MX",
    dialCode: "52",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Micronesia",
    flag: "🇫🇲",
    code: "FM",
    dialCode: "691",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Moldova",
    flag: "🇲🇩",
    code: "MD",
    dialCode: "373",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Monaco",
    flag: "🇲🇨",
    code: "MC",
    dialCode: "377",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mongolia",
    flag: "🇲🇳",
    code: "MN",
    dialCode: "976",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Montenegro",
    flag: "🇲🇪",
    code: "ME",
    dialCode: "382",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Montserrat",
    flag: "🇲🇸",
    code: "MS",
    dialCode: "1664",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Morocco",
    flag: "🇲🇦",
    code: "MA",
    dialCode: "212",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Mozambique",
    flag: "🇲🇿",
    code: "MZ",
    dialCode: "258",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Myanmar",
    flag: "🇲🇲",
    code: "MM",
    dialCode: "95",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Namibia",
    flag: "🇳🇦",
    code: "NA",
    dialCode: "264",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Nauru",
    flag: "🇳🇷",
    code: "NR",
    dialCode: "674",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Nepal",
    flag: "🇳🇵",
    code: "NP",
    dialCode: "977",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Netherlands",
    flag: "🇳🇱",
    code: "NL",
    dialCode: "31",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Netherlands Antilles",
    flag: "",
    code: "AN",
    dialCode: "599",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "New Caledonia",
    flag: "🇳🇨",
    code: "NC",
    dialCode: "687",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "New Zealand",
    flag: "🇳🇿",
    code: "NZ",
    dialCode: "64",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Nicaragua",
    flag: "🇳🇮",
    code: "NI",
    dialCode: "505",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Niger",
    flag: "🇳🇪",
    code: "NE",
    dialCode: "227",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Nigeria",
    flag: "🇳🇬",
    code: "NG",
    dialCode: "234",
    minLength: 10,
    maxLength: 11,
  ),
  Country(
    name: "Niue",
    flag: "🇳🇺",
    code: "NU",
    dialCode: "683",
    minLength: 4,
    maxLength: 4,
  ),
  Country(
    name: "Norfolk Island",
    flag: "🇳🇫",
    code: "NF",
    dialCode: "672",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Northern Mariana Islands",
    flag: "🇲🇵",
    code: "MP",
    dialCode: "1670",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Norway",
    flag: "🇳🇴",
    code: "NO",
    dialCode: "47",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Oman",
    flag: "🇴🇲",
    code: "OM",
    dialCode: "968",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Pakistan",
    flag: "🇵🇰",
    code: "PK",
    dialCode: "92",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Palau",
    flag: "🇵🇼",
    code: "PW",
    dialCode: "680",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Palestinian Territory",
    flag: "🇵🇸",
    code: "PS",
    dialCode: "970",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Panama",
    flag: "🇵🇦",
    code: "PA",
    dialCode: "507",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Papua New Guinea",
    flag: "🇵🇬",
    code: "PG",
    dialCode: "675",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Paraguay",
    flag: "🇵🇾",
    code: "PY",
    dialCode: "595",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Peru",
    flag: "🇵🇪",
    code: "PE",
    dialCode: "51",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Philippines",
    flag: "🇵🇭",
    code: "PH",
    dialCode: "63",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Pitcairn",
    flag: "🇵🇳",
    code: "PN",
    dialCode: "64",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Poland",
    flag: "🇵🇱",
    code: "PL",
    dialCode: "48",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Portugal",
    flag: "🇵🇹",
    code: "PT",
    dialCode: "351",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Puerto Rico",
    flag: "🇵🇷",
    code: "PR",
    dialCode: "1939",
    minLength: 15,
    maxLength: 15,
  ),
  Country(
    name: "Qatar",
    flag: "🇶🇦",
    code: "QA",
    dialCode: "974",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Romania",
    flag: "🇷🇴",
    code: "RO",
    dialCode: "40",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Russia",
    flag: "🇷🇺",
    code: "RU",
    dialCode: "7",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Rwanda",
    flag: "🇷🇼",
    code: "RW",
    dialCode: "250",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Reunion",
    flag: "🇷🇪",
    code: "RE",
    dialCode: "262",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Barthelemy",
    flag: "🇧🇱",
    code: "BL",
    dialCode: "590",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Kitts and Nevis",
    flag: "🇰🇳",
    code: "KN",
    dialCode: "1869",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saint Lucia",
    flag: "🇱🇨",
    code: "LC",
    dialCode: "1758",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saint Martin",
    flag: "🇲🇫",
    code: "MF",
    dialCode: "590",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Saint Pierre and Miquelon",
    flag: "🇵🇲",
    code: "PM",
    dialCode: "508",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Samoa",
    flag: "🇼🇸",
    code: "WS",
    dialCode: "685",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "San Marino",
    flag: "🇸🇲",
    code: "SM",
    dialCode: "378",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Sao Tome and Principe",
    flag: "🇸🇹",
    code: "ST",
    dialCode: "239",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Saudi Arabia",
    flag: "🇸🇦",
    code: "SA",
    dialCode: "966",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Senegal",
    flag: "🇸🇳",
    code: "SN",
    dialCode: "221",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Serbia",
    flag: "🇷🇸",
    code: "RS",
    dialCode: "381",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Seychelles",
    flag: "🇸🇨",
    code: "SC",
    dialCode: "248",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Sierra Leone",
    flag: "🇸🇱",
    code: "SL",
    dialCode: "232",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Singapore",
    flag: "🇸🇬",
    code: "SG",
    dialCode: "65",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Slovakia",
    flag: "🇸🇰",
    code: "SK",
    dialCode: "421",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Slovenia",
    flag: "🇸🇮",
    code: "SI",
    dialCode: "386",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Solomon Islands",
    flag: "🇸🇧",
    code: "SB",
    dialCode: "677",
    minLength: 5,
    maxLength: 5,
  ),
  Country(
    name: "Somalia",
    flag: "🇸🇴",
    code: "SO",
    dialCode: "252",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "South Africa",
    flag: "🇿🇦",
    code: "ZA",
    dialCode: "27",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "South Sudan",
    flag: "🇸🇸",
    code: "SS",
    dialCode: "211",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Spain",
    flag: "🇪🇸",
    code: "ES",
    dialCode: "34",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Sri Lanka",
    flag: "🇱🇰",
    code: "LK",
    dialCode: "94",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Sudan",
    flag: "🇸🇩",
    code: "SD",
    dialCode: "249",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Suriname",
    flag: "🇸🇷",
    code: "SR",
    dialCode: "597",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Svalbard and Jan Mayen",
    flag: "🇸🇯",
    code: "SJ",
    dialCode: "47",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Eswatini",
    flag: "🇸🇿",
    code: "SZ",
    dialCode: "268",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Sweden",
    flag: "🇸🇪",
    code: "SE",
    dialCode: "46",
    minLength: 7,
    maxLength: 13,
  ),
  Country(
    name: "Switzerland",
    flag: "🇨🇭",
    code: "CH",
    dialCode: "41",
    minLength: 12,
    maxLength: 12,
  ),
  Country(
    name: "Syrian Arab Republic",
    flag: "🇸🇾",
    code: "SY",
    dialCode: "963",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Taiwan",
    flag: "🇹🇼",
    code: "TW",
    dialCode: "886",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Tajikistan",
    flag: "🇹🇯",
    code: "TJ",
    dialCode: "992",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Tanzania",
    flag: "🇹🇿",
    code: "TZ",
    dialCode: "255",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Thailand",
    flag: "🇹🇭",
    code: "TH",
    dialCode: "66",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Timor-Leste",
    flag: "🇹🇱",
    code: "TL",
    dialCode: "670",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Togo",
    flag: "🇹🇬",
    code: "TG",
    dialCode: "228",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Tokelau",
    flag: "🇹🇰",
    code: "TK",
    dialCode: "690",
    minLength: 4,
    maxLength: 4,
  ),
  Country(
    name: "Tonga",
    flag: "🇹🇴",
    code: "TO",
    dialCode: "676",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Trinidad and Tobago",
    flag: "🇹🇹",
    code: "TT",
    dialCode: "1868",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Tunisia",
    flag: "🇹🇳",
    code: "TN",
    dialCode: "216",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Turkey",
    flag: "🇹🇷",
    code: "TR",
    dialCode: "90",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Turkmenistan",
    flag: "🇹🇲",
    code: "TM",
    dialCode: "993",
    minLength: 8,
    maxLength: 8,
  ),
  Country(
    name: "Turks and Caicos Islands",
    flag: "🇹🇨",
    code: "TC",
    dialCode: "1649",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Tuvalu",
    flag: "🇹🇻",
    code: "TV",
    dialCode: "688",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Uganda",
    flag: "🇺🇬",
    code: "UG",
    dialCode: "256",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Ukraine",
    flag: "🇺🇦",
    code: "UA",
    dialCode: "380",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "United Arab Emirates",
    flag: "🇦🇪",
    code: "AE",
    dialCode: "971",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "United Kingdom",
    flag: "🇬🇧",
    code: "GB",
    dialCode: "44",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "United States",
    flag: "🇺🇸",
    code: "US",
    dialCode: "1",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Uruguay",
    flag: "🇺🇾",
    code: "UY",
    dialCode: "598",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Uzbekistan",
    flag: "🇺🇿",
    code: "UZ",
    dialCode: "998",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Vanuatu",
    flag: "🇻🇺",
    code: "VU",
    dialCode: "678",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Venezuela",
    flag: "🇻🇪",
    code: "VE",
    dialCode: "58",
    minLength: 10,
    maxLength: 10,
  ),
  Country(
    name: "Vietnam",
    flag: "🇻🇳",
    code: "VN",
    dialCode: "84",
    minLength: 11,
    maxLength: 11,
  ),
  Country(
    name: "Virgin Islands, British",
    flag: "🇻🇬",
    code: "VG",
    dialCode: "1284",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Virgin Islands, U.S.",
    flag: "🇻🇮",
    code: "VI",
    dialCode: "1340",
    minLength: 7,
    maxLength: 7,
  ),
  Country(
    name: "Wallis and Futuna",
    flag: "🇼🇫",
    code: "WF",
    dialCode: "681",
    minLength: 6,
    maxLength: 6,
  ),
  Country(
    name: "Yemen",
    flag: "🇾🇪",
    code: "YE",
    dialCode: "967",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Zambia",
    flag: "🇿🇲",
    code: "ZM",
    dialCode: "260",
    minLength: 9,
    maxLength: 9,
  ),
  Country(
    name: "Zimbabwe",
    flag: "🇿🇼",
    code: "ZW",
    dialCode: "263",
    minLength: 9,
    maxLength: 9,
  ),
];

class Country {
  final String name;
  final String flag;
  final String code;
  final String dialCode;
  final String regionCode;
  final int minLength;
  final int maxLength;

  const Country({
    required this.name,
    required this.flag,
    required this.code,
    required this.dialCode,
    required this.minLength,
    required this.maxLength,
    this.regionCode = "",
  });

  String get fullCountryCode {
    return dialCode + regionCode;
  }

  String get displayCC {
    if (regionCode != "") {
      return "$dialCode $regionCode";
    }
    return dialCode;
  }
}
