import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/home_controller.dart';
import '../home/<USER>/restaurants_section.dart';

class AllRestaurant extends StatelessWidget {
  final HomeController homeController = Get.put(HomeController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final String selectedCategory;
  final RxList<String> favoriteList = RxList<String>();

  AllRestaurant({
    super.key,
    this.selectedCategory = 'Tüm Kate<PERSON>iler',
  });

  Future<List<Map<String, dynamic>>> getFirms() async {
    QuerySnapshot snapshot = await FirebaseFirestore.instance.collection('firms').get();
    return snapshot.docs.map((doc) {
      var data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return data;
    }).toList();
  }

  Future<bool> hasProducts(String firmId) async {
    QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('products')
        .where('firmId', isEqualTo: firmId)
        .get();
    return snapshot.docs.isNotEmpty;
  }

  Future<void> toggleFavorite(String firmId) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      Get.toNamed(AppRoutes.signInScreen);
      return;
    }

    final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
    final userDoc = await userRef.get();

    if (!userDoc.exists || !(userDoc.data()?.containsKey('favorites') ?? false)) {
      await userRef.set({'favorites': []}, SetOptions(merge: true));
    }

    final favorites = List<String>.from(userDoc.data()?['favorites'] ?? []);

    if (favorites.contains(firmId)) {
      favorites.remove(firmId);
    } else {
      favorites.add(firmId);
    }

    await userRef.update({'favorites': favorites});
    favoriteList.value = favorites;
    favoriteList.refresh();
  }

  Future<List<String>> getFavorites() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return [];

    final userDoc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .get();

    return List<String>.from(userDoc.data()?['favorites'] ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: AppBar(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            ),
            onPressed: () => Get.back(),
          ),
          title: Text(
            "Tüm Firmalar",
            style: TextStyle(
              fontFamily: FontFamilyConfig.urbanistSemiBold,
              fontSize: FontConfig.kFontSize18,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            ),
          ),

        ),
      ),
      body: FutureBuilder(
        future: Future.wait([getFirms(), getFavorites()]),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text("Firmalar alınamadı."));
          }

          var firms = snapshot.data?[0] as List<Map<String, dynamic>>;
          favoriteList.value = snapshot.data?[1] as List<String>? ?? [];

          firms.sort((a, b) {
            bool aVisible = a['visible'] ?? true;
            bool bVisible = b['visible'] ?? true;
            if (aVisible == bVisible) return 0;
            return aVisible ? -1 : 1;
          });

          return ListView.builder(
            padding: const EdgeInsets.all(12),
            itemCount: firms.length,
            itemBuilder: (context, index) {
              final firm = firms[index];
              List<String> firmCategories = List<String>.from(firm['categories'] ?? []);

              if (selectedCategory != 'Tüm Kategoriler' &&
                  !firmCategories.contains(selectedCategory)) {
                return const SizedBox.shrink();
              }

              return RestaurantCard(
                firm: firm,
                darkModeController: darkModeController,
                favoriteList: favoriteList,
                onToggleFavorite: toggleFavorite,
                hasProducts: hasProducts,
              );
            },
          );
        },
      ),
    ));
  }
} 