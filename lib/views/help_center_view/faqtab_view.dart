// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:get/get.dart';
import '../../../../../config/color.dart';
import '../../../../../config/font_config.dart';
import '../../../../../config/font_family.dart';
import '../../../../../config/image_path.dart';
import '../../../../../config/size_config.dart';
import '../../../../../controller/setting_controller.dart';

class FaqView extends StatelessWidget {
  FaqView({Key? key}) : super(key: key);

  final SettingController _settingController = Get.put(SettingController());
  DarkModeController darkModeController = Get.put(DarkModeController());

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'Yemekkapımda Nedir?',
              content: 'Yemekkapımda; Restoran, Market vb işletmelerden sipariş vermek isteyen internet kullanıcılarını aynı ortamda buluşturmak amacı ile kurulmuş bir sitedir.',
              isExpanded: _settingController.isReservation,
            ),
            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'Yemekkapımda\'ya Nasıl Üye Olunur?',
              content: 'Yemekkapımda.com web sitesinde sağ üst köşedeki "Kayıt Ol" butonunu, uygulamada ise sağ alt köşedeki "Kayıt Ol" seçeneğini göreceksiniz. Açılan sayfada ad, soyad, telefon bilgilerini doldurduktan sonra tarafınıza gelen sms onay kodunu ekrana girerek üyeliğinizi tamamlayıp sisteme giriş yapabilirsiniz. Üyelik oluşturabilmek için telefon numaranızın Türkiye uzantılı olması gerekmektedir. Yemekkapımda üzerinden gerçekleşen işlemler sipariş içeriğine göre Mesafeli Sözleşmeler Yönetmeliği kapsamında olmayabilecektir.',
              isExpanded: _settingController.isOffers,
            ),
            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'Yemekkapımda\'da Yemek Seçimi Nasıl Yapılır?',
              content: 'Yemekkapımda\'da yemek seçimi yapmak için "Restoranlar" butonuna tıklayarak restoran seçimi yapmanız gerekir. Sipariş vermek istediğiniz restoranın menüsüne girerek yemek ve diğer ürünleri görebilir; seçtiğiniz ürünün üzerine tıklayarak sepetinize ekleyebilirsiniz. Ürünle ilgili not eklemek isterseniz ürünü sepete eklemeden önce Ürün Notu bölümünden eklemek istediğiniz notu yazabilirsiniz.',
              isExpanded: _settingController.isLikes,
            ),
            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'Yemekkapımda\'da Sipariş Nasıl Verilir?',
              content: 'Yemek siparişi vermek için öncelikle sepetinize en az bir ürün eklemelisiniz. Sepetinizdeki ürünlerin toplam fiyatı belirlenmiş Minimum Paket Tutarı seviyesiyle aynı ya da daha fazla olmalıdır. Bu koşulu sağladığınız takdirde, mobil uygulama için "Sipariş Ver"; web sitesi için sağ üst köşedeki sepet ikonuna tıklayıp, açılan sekmede ödeme şeklini seçip, adres bilginizi de girdikten sonra "Siparişi Tamamla" bağlantısına tıklayarak siparişinizi verebilirsiniz.',
              isExpanded: _settingController.isMore,
            ),
            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'Yemekkapımda hangi saatler arası açık?',
              content: 'Yemekkapımda portföyündeki restoranların açık olma saatleri çerçevesinde tüm kullanıcılarına hizmet vermektedir. Çalışma saatleri içinde olan restoranlar uygulamada ve web sitemizde aktif olarak görünür ve hızlıca sipariş verilebilir.',
              isExpanded: _settingController.isOffers2,
            ),
            const SizedBox(height: SizeConfig.kHeight24),
            buildExpansionTile(
              title: 'İlave sorularım olması halinde nereye ulaşabilirim?',
              content: 'İlave sorularınız için: 0543 582 04 42 arayabilirsiniz.',
              isExpanded: _settingController.isMore2,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildExpansionTile({required String title, required String content, required RxBool isExpanded}) {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.symmetric(vertical: SizeConfig.kHeight2),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.kHeight16),
        boxShadow: [
          BoxShadow(
            color: ColorConfig.kDividerColor.withAlpha(25),
            blurRadius: 3,
            spreadRadius: 5,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Obx(() => ExpansionTile(
        collapsedBackgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        clipBehavior: Clip.antiAlias,
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConfig.kHeight12),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConfig.kHeight12),
        ),
        backgroundColor: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        title: Text(
          title,
          style: TextStyle(
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
            fontWeight: FontWeight.w600,
            fontSize: FontConfig.kFontSize16,
          ),
        ),
        trailing: Image.asset(
          isExpanded.value ? ImagePath.arrowRight : ImagePath.arrowDown,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          height: SizeConfig.kHeight20,
          width: SizeConfig.kHeight20,
        ),
        children: <Widget>[
          buildDivider(),
          Padding(
            padding: const EdgeInsets.only(
              left: SizeConfig.kHeight16,
              top: SizeConfig.kHeight12,
              bottom: SizeConfig.kHeight16,
              right: SizeConfig.kHeight16,
            ),
            child: Text(
              content,
              style: TextStyle(
                fontFamily: FontFamilyConfig.urbanistRegular,
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kBlackColor.withAlpha(175)
                    : ColorConfig.kWhiteColor,
                fontWeight: FontWeight.w400,
                fontSize: FontConfig.kFontSize12,
              ),
            ),
          ),
        ],
        onExpansionChanged: (bool expanded) {
          isExpanded.value = expanded;
        },
      )),
    );
  }

  buildDivider() {
    return Container(
      width: Get.width,
      height: SizeConfig.kHeight1,
      margin: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight16),
      color: ColorConfig.kHintColor.withAlpha(50)
    );
  }
}