// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../../config/color.dart';
import '../../../../../config/font_config.dart';
import '../../../../../config/font_family.dart';
import '../../../../../config/image_path.dart';
import '../../../../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';

class ContactUsView extends StatelessWidget {
  ContactUsView({Key? key}) : super(key: key);

  DarkModeController darkModeController = Get.put(DarkModeController());

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight10),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('İletişime Geçin',
                  style: TextStyle(
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                    fontWeight: FontWeight.w600,
                    fontSize: FontConfig.kFontSize18,
                  )),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: SizeConfig.kHeight10),
                child: Text('Sizden haber almak bizi mutlu eder',
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistRegular,
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kBlackColor
                              .withAlpha(200)
                          : ColorConfig.kWhiteColor,
                      fontWeight: FontWeight.w400,
                      fontSize: FontConfig.kFontSize14,
                    )),
              ),
              const SizedBox(
                height: SizeConfig.kHeight20,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset(
                    ImagePath.letter,
                    height: SizeConfig.kHeight20,
                    width: SizeConfig.kHeight20,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                  ),
                  const SizedBox(
                    width: SizeConfig.kHeight20,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Bize E-posta Gönderin',
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistSemiBold,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                            fontWeight: FontWeight.w600,
                            fontSize: FontConfig.kFontSize16,
                          )),
                      Padding(
                        padding:
                            const EdgeInsets.only(top: SizeConfig.kHeight5),
                        child: Text('Dost canlısı ekibimiz size yardımcı olmaktan mutluluk duyar',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistRegular,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kHintColor
                                      .withAlpha(125)
                                  : ColorConfig.kDividerColor.withAlpha(225),
                              fontWeight: FontWeight.w400,
                              fontSize: FontConfig.kFontSize14,
                            )),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 90,
                        child: Divider(
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kDividerColor
                              : ColorConfig.kFillColor,
                          height: SizeConfig.kHeight25,
                        ),
                      ),
                      InkWell(
                        onTap: () => launchUrl(Uri.parse('mailto:yemekkapımda<EMAIL>')),
                        child: Text('yemekkapımda<EMAIL>',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistSemiBold,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kBlackColor
                                  : ColorConfig.kWhiteColor,
                              fontWeight: FontWeight.w600,
                              fontSize: FontConfig.kFontSize14,
                            )),
                      ),
                    ],
                  )
                ],
              ),
              const SizedBox(
                height: SizeConfig.kHeight40,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset(
                    ImagePath.mapPoint,
                    height: SizeConfig.kHeight20,
                    width: SizeConfig.kHeight20,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                  ),
                  const SizedBox(
                    width: SizeConfig.kHeight20,
                  ),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Ofis',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistSemiBold,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kBlackColor
                                  : ColorConfig.kWhiteColor,
                              fontWeight: FontWeight.w600,
                              fontSize: FontConfig.kFontSize16,
                            )),
                        Padding(
                          padding:
                              const EdgeInsets.only(top: SizeConfig.kHeight5),
                          child: Text('Ofisimizde bizi ziyaret edin',
                              style: TextStyle(
                                fontFamily: FontFamilyConfig.urbanistRegular,
                                color: darkModeController.isLightTheme.value
                                    ? ColorConfig.kHintColor
                                        .withAlpha(125)
                                    : ColorConfig.kDividerColor.withAlpha(225),
                                fontWeight: FontWeight.w400,
                                fontSize: FontConfig.kFontSize14,
                              )),
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width - 90,
                          child: Divider(
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kDividerColor
                                : ColorConfig.kFillColor,
                            height: SizeConfig.kHeight25,
                          ),
                        ),
                        Text('Hacı Seyit Ali Mahallesi 153086 Sokak No:20 C Seydişehir/Konya',
                            style: TextStyle(
                              fontFamily: FontFamilyConfig.urbanistSemiBold,
                              color: darkModeController.isLightTheme.value
                                  ? ColorConfig.kBlackColor
                                  : ColorConfig.kWhiteColor,
                              fontWeight: FontWeight.w600,
                              fontSize: FontConfig.kFontSize14,
                            ))
                      ])
                ],
              ),
              const SizedBox(
                height: SizeConfig.kHeight40,
              ),
              Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Image.asset(
                  ImagePath.phone,
                  height: SizeConfig.kHeight20,
                  width: SizeConfig.kHeight20,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
                const SizedBox(
                  width: SizeConfig.kHeight20,
                ),
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Text('Telefon',
                      style: TextStyle(
                        fontFamily: FontFamilyConfig.urbanistSemiBold,
                        color: darkModeController.isLightTheme.value
                            ? ColorConfig.kBlackColor
                            : ColorConfig.kWhiteColor,
                        fontWeight: FontWeight.w600,
                        fontSize: FontConfig.kFontSize16,
                      )),
                  Padding(
                    padding: const EdgeInsets.only(top: SizeConfig.kHeight5),
                    child: Text('Pazartesi-Cuma 9:00-18:00',
                        style: TextStyle(
                          fontFamily: FontFamilyConfig.urbanistRegular,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kHintColor
                              : ColorConfig.kDividerColor.withAlpha(225),
                          fontWeight: FontWeight.w400,
                          fontSize: FontConfig.kFontSize14,
                        )),
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width - 90,
                    child: Divider(
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kDividerColor
                          : ColorConfig.kFillColor,
                      height: SizeConfig.kHeight25,
                    ),
                  ),
                  InkWell(
                    onTap: () => launchUrl(Uri.parse('tel:05435820442')),
                    child: Text('0543 582 04 42',
                        style: TextStyle(
                          fontFamily: FontFamilyConfig.urbanistSemiBold,
                          color: darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor,
                          fontWeight: FontWeight.w600,
                          fontSize: FontConfig.kFontSize14,
                        )),
                  ),
                ])
              ]),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    ImagePath.whatsapp,
                    height: SizeConfig.kHeight18,
                    width: SizeConfig.kHeight18,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kTextfieldTextColor
                        : ColorConfig.kWhiteColor,
                  ),
                  const SizedBox(
                    width: SizeConfig.kHeight24,
                  ),
                  Image.asset(
                    ImagePath.facebook,
                    height: SizeConfig.kHeight18,
                    width: SizeConfig.kHeight18,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kTextfieldTextColor
                        : ColorConfig.kWhiteColor,
                  ),
                  const SizedBox(
                    width: SizeConfig.kHeight24,
                  ),
                  Image.asset(
                    ImagePath.twitter,
                    height: SizeConfig.kHeight18,
                    width: SizeConfig.kHeight18,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kTextfieldTextColor
                        : ColorConfig.kWhiteColor,
                  ),
                  const SizedBox(
                    width: SizeConfig.kHeight24,
                  ),
                  Image.asset(
                    ImagePath.instagram,
                    height: SizeConfig.kHeight18,
                    width: SizeConfig.kHeight18,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kTextfieldTextColor
                        : ColorConfig.kWhiteColor,
                  )
                ],
              ),
              const SizedBox(
                height: SizeConfig.kHeight10,
              ),
            ]));
  }
}