import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/size_config.dart';
import '../../config/image_path.dart';
import '../../config/string_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/my_cart_controller.dart';
import '../../controller/checkout_order_controller.dart';
import '../../utils/appbar_common.dart';
import '../../utils/common_material_button.dart';

class CheckoutOrderScreen extends StatefulWidget {
  const CheckoutOrderScreen({Key? key}) : super(key: key);

  @override
  State<CheckoutOrderScreen> createState() => _CheckoutOrderScreenState();
}

class _CheckoutOrderScreenState extends State<CheckoutOrderScreen> {
  DarkModeController darkModeController = Get.put(DarkModeController());
  MyCartController myCartController = Get.put(MyCartController());
  CheckoutOrderController checkoutOrderController =
      Get.put(CheckoutOrderController());
  FirebaseFirestore firestore = FirebaseFirestore.instance;
  FirebaseAuth auth = FirebaseAuth.instance;

  String deliveryAddress = "Adres Bulunamadı";
  List<Map<String, dynamic>> cartItems = [];
  double totalPrice = 0.0;
  double finalPrice = 0.0;
  List<String> selectedInstructions = [];
  String orderId = "";
  String orderNote = '';
  String selectedPaymentMethod = "Kapıda Nakit Ödeme";
  bool isFirstOrder = false;
  final double firstOrderDiscount = 10.0;
  final double minOrderAmount = 100.0;
  Map<String, double> firmTotals = {};
  String errorMessage = '';
  bool isProcessing = false;

  @override
  void initState() {
    super.initState();
    getUserAddress();
    fetchCartItemsFromFirebase();
    checkIfFirstOrder();
  }

  Future<void> checkIfFirstOrder() async {
    try {
      var userId = auth.currentUser!.uid;
      var ordersRef =
          firestore.collection('users').doc(userId).collection('orders');
      var orderSnapshot = await ordersRef.limit(1).get();

      setState(() {
        isFirstOrder = orderSnapshot.docs.isEmpty;
      });
    } catch (e) {
      setState(() {
        isFirstOrder = false;
      });
      debugPrint('İlk sipariş kontrolünde hata: $e');
    }
  }

  Future<void> getUserAddress() async {
    var userId = auth.currentUser!.uid;
    var userDoc = await firestore.collection('users').doc(userId).get();
    var deliverToId = userDoc.data()?['deliverTo'];
    var addresses = userDoc.data()?['addresses'] ?? [];

    for (var address in addresses) {
      if (address['id'] == deliverToId) {
        setState(() {
          deliveryAddress =
              "${address['sokak']}, ${address['mahalle']}, ${address['bina_ve_daire']}";
        });
        break;
      }
    }
  }

  Future<void> fetchCartItemsFromFirebase() async {
    var userId = auth.currentUser!.uid;
    var cartSnapshot = await firestore
        .collection('users')
        .doc(userId)
        .collection('activeCartWeb')
        .doc('cart')
        .collection('items')
        .get();

    setState(() {
      cartItems = cartSnapshot.docs.map((doc) {
        var data = doc.data();
        var selectedFeatures = data['selectedFeatures'] ?? [];
        return {
          ...data,
          'selectedFeatures': selectedFeatures,
        };
      }).toList();

      totalPrice = calculateTotalPrice();
      calculateFirmTotals();

      if (isFirstOrder) {
        finalPrice = totalPrice * (1 - firstOrderDiscount / 100);
      } else {
        finalPrice = totalPrice;
      }
    });
  }

  double calculateTotalPrice() {
    return cartItems.fold(
      0.0,
      (total, item) => total + (item['price'] * item['quantity']),
    );
  }

  void calculateFirmTotals() {
    Map<String, double> totals = {};

    for (var item in cartItems) {
      String firmId = item['firmId'];
      double itemTotal = item['price'] * item['quantity'];
      totals[firmId] = (totals[firmId] ?? 0) + itemTotal;
    }

    setState(() {
      firmTotals = totals;
    });
  }

  bool checkMinimumOrderAmounts() {
    List<String> invalidFirms = [];

    firmTotals.forEach((firmId, total) {
      if (total < minOrderAmount) {
        invalidFirms.add(firmId);
      }
    });

    if (invalidFirms.isNotEmpty) {
      setState(() {
        errorMessage =
            'Her firma için minimum sipariş tutarı 100 TL olmalıdır.';
      });
      Get.snackbar(
        'Minimum Sipariş Tutarı',
        'Her firma için minimum sipariş tutarı 100 TL olmalıdır.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  Future<void> createOrUpdateOrderInFirebase() async {
    if (isProcessing) return;

    if (!checkMinimumOrderAmounts()) {
      return;
    }

    setState(() {
      isProcessing = true;
    });

    try {
      var userId = auth.currentUser!.uid;

      var newOrderRef = firestore.collection('orders').doc();
      orderId = newOrderRef.id;

      // Kullanıcı detaylarını ve adres bilgilerini al
      var userDoc = await firestore.collection('users').doc(userId).get();
      var userData = userDoc.data();
      var deliverToId = userData?['deliverTo'];
      var addresses = userData?['addresses'] ?? [];
      var firstName = userData?['firstName'] ?? '';
      var lastName = userData?['lastName'] ?? '';
      var phoneNumber = userData?['phoneNumber'] ?? '';

      // Web formatına uygun olarak items'ları düzenleyelim
      List<Map<String, dynamic>> formattedItems =
          await Future.wait(cartItems.map((item) async {
        // Kategori adını getir
        var categoryDoc = await firestore
            .collection('categories')
            .doc(item['categoryId'])
            .get();
        String categoryName = categoryDoc.data()?['name'] ?? '';

        // Firma adını getir
        var firmDoc =
            await firestore.collection('firms').doc(item['firmId']).get();
        String firmName = firmDoc.data()?['name'] ?? '';

        return {
          "categoryId": item['categoryId'],
          "categoryName": categoryName,
          "firmId": item['firmId'],
          "firmName": firmName,
          "id": item['id'],
          "productId": item['id'],
          "imageURL": item['imageURL'],
          "itemStatus": "Sipariş Alındı",
          "price": item['price'],
          "productName": item['productName'],
          "quantity": item['quantity'],
          "selectedFeatures": item['selectedFeatures'] ?? [],
        };
      }));

      // Tüm talimatlar için sayısal indeksli bir map oluştur
      Map<String, String> deliveryInstructionsMap = {};
      for (int i = 0; i < selectedInstructions.length; i++) {
        deliveryInstructionsMap[i.toString()] = selectedInstructions[i];
      }

      Map<String, dynamic> orderData = {
        'addresses': addresses,
        'deliverTo': deliverToId,
        'deliveryInstructions': deliveryInstructionsMap,
        'discountApplied':
            isFirstOrder ? (totalPrice * firstOrderDiscount / 100) : 0,
        'finalPrice': finalPrice,
        'firstName': firstName,
        'instructions': orderNote,
        'isFirstOrder': isFirstOrder,
        'items': formattedItems,
        'lastName': lastName,
        'orderDate': FieldValue.serverTimestamp(),
        'totalPrice': totalPrice,
        'paymentMethod': selectedPaymentMethod,
        'phoneNumber': phoneNumber,
        'status': 'Sipariş Alındı',
        'userId': userId
      };

      // Ana orders koleksiyonuna kaydet
      await newOrderRef.set(orderData);

      // Kullanıcının orders koleksiyonuna kaydet
      await firestore
          .collection('users')
          .doc(userId)
          .collection('orders')
          .doc(orderId)
          .set(orderData);

      // Her firmanın orders koleksiyonuna kaydet
      Map<String, List<Map<String, dynamic>>> firmOrders = {};
      for (var item in formattedItems) {
        String firmId = item['firmId'];
        if (!firmOrders.containsKey(firmId)) {
          firmOrders[firmId] = [];
        }
        firmOrders[firmId]!.add(item);
      }

      for (var firmId in firmOrders.keys) {
        var firmOrderData = {
          ...orderData,
          'items': firmOrders[firmId],
        };

        await firestore
            .collection('firms')
            .doc(firmId)
            .collection('orders')
            .doc(orderId)
            .set(firmOrderData);
      }

      // Sepeti temizle
      var cartSnapshot = await firestore
          .collection('users')
          .doc(userId)
          .collection('activeCartWeb')
          .doc('cart')
          .collection('items')
          .get();

      for (var doc in cartSnapshot.docs) {
        await doc.reference.delete();
      }

      Get.toNamed(AppRoutes.successfulOrder);
    } catch (error) {
      Get.snackbar(
        'Hata',
        'Sipariş oluşturulurken bir hata oluştu',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkModeController.isLightTheme.value
          ? ColorConfig.kWhiteColor
          : ColorConfig.kBlackColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
        child: CommonAppBar(
          title: StringConfig.checkoutOrders,
          leadingImage: ImagePath.arrow,
          color: darkModeController.isLightTheme.value
              ? ColorConfig.kBlackColor
              : ColorConfig.kWhiteColor,
          leadingOnTap: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            if (errorMessage.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: Colors.red,
                child: Text(
                  errorMessage,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(SizeConfig.padding20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDeliveryAddressSection(),
                  const SizedBox(height: SizeConfig.kHeight20),
                  _buildOrderSummary(),
                  const SizedBox(height: SizeConfig.kHeight20),
                  _buildDeliveryInstructions(),
                  const SizedBox(height: SizeConfig.kHeight20),
                  _buildPaymentMethod(),
                  const SizedBox(height: SizeConfig.kHeight20),
                  _buildOrderNote(),
                  const SizedBox(height: SizeConfig.kHeight20),
                  _buildTotalSection(),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomButton(),
    );
  }

  Widget _buildDeliveryAddressSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        boxShadow: [
          BoxShadow(
            spreadRadius: 3,
            color: ColorConfig.kDarkDialougeColor.withAlpha(25),
            blurRadius: 5,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(SizeConfig.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Teslimat Adresi",
                  style: TextStyle(
                    fontSize: FontConfig.kFontSize18,
                    fontFamily: FontFamilyConfig.urbanistSemiBold,
                    color: darkModeController.isLightTheme.value
                        ? ColorConfig.kBlackColor
                        : ColorConfig.kWhiteColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(AppRoutes.deliverToScreen);
                  },
                  child: Text(
                    "Değiştir",
                    style: TextStyle(
                      color: ColorConfig.kPrimaryColor,
                      fontSize: FontConfig.kFontSize14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: SizeConfig.kHeight10),
            Text(
              deliveryAddress,
              style: TextStyle(
                fontSize: FontConfig.kFontSize14,
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kHintColor
                    : ColorConfig.kWhiteColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Sipariş Özeti",
          style: TextStyle(
            fontSize: FontConfig.kFontSize18,
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
        const SizedBox(height: SizeConfig.kHeight10),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: cartItems.length,
          itemBuilder: (context, index) {
            var item = cartItems[index];
            return Container(
              margin: const EdgeInsets.only(bottom: SizeConfig.kHeight10),
              padding: const EdgeInsets.all(SizeConfig.padding10),
              decoration: BoxDecoration(
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kWhiteColor
                    : ColorConfig.kDarkModeColor,
                borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
                boxShadow: [
                  BoxShadow(
                    spreadRadius: 3,
                    color: ColorConfig.kDarkDialougeColor.withAlpha(25),
                    blurRadius: 5,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Image.network(
                    item['imageURL'],
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                  ),
                  const SizedBox(width: SizeConfig.kHeight10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['productName'],
                          style: TextStyle(
                            fontSize: FontConfig.kFontSize16,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                          ),
                        ),
                        Text(
                          "${item['price']} TL x ${item['quantity']}",
                          style: TextStyle(
                            fontSize: FontConfig.kFontSize14,
                            color: ColorConfig.kPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDeliveryInstructions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Teslimat Talimatları",
          style: TextStyle(
            fontSize: FontConfig.kFontSize18,
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
        const SizedBox(height: SizeConfig.kHeight10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: [
            for (String instruction
                in checkoutOrderController.instructionsString)
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (selectedInstructions.contains(instruction)) {
                      selectedInstructions.remove(instruction);
                    } else {
                      selectedInstructions.add(instruction);
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: SizeConfig.padding16,
                    vertical: SizeConfig.padding8,
                  ),
                  decoration: BoxDecoration(
                    color: selectedInstructions.contains(instruction)
                        ? ColorConfig.kPrimaryColor
                        : darkModeController.isLightTheme.value
                            ? ColorConfig.kWhiteColor
                            : ColorConfig.kDarkModeColor,
                    borderRadius:
                        BorderRadius.circular(SizeConfig.borderRadius12),
                    boxShadow: [
                      BoxShadow(
                        spreadRadius: 1,
                        color: ColorConfig.kDarkDialougeColor.withAlpha(25),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Text(
                    instruction,
                    style: TextStyle(
                      color: selectedInstructions.contains(instruction)
                          ? ColorConfig.kWhiteColor
                          : darkModeController.isLightTheme.value
                              ? ColorConfig.kBlackColor
                              : ColorConfig.kWhiteColor,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentMethod() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Ödeme Yöntemi",
          style: TextStyle(
            fontSize: FontConfig.kFontSize18,
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
        const SizedBox(height: SizeConfig.kHeight10),
        Container(
          decoration: BoxDecoration(
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kDarkModeColor,
            borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
            boxShadow: [
              BoxShadow(
                spreadRadius: 3,
                color: ColorConfig.kDarkDialougeColor.withAlpha(25),
                blurRadius: 5,
              ),
            ],
          ),
          child: Column(
            children: [
              RadioListTile<String>(
                title: const Text('Kapıda Nakit Ödeme'),
                value: 'Kapıda Nakit Ödeme',
                groupValue: selectedPaymentMethod,
                onChanged: (value) {
                  setState(() {
                    selectedPaymentMethod = value!;
                  });
                },
              ),
              RadioListTile<String>(
                title: const Text('Kapıda Kredi Kartı ile Ödeme'),
                value: 'Kapıda Kredi Kartı ile Ödeme',
                groupValue: selectedPaymentMethod,
                onChanged: (value) {
                  setState(() {
                    selectedPaymentMethod = value!;
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTotalSection() {
    return Container(
      padding: const EdgeInsets.all(SizeConfig.padding16),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
        boxShadow: [
          BoxShadow(
            spreadRadius: 3,
            color: ColorConfig.kDarkDialougeColor.withAlpha(25),
            blurRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Ara Toplam",
                style: TextStyle(
                  fontSize: FontConfig.kFontSize14,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
              Text(
                "${totalPrice.toStringAsFixed(2)} TL",
                style: TextStyle(
                  fontSize: FontConfig.kFontSize14,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
            ],
          ),
          if (isFirstOrder) ...[
            const SizedBox(height: SizeConfig.kHeight10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "İlk Sipariş İndirimi (%$firstOrderDiscount)",
                  style: TextStyle(
                    fontSize: FontConfig.kFontSize14,
                    color: ColorConfig.kPrimaryColor,
                  ),
                ),
                Text(
                  "-${(totalPrice * firstOrderDiscount / 100).toStringAsFixed(2)} TL",
                  style: TextStyle(
                    fontSize: FontConfig.kFontSize14,
                    color: ColorConfig.kPrimaryColor,
                  ),
                ),
              ],
            ),
          ],
          const Divider(height: SizeConfig.kHeight20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Toplam",
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontWeight: FontWeight.bold,
                  color: darkModeController.isLightTheme.value
                      ? ColorConfig.kBlackColor
                      : ColorConfig.kWhiteColor,
                ),
              ),
              Text(
                "${finalPrice.toStringAsFixed(2)} TL",
                style: TextStyle(
                  fontSize: FontConfig.kFontSize16,
                  fontWeight: FontWeight.bold,
                  color: ColorConfig.kPrimaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(SizeConfig.padding20),
      decoration: BoxDecoration(
        color: darkModeController.isLightTheme.value
            ? ColorConfig.kWhiteColor
            : ColorConfig.kDarkModeColor,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -4),
            blurRadius: 20,
            color: ColorConfig.kDarkDialougeColor.withAlpha(25),
          ),
        ],
      ),
      child: CommonMaterialButton(
        buttonText: isProcessing
            ? "Sipariş Oluşturuluyor..."
            : "Siparişi Onayla (${finalPrice.toStringAsFixed(2)} TL)",
        txtColor: ColorConfig.kWhiteColor,
        buttonColor: isProcessing
            ? ColorConfig.kPrimaryColor.withAlpha(125)
            : ColorConfig.kPrimaryColor,
        width: double.infinity,
        height: SizeConfig.kHeight52,
        onButtonClick:
            isProcessing ? null : () => createOrUpdateOrderInFirebase(),
      ),
    );
  }

  Widget _buildOrderNote() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Sipariş Notu",
          style: TextStyle(
            fontSize: FontConfig.kFontSize18,
            fontFamily: FontFamilyConfig.urbanistSemiBold,
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kBlackColor
                : ColorConfig.kWhiteColor,
          ),
        ),
        const SizedBox(height: SizeConfig.kHeight10),
        Container(
          decoration: BoxDecoration(
            color: darkModeController.isLightTheme.value
                ? ColorConfig.kWhiteColor
                : ColorConfig.kDarkModeColor,
            borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
            boxShadow: [
              BoxShadow(
                spreadRadius: 3,
                color: ColorConfig.kDarkDialougeColor.withAlpha(25),
                blurRadius: 5,
              ),
            ],
          ),
          child: TextField(
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Sipariş notunuz (isteğe bağlı)',
              hintStyle: TextStyle(
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kHintColor
                    : ColorConfig.kWhiteColor.withAlpha(125),
              ),
              contentPadding: const EdgeInsets.all(16),
              border: InputBorder.none,
            ),
            style: TextStyle(
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
            ),
            onChanged: (value) {
              setState(() {
                orderNote = value;
              });
            },
          ),
        ),
      ],
    );
  }
}
