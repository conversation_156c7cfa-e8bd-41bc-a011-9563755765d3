import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/radio_controller.dart';
import '../../utils/appbar_common.dart';
import '../../utils/common_material_button.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class PaymentScreen extends StatelessWidget {
  final RadioController radioController = Get.put(RadioController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  PaymentScreen({super.key});

  // Siparişin durumunu "hazırlanıyor" olarak güncelle ve ödeme yöntemini kaydet
  Future<void> completeOrder() async {
    var userId = auth.currentUser!.uid;
    var orderSnapshot = await firestore
        .collection('users')
        .doc(userId)
        .collection('orders')
        .where('status', isEqualTo: 'Sipariş Alındı')
        .get();
    var paymentMethod = radioController.selectedOption.value;

    for (var order in orderSnapshot.docs) {
      var orderData = order.data();
      var items = orderData['items'] as List<dynamic>;

      // Her bir ürün için docId'yi kontrol et ve ekle
      for (var item in items) {
        if (!item.containsKey('docId')) {
          item['docId'] = item['id']; // Eğer docId yoksa, id'yi kullan
        }
      }

      var updatedOrderData = {
        'status': 'hazırlanıyor',
        'paymentMethod': paymentMethod,
        'items': items,
      };

      // Ana orders koleksiyonunu güncelle
      await firestore
          .collection('orders')
          .doc(order.id)
          .update(updatedOrderData);

      // Kullanıcının orders koleksiyonunu güncelle
      await firestore
          .collection('users')
          .doc(userId)
          .collection('orders')
          .doc(order.id)
          .update(updatedOrderData);

      // Firma orders koleksiyonunu güncelle
      String firmId = orderData['firmId'];
      await firestore
          .collection('firms')
          .doc(firmId)
          .collection('orders')
          .doc(order.id)
          .update(updatedOrderData);
    }

    // Web sepetini temizle
    var cartItems = await firestore
        .collection('users')
        .doc(userId)
        .collection('activeCartWeb')
        .doc('cart')
        .collection('items')
        .get();
        
    for (var cartItem in cartItems.docs) {
      await cartItem.reference.delete();
    }
  }

  // Toplam tutarı hesapla
  Future<double> calculateTotalPrice() async {
    var userId = auth.currentUser!.uid;
    var cartSnapshot = await firestore
        .collection('users')
        .doc(userId)
        .collection('activeCartWeb')
        .doc('cart')
        .collection('items')
        .get();
    
    double totalPrice = 0.0;

    for (var item in cartSnapshot.docs) {
      var itemData = item.data();
      totalPrice += itemData['price'] * itemData['quantity'];
    }

    return totalPrice;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<double>(
      future: calculateTotalPrice(), // Toplam tutarı hesaplıyoruz
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        double totalPrice = snapshot.data!;

        return Obx(() => Scaffold(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
              child: CommonAppBar(
                title: StringConfig.payment,
                leadingImage: ImagePath.arrow,
                color: darkModeController.isLightTheme.value
                    ? ColorConfig.kBlackColor
                    : ColorConfig.kWhiteColor,
                leadingOnTap: () {
                  Get.back();
                },
              )),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: SizeConfig.padding20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: SizeConfig.kHeight5),
                      Text(StringConfig.paymentString,
                          style: TextStyle(
                            fontFamily: FontFamilyConfig.urbanistRegular,
                            color: darkModeController.isLightTheme.value
                                ? ColorConfig.kBlackColor
                                : ColorConfig.kWhiteColor,
                            fontWeight: FontWeight.w400,
                            fontSize: FontConfig.kFontSize14,
                          )),
                      const SizedBox(height: SizeConfig.kHeight15),

                      // Kapıda Kredi Kartı ve Kapıda Nakit Ödeme Seçenekleri
                      Column(
                        children: [
                          ListTile(
                            title: const Text('Kapıda Kredi Kartı'),
                            leading: Obx(() => Radio(
                              value: 'Kapıda Kredi Kartı',
                              groupValue: radioController.selectedOption.value,
                              onChanged: (value) {
                                radioController.selectedOption.value = value!;
                              },
                            )),
                          ),
                          ListTile(
                            title: const Text('Kapıda Nakit'),
                            leading: Obx(() => Radio(
                              value: 'Kapıda Nakit',
                              groupValue: radioController.selectedOption.value,
                              onChanged: (value) {
                                radioController.selectedOption.value = value!;
                              },
                            )),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Toplam Tutar
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: SizeConfig.kHeight20),
                  child: Text(
                    'Toplam Tutar: ${totalPrice.toStringAsFixed(2)} TL',
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistSemiBold,
                      fontSize: FontConfig.kFontSize18,
                      color: ColorConfig.kPrimaryColor,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: SizeConfig.padding15),
                    child: CommonMaterialButton(
                        width: double.infinity,
                        buttonColor: ColorConfig.kPrimaryColor,
                        height: SizeConfig.kHeight52,
                        txtColor: ColorConfig.kWhiteColor,
                        buttonText: "Siparişi Tamamla",
                        onButtonClick: () async {
                          await completeOrder();
                          Get.toNamed(AppRoutes.successfulOrder); // Sipariş başarılı ekranına yönlendirme
                        }),
                  ),
                ),
              ],
            ),
          ),
        ));
      },
    );
  }
}
