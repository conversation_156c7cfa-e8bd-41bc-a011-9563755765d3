import 'package:cupertino_will_pop_scope/cupertino_will_pop_scope.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import '../../controller/bottom_controller.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/favourite_controller.dart';
import '../../controller/profile_controller.dart';
import '../../utils/appbar_common.dart';

class FavoriteScreen extends StatefulWidget {
  const FavoriteScreen({Key? key}) : super(key: key);

  @override
  State<FavoriteScreen> createState() => _FavoriteScreenState();
}

class _FavoriteScreenState extends State<FavoriteScreen>
    with SingleTickerProviderStateMixin {
  FavouriteController favouriteController = Get.put(FavouriteController());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (FirebaseAuth.instance.currentUser == null) {
        Get.offAllNamed(AppRoutes.signInScreen);
      }
    });
    favouriteController.tabController = TabController(
      initialIndex: 0,
      length: 1, // Sadece favori firmaları listeleyeceğiz.
      vsync: this,
    );
  }

  @override
  void dispose() {
    favouriteController.tabController.dispose();
    super.dispose();
  }

  ProfileController profileController = Get.put(ProfileController());
  DarkModeController darkModeController = Get.put(DarkModeController());
  BottomNavigationController bottomController =
      Get.put(BottomNavigationController());

  // Firestore'dan favori firmaları çeken fonksiyon
  Future<List<Map<String, dynamic>>> getFavoriteFirms() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return [];

    final userRef =
        FirebaseFirestore.instance.collection('users').doc(user.uid);
    final userDoc = await userRef.get();

    if (!userDoc.exists || !userDoc.data()!.containsKey('favorites')) {
      return [];
    }

    List<String> favoriteFirmIds =
        List<String>.from(userDoc['favorites'] ?? []);
    if (favoriteFirmIds.isEmpty) return [];

    QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('firms')
        .where(FieldPath.documentId, whereIn: favoriteFirmIds)
        .get();

    return snapshot.docs.map((doc) {
      var data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return data;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return ConditionalWillPopScope(
      onWillPop: () {
        bottomController.controller.jumpToPage(0);
        bottomController.selected.value = 0;
        return Future(() => false);
      },
      shouldAddCallback: false,
      child: SafeArea(
          child: Obx(() => Scaffold(
              backgroundColor: darkModeController.isLightTheme.value
                  ? ColorConfig.kWhiteColor
                  : ColorConfig.kBlackColor,
              appBar: PreferredSize(
                  preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
                  child: CommonAppBar(
                    title: StringConfig.myFavorites,
                    leadingImage: ImagePath.restarantMenu,
                    leadingOnTap: () {
                      // Get.back();
                    },
                  )),
              body: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: SizeConfig.kHeight26),
                    Expanded(
                      child: FutureBuilder(
                        future: getFavoriteFirms(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const Center(
                                child: CircularProgressIndicator());
                          } else if (snapshot.hasError) {
                            return const Center(
                                child: Text('Favori firmalar alınamadı.'));
                          } else if (!snapshot.hasData ||
                              (snapshot.data as List).isEmpty) {
                            return const Center(
                                child: Text('Favori firma bulunamadı.'));
                          } else {
                            var firms =
                                snapshot.data as List<Map<String, dynamic>>;
                            return ListView.builder(
                              itemCount: firms.length,
                              itemBuilder: (context, index) {
                                var firm = firms[index];
                                bool isVisible = firm['visible'] ?? false;

                                return Stack(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: SizeConfig.kHeight20,
                                        vertical: SizeConfig.kHeight10,
                                      ),
                                      child: GestureDetector(
                                        onTap: isVisible
                                            ? () {
                                                // Favori firmaya tıklandığında detay sayfasına yönlendirme
                                                Get.toNamed(
                                                    AppRoutes.firmDetail,
                                                    parameters: {
                                                      'firmId': firm['id']
                                                    });
                                              }
                                            : null,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              SizeConfig.borderRadius12,
                                            ),
                                            color: darkModeController
                                                    .isLightTheme.value
                                                ? ColorConfig.kWhiteColor
                                                : ColorConfig.kDarkModeColor,
                                            boxShadow: [
                                              BoxShadow(
                                                color: ColorConfig.kBlackColor
                                                    .withAlpha(50),
                                                blurRadius:
                                                    SizeConfig.borderRadius6,
                                                offset: const Offset(0, 0),
                                              ),
                                            ],
                                          ),
                                          height: SizeConfig.kHeight130,
                                          width: double.infinity,
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.all(
                                                    SizeConfig.kHeight12),
                                                child: Image.network(
                                                  firm['banner'] ??
                                                      'assets/images/placeholder.png',
                                                  fit: BoxFit.cover,
                                                  height: SizeConfig.kHeight100,
                                                  width: SizeConfig.kHeight100,
                                                ),
                                              ),
                                              Expanded(
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                    top: SizeConfig.kHeight25,
                                                    left: SizeConfig.kHeight10,
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        firm['name'] ??
                                                            "Restoran İsmi Yok",
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        maxLines: 1,
                                                        style: TextStyle(
                                                          fontFamily:
                                                              FontFamilyConfig
                                                                  .urbanistSemiBold,
                                                          color: darkModeController
                                                                  .isLightTheme
                                                                  .value
                                                              ? ColorConfig
                                                                  .kBlackColor
                                                              : ColorConfig
                                                                  .kWhiteColor,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: FontConfig
                                                              .kFontSize16,
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                          height: SizeConfig
                                                              .kHeight5),
                                                      const SizedBox(
                                                          height: SizeConfig
                                                              .kHeight15),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (!isVisible)
                                      Positioned.fill(
                                        child: Container(
                                          color: Colors.black.withAlpha(125),
                                          child: const Center(
                                            child: Text(
                                              "Kapalı",
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize:
                                                    FontConfig.kFontSize18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                );
                              },
                            );
                          }
                        },
                      ),
                    )
                  ])))),
    );
  }
}
