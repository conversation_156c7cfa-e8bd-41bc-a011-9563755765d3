class StringConfig {
  static const titleApp = "<PERSON><PERSON><PERSON>ımda!";
  static const foodDeliveryIsThe =
      "<PERSON><PERSON><PERSON>, en sevdiğiniz yemeği kapınıza kadar getirmenin en kolay yoludur";
  static const signIn = "Giriş Yap";
  static const signUp = "Kayıt Ol";
  static const welcomeBack = "Tekrar Hoş Geldiniz";
  static const pleaseEnterYourNumber =
      "Lütfen telefon numaranızı girin, size \n4 haneli bir kod göndereceğiz";
  static const enterPhoneNumber = "Telefon numarasını girin";
  static const orContinueWith = "Veya devam edin";
  static const google = "Google";
  static const faceBook = "Facebook";
  static const dontHaveAnAccount = 'Hesabınız yok mu?  ';
  static const otpVerification = 'OTP Doğrulama ';
  static const sendTheCode = 'Kodu SMS İle gönderdik\n Telefonunuzu kontrol edin';
  static const mobileNumber = '00000 00000';
  static const initialDate = '12/7/2024';
  static const verify = 'Doğrula';
  static const errorText = '\u24D8 Bu kod yanlış.';
  static const reSendCode = 'Kodu tekrar gönder ';
  static const time = '00:35';
  static const createAccount = 'Hesap Oluşturalım';
  static const alreadyHaveAnAccount = 'Zaten bir hesabınız var mı? ';
  static const enterYourName = 'Adınızı girin ';
  static const search = 'Yemek, Restoran, Kategori Ara... ';
  static const searchCountry = 'Ülke Ara ';
  static const searchCuisine = 'Mutfak Ara... ';
  static const address = "Seydişehir ";
  static const categories = "Kategoriler ";
  static const allRestaurants = "Popüler Restoranlar ";
  static const seeAll = "Hepsini Gör ";
  static const short = "Sırala";
  static const nearest = "En Yakın";
  static const cuisines = "Mutfaklar";
  static const pureVeg = "Sadece Vejetaryen";
  static const nonVeg = "Vejetaryen Olmayan";
  static const ratings = "Puanlar";
  static const thankYouForRating = "Değerlendirmeniz için teşekkür ederiz";
  static const ratings45 = "4.5";
  static const km = "30 DK";
  static const reviews = "4.5  (4.259 yorum)";
  static const reviews4 = "4.9  (4.959 yorum)";
  static const reviews4959 = "(4.959 yorum)";
  static const reviews4half = "4.6  (3.359 yorum)";
  static const sort = "Sırala";
  static const mediumInches = "Orta (29 cm)";
  static const normalInches = "Normal (26 cm)";
  static const deliveryTime = "Teslim Süresi";
  static const cost = "Fiyat: Azdan Çoğa";
  static const cost1 = "Fiyat: Çoktan Aza";
  static const highToLow = "Çoktan Aza";
  static const highestPrice = "En Yüksek Fiyat";
  static const lowestPrice = "En Düşük Fiyat";
  static const editProfile = "Profili Düzenle";
  static const newest = "En Yeni";
  static const recommended = "Tavsiye Edilen";
  static const lessThan30Mins = "30 dakikadan az";
  static const lessThan45Mins = "45 dakikadan az";
  static const profile = "Profil";
  static const userprofile = "Abdullah Bey";
  static const phoneNumberString = "+90 00000 00000";
  static const viewActivity = "Etkinliği Görüntüle";
  static const myReviews = "Yorumlarım";
  static const addressSetting = "Adres";
  static const notification = "Bildirimler";
  static const settings = "Ayarlar";
  static const logout = "Çıkış Yap";
  static const restaurants = "Restoranlar";
  static const restaurant1 = "Seydi Fırın";
  static const review1String =
      "Siparişler çok güzel ve hizmet mükemmel! Beğendim ve tekrar sipariş vermek istiyorum🤩😀";
  static const string_6daysAgo = '6 gün önce';
  static const restaurant2 = 'Seydi Cafe';
  static const review2String =
      'Siparişler çok güzel ve hizmet mükemmel!🤗😍';
  static const string_15daysAgo = '15 gün önce';
  static const restaurant3 = 'Seydi Pizza';
  static const review3String =
      'Siparişler çok güzel ve hizmet mükemmel! Beğendim ve tekrar sipariş vermek istiyorum🤩😀';
  static const string_24JanAgo = '24 Ocak 2023';
  static const restaurant4 = 'Seydi Han Cafe';
  static const review4String =
      'Siparişler çok güzel ve hizmet mükemmel! Beğendim ve tekrar sipariş vermek istiyorum🤩😀';
  static const string_3FebAgo = '3 Şubat 2022';
  static const review5String =
      'Bu restoran için çok güzel ve mükemmel hizmet, çok iyi🥰';
  static const share = 'Paylaş';
  static const doubleCheese = 'Çift Peynir...';
  static const farmVillaPizza = 'Seydi Pizza';
  static const bunnyPizza = 'Peynirli Pizzası';
  static const jiraRice = 'Sucuklu Pilavı';
  static const thePie = 'Turta';
  static const pizzaRestaurants = "Döner Restoranları";
  static const dPizza = "Seydi Pizza";
  static const ratingReview = "Puanlar & Yorumlar";
  static const customer1 = "Mustafa Sandal";
  static const customer2 = "Simge Sağın";
  static const offers = "Mevcut Teklifler";
  static const notFound = "Bulunamadı";
  static const sorryTheKeyword =
      'Üzgünüz, girdiğiniz anahtar kelime bulunamadı, lütfen tekrar kontrol edin veya başka bir anahtar kelime ile arayın.';
  static const dsdfdffdf = "dsdfdffdf";
  static const freeDelivery = "Ücretsiz Teslimat";
  static const freeDeliveryMax = "Minimum 600 TL Ücretsiz teslimat";
  static const claim = "Talep Et";
  static const orders = "Siparişler";
  static const promoNewUser = "Yeni Kullanıcı Promosyonu";
  static const validForNewUser = "Yeni kullanıcılar için geçerli";
  static const claimed = "Talep Edildi";
  static const specialFriday = "Özel Cuma";
  static const onlyForFriday = "Sadece Cuma İçin";
  static const extraOff = "Ekstra %20 İndirim";
  static const discountOff = "%20 İndirim";
  static const addressProfile = "Adres";
  static const areYouSureYouWantToLogOut =
      "Bu uygulamadan çıkmak istediğinizden emin misiniz?";
  static const cancel = "İptal";
  static const home = "Ana Sayfa";
  static const myOffice = "Ofisim";
  static const address1 =
      "Seydişehir Mahallesi, Atatürk Caddesi No:40\nKonya, Türkiye";
  static const address2 =
      "Seydişehir Mahallesi, Mevlana Sokak No:57\nKonya, Türkiye";
  static const address3 =
      "Seydişehir Mahallesi, Cumhuriyet Meydanı No:10\nKonya, Türkiye";
  static const sound = "Ses";
  static const vibrate = "Titreşim";
  static const specialOffers = "Özel Teklifler";
  static const promoDiscount = "Promosyon ve İndirim";
  static const payments = "Ödemeler";
  static const payment = "Ödeme";
  static const usingUpi = "Ödeme: Kapıda Ödeme";
  static const cashback = "iade";
  static const appUpdates = "Uygulama Güncellemeleri";
  static const newServiceAvailable = "Yeni Hizmet/Özellik";
  static const addProfilePhoto = "Profil Fotoğrafı Ekle";
  static const takeAPhoto = "Fotoğraf Çek";
  static const uploadFromGallery = "Galeriden Yükle";
  static const darkMode = "Karanlık Mod";
  static const lightMode = "Aydınlık Mod";
  static const helpCenter = "Yardım Merkezi";
  static const termsOfService = "Gizlilik veGüvenlik Politikası";
  static const submit = "Gönder";
  static const orderCancelString =
      "Seydi Pizza'da bir siparişi iptal ettiniz, verdiğimiz rahatsızlıktan dolayı özür dileriz. Hizmetimizi bir dahaki sefere geliştirmeye çalışacağız😥";
  static const orderCancelString1 =
      "Seydi Fırın'ta bir sipariş verdiniz ve 340 TL ödediniz. Yemeğiniz yakında ulaşacak, hizmetimizin tadını çıkarın😍";

  static const faq = "SSS";
  static const contactUs = "Bize Ulaşın";
  static const whatIisApp = "Yemek Kapımda nedir?";
  static const loremIpsumDolorSitAmetConsectetur =
      "Yemekkapımda; Restoran, Market vb işletmelerden sipariş vermek isteyen internet kullanıcılarını aynı ortamda buluşturmak amacı ile geliştirilmiş bir platformdur";
  static const searchTab = "Arama";
  static const howToUseApp = "Yemek Kapımda nasıl kullanılır?";
  static const howDoICancelAnOrders = "Siparişleri nasıl iptal ederim?";
  static const howDoExitTheApp = "Uygulamadan nasıl çıkarım?";
  static const getInTouch = "İletişime geçin";
  static const weLoveToHearFromYou =
      "Sizden haber almak istiyoruz. Dost canlısı ekibimiz her zaman burada sohbet etmek için hazır.";
  static const emailUs = "Bize E-posta Gönderin";
  static const ourFriendlyTeamIsHereToHelp =
      "Dost canlısı ekibimiz size yardımcı olmak için burada";
  static const email1 = "<EMAIL>";
  static const office = "Ofis";
  static const phone = "Telefon";
  static const monFriFromAmToPm = "7/24";
  static const comeSayHelloAtOurOfficeHQ = "Ofis merkezimize gelip merhaba deyin";
  static const email2 = "Seydişehir Konya";
  static const email3 = "+(*************";
  static const termsService = "Hizmet Koşulları";
  static const acceptanceOfTerms = "1. Şartların Kabulü";
  static const discripstion1 =
      "Lorem ipsum dolor sit amet consectetur. Feugiat\n"
      "metus tellus ac consequat donec enim ac aliquet\n"
      "tempor. Imperdiet vitae metus tristique quam enim\n"
      "etiam est. Amet aenean tincidunt hendrerit velit\n"
      "quis. Id arcu scelerisque mattis sit viverra. Morbi\n"
      "tortor ut sed nunc tristique pulvinar fringilla.\n"
      "Dignissim neque tellus nisi adipiscing cras nisi diam\n"
      "pharetra aenean. Pretium auctor consectetur nulla\n"
      "dui a.";
  static const title1 =
      "Lorem ipsum dolor sit amet consectetur. Feugiat\n"
      "metus tellus ac consequat donec enim ac aliquet\n"
      "tempor. Imperdiet vitae metus tristique quam \n"
      "etiam est. Amet aenean tincidunt hendrerit velit\n"
      "quis. Id arcu scelerisque mattis ";
  static const disc1 =
      "Lorem ipsum dolor sit amet consectetur.\n"
      "Feugiat metus tellus ac consequat donec\n"
      "enim ac aliquet tempor. Imperdiet vitae\n"
      "metus tristique quam enim etiam est.\n"
      "Amet aenean tincidunt";
  static const disc2 =
      "Lorem ipsum dolor sit amet consectetur.\n"
      "Feugiat metus tellus ac consequat donec\n"
      "enim ac aliquet tempor.";
  static const definitions = "2. Tanımlar";
  static const dot = "●";
  static const defult = "Varsayılan";
  static const myFavorites = "Favorilerim";
  static const removeFavorite = "Favoriyi Kaldır?";
  static const remove = "Kaldır";
  static const areYorSureYou =
      "Favori restoranı silmek istediğinizden\nemin misiniz?";
  static const active = "Aktif";
  static const completed = "Tamamlandı";
  static const cancelled = "İptal Edildi";
  static const cancelOrder = "Siparişi İptal Et";
  static const trackDriver = "Sürücüyü Takip Et";
  static const trackOrder = "Siparişi Takip Et";
  static const trackOrders = "Siparişleri Takip Et";
  static const dishe = "2 Yemek";
  static const leaveReview = "Yorum Yap";
  static const orderAgain = "Tekrar Sipariş Ver";
  static const orderCancelAreYou =
      "Bu siparişi iptal etmek istediğinizden\nemin misiniz?";
  static const setYourLocation = "Konumunuzu Belirleyin";
  static const timeSquareNYCSurat = "Seydişehir";
  static const orderPlaced = "Sipariş Verildi";
  static const septemberAM = "Eylül 05, 08:09 AM";
  static const onTime = "Zamanında";
  static const pending = "Beklemede";
  static const utranSurat = "Utran, Surat";
  static const orderDetails = "Sipariş Detayları";
  static const string1 = "2 x 1L Pizza (26 cm)\n1 Burger (26 cm)";
  static const deliveryAddress = "Teslimat Adresi";
  static const string2 = "Seydişehir Mahallesi, Atatürk Caddesi No:40\nKonya, Türkiye";
  static const cardName = "Kart İsmi";
  static const cardNumber = "Kart Numarası";
  static const cardNu = "8694 5968 5688 5688";
  static const addNewCard = "Yeni Kart Ekle";
  static const expiryDate = "Son Kullanma Tarihi";
  static const cVV = "CVV";
  static const add = "Ekle";
  static const deliverTo = "Teslimat Yeri";
  static const paymentString = "Kullanmak istediğiniz ödeme yöntemini seçin.";
  static const creditAndDebitCard = "Kredi ve Banka Kartı";
  static const yilmazEsen = "Yılmaz Esen";
  static const otherPaymentOption = "Diğer Ödeme Seçeneği";
  static const debitCreditCard = "Kapıda Kredi Kartı";
  static const netBanking = "Kapıda Nakit";
  static const cardText = "*** *** **** 2756";
  static const successful = "Başarılı";
  static const continues = "Devam";
  static const viewOrders = "Siparişleri Görüntüle";
  static const successString =
      "Siparişiniz onaylandı, yakında teslim edilecek. Siparişinizi siparişleri görüntüle sayfasından takip edebiliirsiniz.";
  static const checkoutOrders = "Siparişleri Tamamla";
  static const ordersSummary = "Sipariş Özeti";
  static const editButton = "Düzenle";
  static const dollor78 = "78 TL";
  static const dollor20 = "20 TL";
  static const dollor34 = "34 TL";
  static const counter1 = "1";
  static const counter2 = "2";
  static const dish1 = "1 Yemek";
  static const addMoreItems = "Daha Fazla Ürün Ekle";
  static const getDiscount = "İndirim Al";
  static const deliveryInstructions = "Teslimat Talimatları";
  static const subtotal = "Ara Toplam";
  static const deliveryFee = "Teslimat Ücreti";
  static const discount = "İndirim";
  static const total = "Toplam";
  static const dollar96 = "96.00 TL";
  static const dollar2 = "2.00 TL";
  static const dollar0 = "00.00 TL";
  static const dollar98 = "98.00 TL";
  static const offDiscount = "(%50 İndirim)";
  static const pizza = "Pizza";
  static const fastFood = "Fast Food";
  static const cocoCola = "Coco-Cola";
  static const shorts = "Atıştırmalık";
  static const bestInPizza = "En İyi Pizza (15 )";
  static const addToCart = "Sepete Ekle - (98 TL)";
  static const placeOrder = "Siparişi Ver - (98 TL)";
  static const payNow = "Şimdi Öde - (98 TL)";
  static const myCart = "Sepetim";
  static const addNewAddress = "Yeni Adres Ekle";
  static const editAddress = "Adresi Düzenle";
  static const saveNewAddress = "Yeni Adresi Kaydet";
  static const saveAddress = "Adresi Kaydet";
  static const applyButton = "Uygula";
  static const location = "Konum";
  static const street = "Sokak";
  static const street2 = "Sokak 2";
  static const landmark = "Yer İşareti";
  static const city = "Şehir";
  static const pincode = "Posta Kodu";
  static const state = "Eyalet";
  static const country = "Ülke";

  static const offUpToDiscount = "50TL'a kadar %50 İNDİRİM";
  static const upTo20Discount = "%20 İndirim";
  static const menusDiscount = " Tüm menülerde %20 indirim";
  static const save30Discount = "Bu siparişte 30TL daha tasarruf edin";
  static const discountCode =  " YMKKPMDA";
  static const userPromo = "Yeni Kullanıcı Promosyonu";
  static const validUserString = " Sadece yeni kullanıcılar için geçerli";
  static const freeDeliveryFee = "Ücretsiz Teslimat Ücreti";
  static const freeDeliveryMax6 = " Minimum 600 TL ücretsiz teslimat";
  static const freeDeliveryMaxDollar6 = " Minimum 600 TL ücretsiz teslimat";
  static const weekendSpecial = "Hafta Sonu Özel";
  static const validOnSatSun = " Cumartesi ve Pazar günleri geçerli";
  static const yearEndPromo = "Yıl Sonu Promosyonu";
  static const newYearPromo = "Yeni Yıl Promosyonu";
  static const discount20 = "%20 İndirim";
  static const skipButton = "Geç";
  static const dPizzaAddress = "Büyükşehir Cad. No:100, Seydişehir Konya";
  static const dPizzaAddressNew = "Büyükşehir Cad. No:100, Seydişehir Konya";
  static const dPizzaReview = "Lütfen Seydi Pizza restoranı için deneyimlerinizi ve geri bildirimlerinizi paylaşın";
  static const villaPizzaReview = "Lütfen Seydi Pizza restoranı için deneyimlerinizi ve geri bildirimlerinizi paylaşın";
  static const writeYourReview = "Yorumunuzu Yazın";
  static const dPizzaReviewWrite = "Bu restoranın hizmeti harika ve çok hızlı teslimat, yemek kalitesi en iyi 😍🥰😊";
  static const String activeOrders = 'Aktif Siparişler';

  static const pizza2x = "2 x";
  static const mins22 = "22 dakika";
  static const coco = "Koko";
  static const deliveryTiming = "05 Temmuz 2023 saat 16:30'da";
  static const searchRestaurantAndDishes = "Restoran ve Yemek Arayın";
  static const viewMenu = "Menüyü Görüntüle";
  static const orderSummary = "Sipariş Özeti";
  static const yourDeliveryPartner = "Teslimat ortağınız";
  static const profilemain = "Abdullah Bey, 0000000xxx";
  static const profileName = "Abdullah Bey";
  static const orderNumber = "Sipariş Numarası";
  static const orderInNumber = "54236637657";
  static const notifications = "Bildirimler";
  static const today = "Bugün";
  static const yourPizzaOrderCancel = "Pizza Siparişiniz İptal Edildi";
  static const timing2PM = " 14:40";
  static const timing4PM = " 16:40";
  static const timing240PM = " 14:40";
  static const newString = "Yeni";
  static const orderSuccess = "Sipariş Başarılı";
  static const yesterday = "Önceki";
  static const date2 = "02/30";
  static const rating49 = "4.9";
  static const shareButton = " Paylaş";
  static const freshAvocadoJuice = "Churchill Soda";
  static const seydiPizza = "Seydi Pizza";
  static const off50 = "(%50 İndirim)";
  static const martinozPizza = "Martino’z Pizza";
  static const overview = "Genel Bakış";
  static const openNow = "Şimdi Açık";
  static const resetButton = "Sıfırla";
  static const overviewScreenString = "Biberin, domatesin tazeliği, consectetur. Elit sed integer donec mauris massa turpis. Senectus faucibus dictum blandit pharetra mauris in tortor vulputate. Ultrices in";
  static const laPinozPizza = "La Pino’z Pizza";
  static const burgerKing = "Burger King";
  static const kailashSweet = "Kailash Tatlıları...";
  static const grillBar = "Grill Bar";

  static const burger = "Burger";
  static const chinese = "Çin";
  static const french = "Fransız";
  static const sweet = "Tatlılar";
  static const snack = "Aperatifler";
  static const pasta = "Makarna";
  static const alooPuri = "Aalupuri";
  static const bhaji = "Bhaji";
  static const capsicumPizza = "Biberli Pizza";
  static const onionPizza = "Soğanlı Pizza";
  static const dollor315 = "31.5 TL";
  static const dollor105 = "105 TL";
  static const dollor67 = "67.2 TL";
  static const dollor31 = "31 TL";
  static const dosa = "Dosa";
  static const biryani = "Biryani";
  static const iceCreame = "Dondurma";
  static const thali = "Thali";
  static const sandwich = "Sandviç";
  static const cake = "Pasta";
  static const skylinePizza = "Skyline Pizza";
  static const tipsyToma = "Tipsy Toma...";
  static const avoidRingingBell = "Zili\nçalmayın";
  static const leaveAtDoor = "Kapıda\nbırakın";
  static const avoidCalling = "Aramaktan\nkaçının";
  static const leaveWithSecurity = "Güvenlikte\nbırakın";
  static const directionToReach = "Geldiğinizde\nArayın";
  static const extraToppings = "Ekstra Malzemeler (Büyük Pizza)";
  static const selectUpTo5Options = "Maksimum 5 seçenek seçin";
  static const forYou = "Sizin İçin";
  static const pizzaDishes = "Pizza Yemekleri";
  static const offersAreAvailable = " İndirimler mevcut";
  static const description = "Açıklama";
  static const orderConfirmed = "Sipariş Onaylandı";
  static const outForDelivery = "Teslimat için Yolda";
  static const orderDelivered = "Sipariş Teslim Edildi";
  static const name = "Ad";
  static const phoneNumber = "Telefon Numarası";
  static const email = "E-posta";
  static const date = "Tarih";
  static const gender = "Cinsiyet";
  static const saveButton = "Kaydet";
  static const change = "Değiştir";
  static const textField1 = "Seydişehir Mahallesi";
  static const textField2 = "56 - Düşük Sokak";
  static const textField3 = "Konya";
  static const textField4 = "234658";
  static const textField5 = "Birlik Anıtı";
  static const firstName = "İsim";
  static const lastName = "Soyisim";
}
