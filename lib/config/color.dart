import 'dart:ui';

/// Color

class ColorConfig {
 static Color kPrimaryColor=const Color(0xffEE7201);
 static Color kWhiteColor=const Color(0xffFFFFFF);
 static Color kButtonLightColor=const Color(0xffFFEAEB);
 static Color kBlackColor=const Color(0xff1F1F1F);
 static Color kHintColor=const Color(0xff616161);
 static Color kFillColor=const Color(0xFFFAFAFA);
 static Color kBgColor=const Color(0xFFD4D3D3);
 static Color kDividerColor=const Color(0xFFEAEAEA);
 static Color kBorderColor=const Color(0xFFF5F5F5);
 static Color kErrorColor=const Color(0xFFF82D2D);
 static Color kTabColor=const Color(0xffF1F1F1);
 static Color kTextColor=const Color(0xff181A20);
 static Color kGreenColor=const Color(0xff1FA200);
 static Color kCancelColor=const Color(0xffF75655);
 static Color kShadowColor=const Color(0xffBBBBBB);
 static Color kDarkModeColor=const Color(0xff1f222a);
 static Color kDarkModeDividerColor=const Color(0xffE0E0E0);
 static Color kDarkDialougeColor=const Color(0xff35383F);
 static Color kContainerLightColor=const Color(0xffFFEAEB);
 static Color kTextfieldTextColor=const Color(0xff616161);
 static Color kTextLightTextColor=const Color(0xff424242);
 static Color kTextFieldLightTextColor=const Color(0xff9FA0A2);
}



