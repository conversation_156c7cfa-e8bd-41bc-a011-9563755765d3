class SizeConfig {
  /// borderRadius
  static const double borderRadius10 = 10;
  static const double borderRadius12 = 12;
  static const double borderRadius25 = 25;
  static const double borderRadius14 = 14;
  static const double borderRadius15 = 15;
  static const double borderRadius50 = 50;
  static const double borderRadius20 = 20;
  static const double borderRadius22 = 22;
  static const double borderRadius30 = 30;
  static const double borderRadius34 = 34;
  static const double borderRadius38 = 38;
  static const double borderRadius11 = 11;
  static const double borderRadius70 = 70;
  static const double borderRadius8 = 8.0;
  static const double borderRadius24 = 24;
  static const double borderRadius40 = 40;
  static const double borderRadius5 = 5;
  static const double borderRadius35 = 35;
  static const double borderRadius6 = 6;
  static const double borderRadius3 = 3;
  static const double borderRadius16 = 16.0;

  ///padding
  static const double padding24 = 24;
  static const double padding20 = 20;
  static const double padding12 = 12;
  static const double padding13 = 13;
  static const double padding14 = 14;
  static const double padding10 = 10;
  static const double padding19 = 19;
  static const double padding18 = 18;
  static const double padding17 = 17;
  static const double padding16 = 16;
  static const double padding11 = 11;
  static const double padding15 = 15;
  static const double padding8 = 8;
  static const double padding9 = 9;
  static const double padding5 = 5;
  static const double padding4 = 4;
  static const double padding3 = 3;
  static const double padding2 = 2;
  static const double padding40 = 40;

  /// height
  static const double kHeight0 = 0;
  static const double kHeight1 = 1;
  static const double kHeight144 = 144;
  static const double kHeight99 = 99;
  static const double kHeight12 = 12.0;
  static const double kHeight15 = 15;
  static const double kHeight2 = 2;
  static const double kHeight24 = 24.0;
  static const double kHeight25 = 25;
  static const double kHeight26 = 26;
  static const double kHeight20 = 20;
  static const double kHeight4 = 4;
  static const double kHeight66 = 66;
  static const double kHeight49 = 49;
  static const double kHeight5 = 5;
  static const double kHeight50 = 50;
  static const double kHeight60 = 60;
  static const double kHeight70 = 70;
  static const double kHeight85 = 85;
  static const double kHeight69 = 69;
  static const double kHeight38 = 38;
  static const double kHeight30 = 30;
  static const double kHeight97 = 97;
  static const double kHeight64 = 64;
  static const double kHeight100 = 100;
  static const double kHeight10 = 10;
  static const double kHeight35 = 35;
  static const double kHeight94 = 94;  static const double kHeight91 = 91;
  static const double kHeight105 = 105;
  static const double kHeight45 = 45;
  static const double kHeight500 = 500;
  static const double kHeight600 = 600;
  static const double kHeight700 = 700;
  static const double kHeight900 = 900;
  static const double kHeight910 = 910;
  static const double kHeight920 = 920;
  static const double kHeight1000 = 1000;
  static const double kHeight550 = 550;
  static const double kHeight8 = 8;
  static const double kHeight800 = 800;
  static const double kHeight400 = 400;
  static const double kHeight410 = 410;
  static const double kHeight420 = 420;
  static const double kHeight430 = 430;
  static const double kHeight440 = 440;
  static const double kHeight445 = 445;
  static const double kHeight564 = 564;
  static const double kHeight40 = 40;
  static const double kHeight33 = 33;
  static const double kHeight84 = 84;
  static const double kHeight88 = 88;
  static const double kHeight42 = 42;
  static const double kHeight18 = 18;
  static const double kHeight18point = 18.5;
  static const double kHeight36 = 36;
  static const double kHeight23 = 23;
  static const double kHeight6 = 6;
  static const double kHeight34 = 34;
  static const double kHeight32 = 32;
  static const double kHeight115 = 115;
  static const double kHeight9 = 9;
  static const double kHeight21 = 21;
  static const double kHeight53 = 53;
  static const double kHeight72 = 72;
  static const double kHeight58 = 58;
  static const double kHeight68 = 68;
  static const double kHeight78 = 78;
  static const double kHeight117 = 117;
  static const double kHeight16 = 16.0;
  static const double kHeight17 = 17;
  static const double kHeight14 = 14;
  static const double kHeight250 = 250;
  static const double kHeight150 = 150;
  static const double kHeight151 = 151;
  static const double kHeight152 = 152;
  static const double kHeight154 = 154;
  static const double kHeight156 = 156;
  static const double kHeight55 = 55;
  static const double kHeight13 = 13;
  static const double kHeight11 = 11;
  static const double kHeight7 = 7;
  static const double kHeight65 = 65;
  static const double kHeight67 = 67;
  static const double kHeight245 = 245;
  static const double kHeight27 = 27;
  static const double kHeight39 = 39;
  static const double kHeight46 = 46;
  static const double kHeight44 = 44;
  static const double kHeight110 = 110;
  static const double kHeight240 = 240;
  static const double kHeight238 = 238;
  static const double kHeight275 = 275;
  static const double kHeight3 = 3;
  static const double kHeight3point = 3.5;
  static const double kHeight22 = 22;
  static const double kHeight128 = 128;
  static const double kHeight153 = 153;
  static const double kHeight200 = 200;
  static const double kHeight280 = 280;
  static const double kHeight260 = 260;
  static const double kHeight188 = 188;
  static const double kHeight75 = 75;
  static const double kHeight29 = 29;
  static const double kHeight124 = 124;
  static const double kHeight52 = 52;
  static const double kHeight340 = 340;
  static const double kHeight77 = 77;
  static const double kHeight270 = 270;
  static const double kHeight180 = 180;
  static const double kHeight320 = 320;
  static const double kHeight86 = 86;
  static const double kHeight37 = 37;
  static const double kHeight28 = 28;
  static const double kHeight41 = 41;
  static const double kHeight140 = 140;
  static const double kHeight143 = 143;
  static const double kHeight90 = 90;
  static const double kHeight95 = 95;
  static const double kHeight120 = 120;
  static const double kHeight101 = 101;
  static const double kHeight79 = 79;
  static const double kHeight73 = 73;
  static const double kHeight48 = 48;
  static const double kHeight350 = 350;
  static const double kHeight130 = 130;
  static const double kHeight132 = 132;
  static const double kHeight134 = 134;
  static const double kHeight138 = 138;
  static const double kHeight125 = 125;
  static const double kHeight126 = 126;
  static const double kHeight127 = 127;
  static const double kHeight104 = 104;
  static const double kHeight330 = 330;
  static const double kHeight335 = 335;
  static const double kHeight148 = 148;
  static const double kHeight370 = 370;
  static const double kHeight380 = 380;
  static const double kHeight112 = 112;
  static const double kHeight113 = 113;
  static const double kHeight80 = 80;
  static const double kHeight71 = 71;
  static const double kHeight170 = 170;
  static const double kHeight19 = 19;
  static const double kHeight158 = 158;
  static const double kHeight54 = 54;
  static const double kHeight300 = 300;
  static const double kHeight310 = 310;
  static const double kHeight513 = 513;

  /// height point
  static const double kH45 = 4.5;
  static const double kH44 = 4.4;
  static const double kH43 = 4.3;
  static const double kH47 = 4.7;
  static const double kH18 = 18.5;
  static const double kH20 = 20.4;
  static const double kH21 = 2.1;
  static const double kH22 = 2.2;
  static const double kH02 = 0.2;
  static const double kH03 = 0.3;
  static const double kH15 = 1.5;
  static const double kH16 = 1.6;
  static const double kH12 = 1.2;
  static const double kH13 = 1.3;
  static const double kH25 = 2.5;
  static const double kH26 = 2.6;
  static const double kH24 = 2.4;
  static const double kH27 = 2.7;
  static const double kH53 = 5.3;
  static const double kH012 = 0.12;
  static const double kH005 = 0.05;
  static const double kH004 = 0.04;
  static const double kH14 = 1.4;
  static const double kH015 = 0.15;
  static const double kH018 = 0.18;
  static const double kH11 = 1.1;
  static const double kH23 = 2.3;
  static const double kH07 = 0.7;
  static const double kH06 = 0.6;
  static const double kH05 = 0.5;

  /// opaCity
  static const double kOpp4 = 0.4;
  static const double kOpp03 = 0.3;
  static const double kOpp02 = 0.2;
  static const double kOpp1 = 0.1;
  static const double kOpp9 = 0.9;
  static const double kOpp08 = 0.8;
  static const double kOpp06 = 0.6;
  static const double kOpp07 = 0.7;
  static const double kOpp05 = 0.5;

  /// width
  static const double kWidth66 = 66;
  static const double kWidth15 = 15;
  static const double kWidth10 = 10;
  static const double kWidth25 = 25;
  static const double kWidth20 = 20;
  static const double kWidth100 = 100;
  static const double kWidth12 = 12.0;
  static const double kWidth24 = 24.0;

  ///
  static const int kInt20 = 20;
  static const int kInt5 = 5;
  static const int kInt2 = 2;
  static const int kInt3 = 3;
  static const int kInt100 = 100;
  static const int kInt90 = 90;
  static const int kInt80 = 80;
  static const int kInt40 = 40;
  static const int kInt60 = 60;
  static const int kInt10 = 10;
}








