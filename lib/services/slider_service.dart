import 'package:cloud_firestore/cloud_firestore.dart';
import '../model/slider_model.dart';

class SliderService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<List<SliderModel>> getSliders() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('sliders')
          .get();

      final sliders = snapshot.docs.map((doc) => SliderModel.fromFirestore(
        doc.data() as Map<String, dynamic>,
        doc.id,
      )).toList();

      // Log the fetched sliders

      return sliders;
    } catch (e) {
      return [];
    }
  }
}