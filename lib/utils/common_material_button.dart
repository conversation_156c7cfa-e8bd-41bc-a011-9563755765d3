import 'package:flutter/material.dart';
import 'package:yemekkapimda/config/font_family.dart';

import '../config/font_config.dart';
import '../config/size_config.dart';

class CommonMaterialButton extends StatelessWidget {
  final String? buttonText;
  final VoidCallback? onButtonClick;
  final bool isButtonText;
  final double? textSize;
  final ButtonStyle? style;
  final Color? txtColor;
  final Color? buttonColor;
  final Widget? widget;
  final IconData? icon;
  final double? height;
  final double? width;
  final bool isButtonColor;
  final double? padding;
  final OutlinedBorder? shape;

  const CommonMaterialButton(
      {Key? key,
      required this.buttonText,
      this.onButtonClick,
      this.isButtonText = false,
      this.padding,
      this.style,
      this.textSize,
      this.txtColor,
      this.buttonColor,
      this.widget,
      this.height,
      this.width,
      this.isButtonColor = false,
      this.icon,
      this.shape})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: padding ?? 20),
      child: SizedBox(
        height: height,
        width: width,
        child: ElevatedButton(
          onPressed: onButtonClick,
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            elevation: 0,
            shape: shape ??
                RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(SizeConfig.borderRadius40),
                ),
            padding: const EdgeInsets.symmetric(
              horizontal: SizeConfig.kHeight10,
              vertical: SizeConfig.kHeight10,
            ),
          ),
          child: Text(
            buttonText ?? '',
            style: TextStyle(
              fontWeight: FontWeight.w700,
              color: txtColor,
              fontFamily: FontFamilyConfig.urbanistBold,
              fontSize:
                  isButtonText == true ? textSize : FontConfig.kFontSize16,
            ),
          ),
        ),
      ),
    );
  }
}
