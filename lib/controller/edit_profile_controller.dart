import 'package:flutter/material.dart';
import 'package:yemekkapimda/config/string_config.dart';
import 'package:get/get.dart';

class EditProfileController extends GetxController {
  TextEditingController firstNameController = TextEditingController(text: StringConfig.firstName);
  TextEditingController lastNameController = TextEditingController(text: StringConfig.lastName);
  TextEditingController phoneNumberController = TextEditingController(text: StringConfig.mobileNumber);
  TextEditingController emailController = TextEditingController(text: StringConfig.email);
  TextEditingController dateController = TextEditingController(text: StringConfig.initialDate);
  TextEditingController genderController = TextEditingController(text: StringConfig.gender);

  DateTime? selectedDate;
}