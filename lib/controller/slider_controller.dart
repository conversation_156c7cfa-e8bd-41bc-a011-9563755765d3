import 'package:get/get.dart';
import '../model/slider_model.dart';
import '../services/slider_service.dart';

class SliderController extends GetxController {
  final SliderService _sliderService = SliderService();
  final sliders = <SliderModel>[].obs;
  final currentIndex = 0.obs;
  final isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    fetchSliders();
  }

  Future<void> fetchSliders() async {
    try {
      isLoading(true);
      final data = await _sliderService.getSliders();
      if (data.isNotEmpty) {
        sliders.assignAll(data);
        // Log the sliders after fetching
      }
    } finally {
      isLoading(false);
    }
  }

  void updateIndex(int index) {
    currentIndex.value = index;
  }
}