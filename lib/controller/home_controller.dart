import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../config/size_config.dart';
import '../config/string_config.dart';

class HomeController extends GetxController{

  RxInt pageViewIndex = 0.obs;
  int selectedContainerIndex = -1;
  List<bool> deliverToIndex = [false, false, false, false];
  RxList<bool> internationalTripIndex = List.generate(7, (index) => false).obs;
  RxString dropDownValue = StringConfig.pureVeg.obs;
  RxBool isSort=false.obs;
  RxBool isPureVeg=false.obs;
  RxBool isRating=false.obs;
  RxBool isCuisine=false.obs;
  int counter = 0;
  double currentIndex = SizeConfig.kHeight0;
  int index = 0;
  List<bool> isSelected = List.generate(5, (index) => false);
  TextEditingController searchController = TextEditingController();







}