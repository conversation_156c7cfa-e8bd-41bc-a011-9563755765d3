import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import '../config/string_config.dart';

class AddressController extends GetxController {
  TextEditingController streetController = TextEditingController();
  TextEditingController street2Controller = TextEditingController();
  TextEditingController landmarkController = TextEditingController();
  TextEditingController descriptionController = TextEditingController(); // Add this line

  RxList<bool> selectItems = List.generate(8, (index) => false).obs;
  List<Map<String, dynamic>> addressList = [
    {"text": StringConfig.myOffice, "subText": StringConfig.address2},
  ];

  List<Map<String, dynamic>> notificationText = [
    {"text": StringConfig.sound},
    {"text": StringConfig.vibrate},
    {"text": StringConfig.specialOffers},
    {"text": StringConfig.promoDiscount},
    {"text": StringConfig.payments},
    {"text": StringConfig.cashback},
    {"text": StringConfig.appUpdates},
    {"text": StringConfig.newServiceAvailable},
  ];

  @override
  void onClose() {
    streetController.dispose();
    street2Controller.dispose();
    landmarkController.dispose();
    descriptionController.dispose(); // Dispose the controller
    super.onClose();
  }
}