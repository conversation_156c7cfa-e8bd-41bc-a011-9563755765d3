import 'package:yemekkapimda/config/string_config.dart';
import 'package:get/get.dart';

class PizzaController extends GetxController {

  int currentTabIndex = 0;
  String appBarTitle = StringConfig.pizzaRestaurants;

  List<Map> pizzaSize = [
    {"name": "Normal (26 cm)", "isChecked": false, "name1": "34 TL"},
    {"name": "Medium (29 cm)", "isChecked": false, "name1": "60 TL"},
    {"name": "Large (32 cm)", "isChecked": false, "name1": "98 TL"}
  ];

  List<Map> availableHobbies = [
    {"name": "Fast Food", "isChecked": false},
    {"name": "North Indian", "isChecked": false},
    {
      "name": "Desserts",
      "isChecked": false,
    },
    {"name": "Pizza", "isChecked": false},
    {"name": "Chinese", "isChecked": false},
    {"name": "Shake", "isChecked": false},
    {"name": "SandWish", "isChecked": false}
  ];
}