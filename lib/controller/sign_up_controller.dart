import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignUpController extends GetxController {
  bool isValid = true;
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();

  String selectedCountryCode = '+90';

  // Reaktif bir şekilde verificationId'yi takip etmek için RxString kullanıyoruz
  var verificationId = ''.obs; // verificationId'yi Rx olarak tanımlıyoruz

  // verificationId'yi ayarlamak için setter
  void setVerificationId(String id) {
    verificationId.value = id;
  }

  // verificationId'yi almak için getter
  String getVerificationId() {
    return verificationId.value;
  }
}