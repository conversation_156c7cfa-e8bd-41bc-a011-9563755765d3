class SliderModel {
  final String id;
  final String imageUrl;
  final String altText;
  final String title;
  final int order;

  SliderModel({
    required this.id,
    required this.imageUrl,
    required this.altText,
    required this.title,
    required this.order,
  });

  factory SliderModel.fromFirestore(Map<String, dynamic> data, String id) {
    return SliderModel(
      id: id,
      imageUrl: data['imageUrl'] ?? '',
      altText: data['altText'] ?? '',
      title: data['title'] ?? '',
      order: data['order'] ?? 0,
    );
  }
}