// class Country {
//   final String name;
//   final String code;
//   final String flagCode;
//
//   Country({required this.name, required this.code, required this.flagCode});
// }
//
// List<Country> countries = [
//   Country(name: 'Turkey', code: '+90', flagCode: 'TR'),

//   Country(name: 'Afghanistan', code: '+93', flagCode: 'AF'),
//   Country(name: 'Albania', code: '+355', flagCode: 'AL'),
//   Country(name: 'Algeria', code: '+213', flagCode: 'DZ'),
//   Country(name: 'Andorra', code: '+376', flagCode: 'AD'),
//   Country(name: 'Angola', code: '+244', flagCode: 'AO'),
//   Country(name: 'Antigua and Barbuda', code: '+1-268', flagCode: 'AG'),
//   Country(name: 'Argentina', code: '+54', flagCode: 'AR'),
//   Country(name: 'Armenia', code: '+374', flagCode: 'AM'),
//   Country(name: 'Australia', code: '+61', flagCode: 'AU'),
//   Country(name: 'Austria', code: '+43', flagCode: 'AT'),
//   Country(name: 'Azerbaijan', code: '+994', flagCode: 'AZ'),
//   Country(name: 'Bahamas', code: '+1-242', flagCode: 'BS'),
//   Country(name: 'Bahrain', code: '+973', flagCode: 'BH'),
//   Country(name: 'Bangladesh', code: '+880', flagCode: 'BD'),
//   Country(name: 'Barbados', code: '+1-246', flagCode: 'BB'),
//   Country(name: 'Belarus', code: '+375', flagCode: 'BY'),
//   Country(name: 'Belgium', code: '+32', flagCode: 'BE'),
//   Country(name: 'Belize', code: '+501', flagCode: 'BZ'),
//   Country(name: 'Benin', code: '+229', flagCode: 'BJ'),
//   Country(name: 'Bhutan', code: '+975', flagCode: 'BT'),
//   Country(name: 'Bolivia', code: '+591', flagCode: 'BO'),
//   Country(name: 'Bosnia and Herzegovina', code: '+387', flagCode: 'BA'),
//   Country(name: 'Botswana', code: '+267', flagCode: 'BW'),
//   Country(name: 'Brazil', code: '+55', flagCode: 'BR'),
//   Country(name: 'Brunei', code: '+673', flagCode: 'BN'),
//   Country(name: 'Bulgaria', code: '+359', flagCode: 'BG'),
//   Country(name: 'Burkina Faso', code: '+226', flagCode: 'BF'),
//   Country(name: 'Burundi', code: '+257', flagCode: 'BI'),
//   Country(name: 'Cabo Verde', code: '+238', flagCode: 'CV'),
//   Country(name: 'Cambodia', code: '+855', flagCode: 'KH'),
//   Country(name: 'Cameroon', code: '+237', flagCode: 'CM'),
//   Country(name: 'Canada', code: '+1', flagCode: 'CA'),
//   Country(name: 'Central African Republic', code: '+236', flagCode: 'CF'),
//   Country(name: 'Chad', code: '+235', flagCode: 'TD'),
//   Country(name: 'Chile', code: '+56', flagCode: 'CL'),
//   Country(name: 'China', code: '+86', flagCode: 'CN'),
//   Country(name: 'Colombia', code: '+57', flagCode: 'CO'),
//   Country(name: 'Comoros', code: '+269', flagCode: 'KM'),
//   Country(name: 'Congo (Congo-Brazzaville)', code: '+242', flagCode: 'CG'),
//   Country(name: 'Costa Rica', code: '+506', flagCode: 'CR'),
//   Country(name: 'Croatia', code: '+385', flagCode: 'HR'),
//   Country(name: 'Cuba', code: '+53', flagCode: 'CU'),
//   Country(name: 'Cyprus', code: '+357', flagCode: 'CY'),
//   Country(name: 'Czechia (Czech Republic)', code: '+420', flagCode: 'CZ'),
//   Country(name: 'Democratic Republic of the Congo (Congo-Kinshasa)', code: '+243', flagCode: 'CD'),
//   Country(name: 'Denmark', code: '+45', flagCode: 'DK'),
//   Country(name: 'Djibouti', code: '+253', flagCode: 'DJ'),
//   Country(name: 'Dominica', code: '+1-767', flagCode: 'DM'),
//   Country(name: 'Dominican Republic', code: '+1-809', flagCode: 'DO'),
//   Country(name: 'Ecuador', code: '+593', flagCode: 'EC'),
//   Country(name: 'Egypt', code: '+20', flagCode: 'EG'),
//   Country(name: 'El Salvador', code: '+503', flagCode: 'SV'),
//   Country(name: 'Equatorial Guinea', code: '+240', flagCode: 'GQ'),
//   Country(name: 'Eritrea', code: '+291', flagCode: 'ER'),
//   Country(name: 'Estonia', code: '+372', flagCode: 'EE'),
//   Country(name: 'Eswatini (fmr. "Swaziland")', code: '+268', flagCode: 'SZ'),
//   Country(name: 'Ethiopia', code: '+251', flagCode: 'ET'),
//   Country(name: 'Fiji', code: '+679', flagCode: 'FJ'),
//   Country(name: 'Finland', code: '+358', flagCode: 'FI'),
//   Country(name: 'France', code: '+33', flagCode: 'FR'),
//   Country(name: 'Gabon', code: '+241', flagCode: 'GA'),
//   Country(name: 'Gambia', code: '+220', flagCode: 'GM'),
//   Country(name: 'Georgia', code: '+995', flagCode: 'GE'),
//   Country(name: 'Germany', code: '+49', flagCode: 'DE'),
//   Country(name: 'Ghana', code: '+233', flagCode: 'GH'),
//   Country(name: 'Greece', code: '+30', flagCode: 'GR'),
//   Country(name: 'Grenada', code: '+1-473', flagCode: 'GD'),
//   Country(name: 'Guatemala', code: '+502', flagCode: 'GT'),
//   Country(name: 'Guinea', code: '+224', flagCode: 'GN'),
//   Country(name: 'Guinea-Bissau', code: '+245', flagCode: 'GW'),
//   Country(name: 'Guyana', code: '+592', flagCode: 'GY'),
//   Country(name: 'Haiti', code: '+509', flagCode: 'HT'),
//   Country(name: 'Holy See', code: '+379', flagCode: 'VA'),
//   Country(name: 'Honduras', code: '+504', flagCode: 'HN'),
//   Country(name: 'Hungary', code: '+36', flagCode: 'HU'),
//   Country(name: 'Iceland', code: '+354', flagCode: 'IS'),
//   Country(name: 'India', code: '+91', flagCode: 'IN'),
//   Country(name: 'Indonesia', code: '+62', flagCode: 'ID'),
//   Country(name: 'Iran', code: '+98', flagCode: 'IR'),
//   Country(name: 'Iraq', code: '+964', flagCode: 'IQ'),
//   Country(name: 'Ireland', code: '+353', flagCode: 'IE'),
//   Country(name: 'Israel', code: '+972', flagCode: 'IL'),
//   Country(name: 'Italy', code: '+39', flagCode: 'IT'),
//   Country(name: 'Ivory Coast', code: '+225', flagCode: 'CI'),
//   Country(name: 'Jamaica', code: '+1-876', flagCode: 'JM'),
//   Country(name: 'Japan', code: '+81', flagCode: 'JP'),
//   Country(name: 'Jordan', code: '+962', flagCode: 'JO'),
//   Country(name: 'Kazakhstan', code: '+7', flagCode: 'KZ'),
//   Country(name: 'Kenya', code: '+254', flagCode: 'KE'),
//   Country(name: 'Kiribati', code: '+686', flagCode: 'KI'),
//   Country(name: 'Kuwait', code: '+965', flagCode: 'KW'),
//   Country(name: 'Kyrgyzstan', code: '+996', flagCode: 'KG'),
//   Country(name: 'Laos', code: '+856', flagCode: 'LA'),
//   Country(name: 'Latvia', code: '+371', flagCode: 'LV'),
//   Country(name: 'Lebanon', code: '+961', flagCode: 'LB'),
//   Country(name: 'Lesotho', code: '+266', flagCode: 'LS'),
//   Country(name: 'Liberia', code: '+231', flagCode: 'LR'),
//   Country(name: 'Libya', code: '+218', flagCode: 'LY'),
//   Country(name: 'Liechtenstein', code: '+423', flagCode: 'LI'),
//   Country(name: 'Lithuania', code: '+370', flagCode: 'LT'),
//   Country(name: 'Luxembourg', code: '+352', flagCode: 'LU'),
//   Country(name: 'Madagascar', code: '+261', flagCode: 'MG'),
//   Country(name: 'Malawi', code: '+265', flagCode: 'MW'),
//   Country(name: 'Malaysia', code: '+60', flagCode: 'MY'),
//   Country(name: 'Maldives', code: '+960', flagCode: 'MV'),
//   Country(name: 'Mali', code: '+223', flagCode: 'ML'),
//   Country(name: 'Malta', code: '+356', flagCode: 'MT'),
//   Country(name: 'Marshall Islands', code: '+692', flagCode: 'MH'),
//   Country(name: 'Mauritania', code: '+222', flagCode: 'MR'),
//   Country(name: 'Mauritius', code: '+230', flagCode: 'MU'),
//   Country(name: 'Mexico', code: '+52', flagCode: 'MX'),
//   Country(name: 'Micronesia', code: '+691', flagCode: 'FM'),
//   Country(name: 'Moldova', code: '+373', flagCode: 'MD'),
//   Country(name: 'Monaco', code: '+377', flagCode: 'MC'),
//   Country(name: 'Mongolia', code: '+976', flagCode: 'MN'),
//   Country(name: 'Montenegro', code: '+382', flagCode: 'ME'),
//   Country(name: 'Morocco', code: '+212', flagCode: 'MA'),
//   Country(name: 'Mozambique', code: '+258', flagCode: 'MZ'),
//   Country(name: 'Myanmar (formerly Burma)', code: '+95', flagCode: 'MM'),
//   Country(name: 'Namibia', code: '+264', flagCode: 'NA'),
//   Country(name: 'Nauru', code: '+674', flagCode: 'NR'),
//   Country(name: 'Nepal', code: '+977', flagCode: 'NP'),
//   Country(name: 'Netherlands', code: '+31', flagCode: 'NL'),
//   Country(name: 'New Zealand', code: '+64', flagCode: 'NZ'),
//   Country(name: 'Nicaragua', code: '+505', flagCode: 'NI'),
//   Country(name: 'Niger', code: '+227', flagCode: 'NE'),
//   Country(name: 'Nigeria', code: '+234', flagCode: 'NG'),
//   Country(name: 'North Korea', code: '+850', flagCode: 'KP'),
//   Country(name: 'North Macedonia (formerly Macedonia)', code: '+389', flagCode: 'MK'),
//   Country(name: 'Norway', code: '+47', flagCode: 'NO'),
//   Country(name: 'Oman', code: '+968', flagCode: 'OM'),
//   Country(name: 'Pakistan', code: '+92', flagCode: 'PK'),
//   Country(name: 'Palau', code: '+680', flagCode: 'PW'),
//   Country(name: 'Palestine State', code: '+970', flagCode: 'PS'),
//   Country(name: 'Panama', code: '+507', flagCode: 'PA'),
//   Country(name: 'Papua New Guinea', code: '+675', flagCode: 'PG'),
//   Country(name: 'Paraguay', code: '+595', flagCode: 'PY'),
//   Country(name: 'Peru', code: '+51', flagCode: 'PE'),
//   Country(name: 'Philippines', code: '+63', flagCode: 'PH'),
//   Country(name: 'Poland', code: '+48', flagCode: 'PL'),
//   Country(name: 'Portugal', code: '+351', flagCode: 'PT'),
//   Country(name: 'Qatar', code: '+974', flagCode: 'QA'),
//   Country(name: 'Romania', code: '+40', flagCode: 'RO'),
//   Country(name: 'Russia', code: '+7', flagCode: 'RU'),
//   Country(name: 'Rwanda', code: '+250', flagCode: 'RW'),
//   Country(name: 'Saint Kitts and Nevis', code: '+1-869', flagCode: 'KN'),
//   Country(name: 'Saint Lucia', code: '+1-758', flagCode: 'LC'),
//   Country(name: 'Saint Vincent and the Grenadines', code: '+1-784', flagCode: 'VC'),
//   Country(name: 'Samoa', code: '+685', flagCode: 'WS'),
//   Country(name: 'San Marino', code: '+378', flagCode: 'SM'),
//   Country(name: 'Sao Tome and Principe', code: '+239', flagCode: 'ST'),
//   Country(name: 'Saudi Arabia', code: '+966', flagCode: 'SA'),
//   Country(name: 'Senegal', code: '+221', flagCode: 'SN'),
//   Country(name: 'Serbia', code: '+381', flagCode: 'RS'),
//   Country(name: 'Seychelles', code: '+248', flagCode: 'SC'),
//   Country(name: 'Sierra Leone', code: '+232', flagCode: 'SL'),
//   Country(name: 'Singapore', code: '+65', flagCode: 'SG'),
//   Country(name: 'Slovakia', code: '+421', flagCode: 'SK'),
//   Country(name: 'Slovenia', code: '+386', flagCode: 'SI'),
//   Country(name: 'Solomon Islands', code: '+677', flagCode: 'SB'),
//   Country(name: 'Somalia', code: '+252', flagCode: 'SO'),
//   Country(name: 'South Africa', code: '+27', flagCode: 'ZA'),
//   Country(name: 'South Korea', code: '+82', flagCode: 'KR'),
//   Country(name: 'South Sudan', code: '+211', flagCode: 'SS'),
//   Country(name: 'Spain', code: '+34', flagCode: 'ES'),
//   Country(name: 'Sri Lanka', code: '+94', flagCode: 'LK'),
//   Country(name: 'Sudan', code: '+249', flagCode: 'SD'),
//   Country(name: 'Suriname', code: '+597', flagCode: 'SR'),
//   Country(name: 'Sweden', code: '+46', flagCode: 'SE'),
//   Country(name: 'Switzerland', code: '+41', flagCode: 'CH'),
//   Country(name: 'Syria', code: '+963', flagCode: 'SY'),
//   Country(name: 'Tajikistan', code: '+992', flagCode: 'TJ'),
//   Country(name: 'Tanzania', code: '+255', flagCode: 'TZ'),
//   Country(name: 'Thailand', code: '+66', flagCode: 'TH'),
//   Country(name: 'Timor-Leste', code: '+670', flagCode: 'TL'),
//   Country(name: 'Togo', code: '+228', flagCode: 'TG'),
//   Country(name: 'Tonga', code: '+676', flagCode: 'TO'),
//   Country(name: 'Trinidad and Tobago', code: '+1-868', flagCode: 'TT'),
//   Country(name: 'Tunisia', code: '+216', flagCode: 'TN'),
//   Country(name: 'Turkmenistan', code: '+993', flagCode: 'TM'),
//   Country(name: 'Tuvalu', code: '+688', flagCode: 'TV'),
//   Country(name: 'Uganda', code: '+256', flagCode: 'UG'),
//   Country(name: 'Ukraine', code: '+380', flagCode: 'UA'),
//   Country(name: 'United Arab Emirates', code: '+971', flagCode: 'AE'),
//   Country(name: 'United Kingdom', code: '+44', flagCode: 'GB'),
//   Country(name: 'United States of America', code: '+1', flagCode: 'US'),
//   Country(name: 'Uruguay', code: '+598', flagCode: 'UY'),
//   Country(name: 'Uzbekistan', code: '+998', flagCode: 'UZ'),
//   Country(name: 'Vanuatu', code: '+678', flagCode: 'VU'),
//   Country(name: 'Venezuela', code: '+58', flagCode: 'VE'),
//   Country(name: 'Vietnam', code: '+84', flagCode: 'VN'),
//   Country(name: 'Yemen', code: '+967', flagCode: 'YE'),
//   Country(name: 'Zambia', code: '+260', flagCode: 'ZM'),
//   Country(name: 'Zimbabwe', code: '+263', flagCode: 'ZW'),
//   // Add more countries here
// ];
