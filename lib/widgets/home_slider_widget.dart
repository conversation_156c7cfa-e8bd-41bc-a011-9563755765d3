import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../config/color.dart';
import '../config/font_config.dart';
import '../config/size_config.dart';
import '../controller/slider_controller.dart';

class HomeSliderWidget extends StatelessWidget {
  HomeSliderWidget({Key? key}) : super(key: key);

  final SliderController controller = Get.put(SliderController());
  final PageController pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const SizedBox(
          height: SizeConfig.kHeight140,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      if (controller.sliders.isEmpty) {
        return const SizedBox(
          height: SizeConfig.kHeight140,
          child: Center(
            child: Text(
              'Slider Görselleri Yüklenemedi',
              style: TextStyle(
                color: Colors.red,
                fontSize: FontConfig.kFontSize16,
              ),
            ),
          ),
        );
      }

      return Column(
        children: [
          SizedBox(
            height: SizeConfig.kHeight140,
            child: PageView.builder(
              controller: pageController,
              itemCount: controller.sliders.length,
              onPageChanged: controller.updateIndex,
              itemBuilder: (context, index) {
                final slider = controller.sliders[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: SizeConfig.kHeight8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(SizeConfig.borderRadius12),
                    child: CachedNetworkImage(
                      imageUrl: slider.imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                      errorWidget: (context, url, error) => Image.asset(
                        'assets/images/default_banner.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: SizeConfig.kHeight8),
          _buildIndicators(),
        ],
      );
    });
  }

  Widget _buildIndicators() {
    return Obx(() => Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        controller.sliders.length,
        (index) => Container(
          width: controller.currentIndex.value == index
              ? SizeConfig.kHeight16
              : SizeConfig.kHeight8,
          height: SizeConfig.kHeight8,
          margin: const EdgeInsets.symmetric(horizontal: 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SizeConfig.borderRadius20),
            color: controller.currentIndex.value == index
                ? ColorConfig.kPrimaryColor
                : ColorConfig.kButtonLightColor,
          ),
        ),
      ),
    ));
  }
}